"use client"

import { useState } from "react"
import { WhatsAppIcon } from "./whatsapp-icon"
import { useTranslation } from "./translation-provider"

export function WhatsAppFloat() {
  const [isHovered, setIsHovered] = useState(false)
  const { t, isRTL } = useTranslation()

  const handleWhatsAppClick = () => {
    const phoneNumber = "+9647867100886" // DRYLEX Iraq WhatsApp number
    const message = encodeURIComponent(t("whatsapp.defaultMessage"))
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${message}`
    window.open(whatsappUrl, "_blank")
  }

  return (
    <div className={`fixed bottom-6 z-50 ${isRTL ? "left-6" : "right-6"}`}>
      <button
        onClick={handleWhatsAppClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        className="group relative flex items-center bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        aria-label={t("whatsapp.contactUs")}
      >
        {/* WhatsApp Icon */}
        <div className="flex items-center justify-center w-14 h-14 rounded-full">
          <WhatsAppIcon className="w-7 h-7" />
        </div>

        {/* Expandable Text */}
        <div
          className={`overflow-hidden transition-all duration-300 ${
            isHovered ? "max-w-xs opacity-100" : "max-w-0 opacity-0"
          }`}
        >
          <span className={`whitespace-nowrap font-medium ${isRTL ? "pr-3 pl-4" : "pl-3 pr-4"}`}>
            {t("whatsapp.contactUs")}
          </span>
        </div>

        {/* Pulse Animation */}
        <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-20"></div>
      </button>

      {/* Tooltip */}
      <div
        className={`absolute bottom-full mb-2 ${isRTL ? "right-0" : "left-0"} transform transition-all duration-200 ${
          isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2 pointer-events-none"
        }`}
      >
        <div className="bg-gray-900 text-white text-sm rounded-lg px-3 py-2 whitespace-nowrap">
          {t("whatsapp.tooltip")}
          <div
            className={`absolute top-full ${isRTL ? "right-4" : "left-4"} w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900`}
          ></div>
        </div>
      </div>
    </div>
  )
}
