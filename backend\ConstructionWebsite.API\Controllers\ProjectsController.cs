using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ConstructionWebsite.API.Services;

using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProjectsController : ControllerBase
    {
        private readonly IProjectService _projectService;
        private readonly ILogger<ProjectsController> _logger;

        public ProjectsController(IProjectService projectService, ILogger<ProjectsController> logger)
        {
            _projectService = projectService;
            _logger = logger;
        }

        /// <summary>
        /// Get all projects with optional filtering and pagination
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResult<ProjectDto>>> GetProjects(
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] string? status = null,
            [FromQuery] bool? isFeatured = null,
            [FromQuery] string? language = "en",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 12,
            [FromQuery] string? sortBy = "createdAt",
            [FromQuery] string? sortOrder = "desc")
        {
            try
            {
                var result = await _projectService.GetProjectsAsync(
                    search, categoryId, status, isFeatured, language, 
                    page, pageSize, sortBy, sortOrder);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects");
                return StatusCode(500, new { message = "An error occurred while retrieving projects" });
            }
        }

        /// <summary>
        /// Get project by slug
        /// </summary>
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<ProjectDetailDto>> GetProjectBySlug(
            string slug, 
            [FromQuery] string language = "en")
        {
            try
            {
                var project = await _projectService.GetProjectBySlugAsync(slug, language);
                if (project == null)
                {
                    return NotFound(new { message = "Project not found" });
                }

                // Increment view count
                await _projectService.IncrementViewCountAsync(project.Id);

                return Ok(project);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project with slug: {Slug}", slug);
                return StatusCode(500, new { message = "An error occurred while retrieving the project" });
            }
        }

        /// <summary>
        /// Get project by ID
        /// </summary>
        [HttpGet("{id:int}")]
        public async Task<ActionResult<ProjectDetailDto>> GetProject(
            int id, 
            [FromQuery] string language = "en")
        {
            try
            {
                var project = await _projectService.GetProjectByIdAsync(id, language);
                if (project == null)
                {
                    return NotFound(new { message = "Project not found" });
                }

                return Ok(project);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the project" });
            }
        }

        /// <summary>
        /// Get related projects
        /// </summary>
        [HttpGet("{id:int}/related")]
        public async Task<ActionResult<List<ProjectDto>>> GetRelatedProjects(
            int id,
            [FromQuery] string language = "en",
            [FromQuery] int limit = 3)
        {
            try
            {
                var relatedProjects = await _projectService.GetRelatedProjectsAsync(id, language, limit);
                return Ok(relatedProjects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related projects for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving related projects" });
            }
        }

        /// <summary>
        /// Create project inquiry
        /// </summary>
        [HttpPost("{id:int}/inquiries")]
        public async Task<ActionResult<InquiryResponseDto>> CreateProjectInquiry(
            int id, 
            [FromBody] CreateProjectInquiryDto inquiryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var result = await _projectService.CreateInquiryAsync(
                    id, inquiryDto, clientIp, userAgent);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project inquiry for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while creating the inquiry" });
            }
        }

        /// <summary>
        /// Get project categories
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<List<CategoryDto>>> GetCategories(
            [FromQuery] string language = "en")
        {
            try
            {
                var categories = await _projectService.GetCategoriesAsync(language);
                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project categories");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        /// <summary>
        /// Create a new project (Admin only)
        /// </summary>
        [HttpPost]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ProjectDto>> CreateProject([FromBody] CreateProjectDto projectDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _projectService.CreateProjectAsync(projectDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return CreatedAtAction(nameof(GetProject), new { id = result.Data.Id }, result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project");
                return StatusCode(500, new { message = "An error occurred while creating the project" });
            }
        }

        /// <summary>
        /// Update a project (Admin only)
        /// </summary>
        [HttpPut("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ProjectDto>> UpdateProject(int id, [FromBody] UpdateProjectDto projectDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _projectService.UpdateProjectAsync(id, projectDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the project" });
            }
        }

        /// <summary>
        /// Delete a project (Admin only)
        /// </summary>
        [HttpDelete("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult> DeleteProject(int id)
        {
            try
            {
                var result = await _projectService.DeleteProjectAsync(id);
                if (!result)
                {
                    return NotFound(new { message = "Project not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the project" });
            }
        }
    }
}
