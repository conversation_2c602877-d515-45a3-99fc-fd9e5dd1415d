import { NextRequest, NextResponse } from "next/server"
import { zeroRiskAuth } from "@/utils/advanced-auth"
import { zeroRiskValidator } from "@/utils/zero-risk-validation"
import { validateAPIRequest } from "@/middleware/zero-risk-security"
import jwt from "jsonwebtoken"

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-this-in-production"

export async function POST(request: NextRequest) {
  try {
    // Zero-risk API validation
    if (!validateAPIRequest(request)) {
      return NextResponse.json(
        { message: "Request blocked by security policy" },
        { status: 403 }
      )
    }

    const { sessionId, mfaCode } = await request.json()

    // Validate MFA code
    const mfaValidation = zeroRiskValidator.validateInput(mfaCode, 'text', 10)
    if (!mfaValidation.isValid) {
      return NextResponse.json(
        { message: "Invalid MFA code format" },
        { status: 400 }
      )
    }

    const clientIP = request.headers.get("x-forwarded-for") || 
                    request.headers.get("x-real-ip") || 
                    "unknown"
    const userAgent = request.headers.get("user-agent") || "unknown"

    // Verify MFA
    const isValidMFA = zeroRiskAuth.verifyMFA(sessionId, mfaValidation.sanitizedValue)
    
    if (!isValidMFA) {
      return NextResponse.json(
        { message: "Invalid MFA code" },
        { status: 401 }
      )
    }

    // Validate session
    const session = zeroRiskAuth.validateSession(sessionId, clientIP, userAgent)
    if (!session || !session.mfaVerified) {
      return NextResponse.json(
        { message: "Invalid or expired session" },
        { status: 401 }
      )
    }

    // Generate JWT token for zero-risk session
    const token = jwt.sign(
      { 
        userId: session.userId,
        username: session.username,
        role: "admin",
        sessionId: session.sessionId,
        iat: Math.floor(Date.now() / 1000)
      },
      JWT_SECRET,
      { expiresIn: "15m" } // 15 minutes for zero-risk
    )

    const expiresAt = new Date(Date.now() + 15 * 60 * 1000) // 15 minutes

    return NextResponse.json({
      success: true,
      token,
      expiresAt: expiresAt.toISOString(),
      message: "MFA verification successful"
    })

  } catch (error) {
    console.error("MFA verification error:", error)
    return NextResponse.json(
      { message: "MFA verification failed" },
      { status: 500 }
    )
  }
}
