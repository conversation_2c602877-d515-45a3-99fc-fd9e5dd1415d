using ConstructionWebsite.API.Data;

using ConstructionWebsite.API.DTOs;
using ConstructionWebsite.API.Models;
using Microsoft.EntityFrameworkCore;

namespace ConstructionWebsite.API.Services
{
    public class ProjectService : IProjectService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ProjectService> _logger;

        public ProjectService(ApplicationDbContext context, ILogger<ProjectService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Project>> GetAllProjectsAsync()
        {
            try
            {
                return await _context.Projects
                    .Where(p => !p.IsDeleted)
                    .OrderByDescending(p => p.CreatedAt)
                    .ToListAsync();
            }

         catch (Exception ex)
         {
                _logger.LogError(ex, "Error retrieving all projects");
                throw;
            }
        }

        public async Task<Project?> GetProjectByIdAsync(int id)
        {
            try
            {
                return await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project with ID {ProjectId}", id);
                throw;
            }
        }

        public async Task<Project> CreateProjectAsync(Project project)
        {
            try
            {
                project.CreatedAt = DateTime.UtcNow;
                project.UpdatedAt = DateTime.UtcNow;
                
                _context.Projects.Add(project);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Project created with ID {ProjectId}", project.Id);
                return project;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project");
                throw;
            }
        }

        public async Task<Project?> UpdateProjectAsync(int id, Project project)
        {
            try
            {
                var existingProject = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
                
                if (existingProject == null)
                    return null;

                existingProject.Name = project.Name;
                existingProject.Description = project.Description;
                existingProject.ProjectType = project.ProjectType;
                existingProject.ProjectStatus = project.ProjectStatus;
                existingProject.Location = project.Location;
                existingProject.StartDate = project.StartDate;
                existingProject.EndDate = project.EndDate;
                existingProject.ProjectValue = project.ProjectValue;
                existingProject.ClientName = project.ClientName;
                existingProject.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Project updated with ID {ProjectId}", id);
                return existingProject;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project with ID {ProjectId}", id);
                throw;
            }
        }

        public async Task<ServiceResult<bool>> DeleteProjectAsync(int id, bool isAdmin = true)
        {
            try
            {
                var project = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);
                
                if (project == null)
                    return ServiceResult<bool>.ErrorResult("Project not found");

                if (!isAdmin)
                {
                    // Soft delete for non-admin users
                    project.IsDeleted = true;
                    project.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    // Hard delete for admin users
                    _context.Projects.Remove(project);
                }
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Project deleted with ID {ProjectId}", id);
                return ServiceResult<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting project with ID {ProjectId}", id);
                return ServiceResult<bool>.ErrorResult($"Error deleting project: {ex.Message}");
            }
        }

        public async Task<IEnumerable<Project>> GetProjectsByStatusAsync(string status)
        {
            try
            {
                return await _context.Projects
                    .Where(p => !p.IsDeleted && p.ProjectStatus.ToLower() == status.ToLower())
                    .OrderByDescending(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects by status {Status}", status);
                throw;
            }
        }

        public async Task<IEnumerable<Project>> GetProjectsByTypeAsync(string type)
        {
            try
            {
                return await _context.Projects
                    .Where(p => !p.IsDeleted && p.ProjectType.ToLower() == type.ToLower())
                    .OrderByDescending(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects by type {Type}", type);
                throw;
            }
        }

        public async Task<IEnumerable<Project>> SearchProjectsAsync(string searchTerm)
        {
            try
            {
                return await _context.Projects
                    .Where(p => !p.IsDeleted && 
                        (p.Name.Contains(searchTerm) || 
                         p.Description.Contains(searchTerm) ||
                         p.ProjectType.Contains(searchTerm) ||
                         p.Location.Contains(searchTerm) ||
                         p.ClientName.Contains(searchTerm)))
                    .OrderByDescending(p => p.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching projects with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<bool> ProjectExistsAsync(int id)
        {
            try
            {
                return await _context.Projects
                    .AnyAsync(p => p.Id == id && !p.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if project exists with ID {ProjectId}", id);
                throw;
            }
        }

        public async Task<PagedResult<ProjectDto>> GetProjectsAsync(string? search = null, int? categoryId = null, string? status = null, bool? isFeatured = null, string? language = "en", int page = 1, int pageSize = 12, string? sortBy = "createdAt", string? sortOrder = "desc")
        {
            try
            {
                var query = _context.Projects
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .Where(p => !p.IsDeleted);

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(p => p.Name.Contains(search) || p.Description.Contains(search));
                }

                if (categoryId.HasValue)
                {
                    query = query.Where(p => p.CategoryId == categoryId.Value);
                }

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(p => p.ProjectStatus == status);
                }

                if (isFeatured.HasValue)
                {
                    query = query.Where(p => p.IsFeatured == isFeatured.Value);
                }

                // Apply sorting
                query = sortBy?.ToLower() switch
                {
                    "name" => sortOrder == "desc" ? query.OrderByDescending(p => p.Name) : query.OrderBy(p => p.Name),
                    "createdat" => sortOrder == "desc" ? query.OrderByDescending(p => p.CreatedAt) : query.OrderBy(p => p.CreatedAt),
                    "viewcount" => sortOrder == "desc" ? query.OrderByDescending(p => p.ViewCount) : query.OrderBy(p => p.ViewCount),
                    _ => query.OrderByDescending(p => p.CreatedAt)
                };

                var totalCount = await query.CountAsync();
                var projects = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var projectDtos = projects.Select(p => MapToProjectDto(p, language)).ToList();
                return PagedResult<ProjectDto>.Create(projectDtos, totalCount, page, pageSize);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving projects");
                throw;
            }
        }

        public async Task<ProjectDetailDto?> GetProjectBySlugAsync(string slug, string language = "en")
        {
            try
            {
                var project = await _context.Projects
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .FirstOrDefaultAsync(p => p.Slug == slug && !p.IsDeleted);

                return project != null ? MapToProjectDetailDto(project, language) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project by slug {Slug}", slug);
                throw;
            }
        }

        public async Task<ProjectDetailDto?> GetProjectByIdAsync(int id, string language = "en")
        {
            try
            {
                var project = await _context.Projects
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                return project != null ? MapToProjectDetailDto(project, language) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving project detail by ID {ProjectId}", id);
                throw;
            }
        }

        public async Task<List<ProjectDto>> GetRelatedProjectsAsync(int id, string language = "en", int limit = 3)
        {
            try
            {
                var currentProject = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (currentProject == null) return new List<ProjectDto>();

                var relatedProjects = await _context.Projects
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .Where(p => p.Id != id && !p.IsDeleted && 
                               (p.CategoryId == currentProject.CategoryId || p.ProjectType == currentProject.ProjectType))
                    .OrderByDescending(p => p.ViewCount)
                    .Take(limit)
                    .ToListAsync();

                return relatedProjects.Select(p => MapToProjectDto(p, language)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related projects for ID {ProjectId}", id);
                throw;
            }
        }

        public async Task IncrementViewCountAsync(int id)
        {
            try
            {
                var project = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (project != null)
                {
                    project.ViewCount++;
                    project.UpdatedAt = DateTime.UtcNow;
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for project ID {ProjectId}", id);
                throw;
            }
        }

        public async Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(int id, CreateProjectInquiryDto inquiryDto, string? clientIp, string? userAgent)
        {
            try
            {
                var project = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (project == null)
                {
                    return ServiceResult<InquiryResponseDto>.ErrorResult("Project not found", 404);
                }

                var inquiry = new ProjectInquiry
                 {
                     ProjectId = id,
                     Name = inquiryDto.Name,
                     Email = inquiryDto.Email,
                     Phone = inquiryDto.Phone,
                     Company = inquiryDto.Company,
                     Subject = "Project Inquiry",
                     Message = inquiryDto.Message,
                     InquiryType = inquiryDto.InquiryType ?? "general",
                     BudgetRange = inquiryDto.Budget,
                     Timeline = inquiryDto.Timeline,
                     Location = inquiryDto.Location,
                     IpAddress = clientIp,
                     UserAgent = userAgent,
                     CreatedAt = DateTime.UtcNow
                 };

                 _context.ProjectInquiries.Add(inquiry);
                project.InquiryCount++;
                await _context.SaveChangesAsync();

                var response = new InquiryResponseDto
                {
                    Id = inquiry.Id,
                    Message = "Your inquiry has been submitted successfully. We will contact you soon."
                };

                return ServiceResult<InquiryResponseDto>.SuccessResult(response, "Inquiry submitted successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating inquiry for project ID {ProjectId}", id);
                return ServiceResult<InquiryResponseDto>.ErrorResult("Failed to submit inquiry");
            }
        }

        public async Task<List<CategoryDto>> GetCategoriesAsync(string language = "en")
        {
            try
            {
                var categories = await _context.ProjectCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.SortOrder)
                    .ThenBy(c => c.Name)
                    .Select(c => new CategoryDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        Slug = c.Slug,
                        Description = c.Description,
                        IsActive = c.IsActive,
                        SortOrder = c.SortOrder
                    })
                    .ToListAsync();

                return categories.ToList();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error retrieving categories: {ex.Message}");
            }
        }

        public async Task<ServiceResult<ProjectDto>> CreateProjectAsync(CreateProjectDto projectDto)
        {
            try
            {
                var project = new Project
                {
                    Name = projectDto.Title,
                    NameAr = projectDto.TitleAr,
                    Slug = projectDto.Slug,
                    ShortDescription = projectDto.ShortDescription,
                    ShortDescriptionAr = projectDto.ShortDescriptionAr,
                    Description = projectDto.Description,
                    DescriptionAr = projectDto.DescriptionAr,
                    ClientName = projectDto.Client,
                    Location = projectDto.Location,
                    LocationAr = projectDto.LocationAr,
                    ProjectType = projectDto.ProjectType,
                    ProjectStatus = projectDto.Status,
                    ProjectValue = projectDto.Budget,
                    Currency = projectDto.Currency,
                    StartDate = projectDto.StartDate,
                    EndDate = projectDto.EndDate,
                    CompletionDate = projectDto.CompletionDate,
                    IsFeatured = projectDto.IsFeatured,
                    SortOrder = projectDto.SortOrder,
                    SeoTitle = projectDto.SeoTitle,
                    SeoDescription = projectDto.SeoDescription,
                    SeoKeywords = projectDto.SeoKeywords,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Projects.Add(project);
                await _context.SaveChangesAsync();

                var result = MapToProjectDto(project, "en");
                return ServiceResult<ProjectDto>.SuccessResult(result, "Project created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating project");
                return ServiceResult<ProjectDto>.ErrorResult("Failed to create project");
            }
        }

        public async Task<ServiceResult<ProjectDto>> UpdateProjectAsync(int id, UpdateProjectDto projectDto)
        {
            try
            {
                var project = await _context.Projects
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (project == null)
                {
                    return ServiceResult<ProjectDto>.ErrorResult("Project not found", 404);
                }

                if (!string.IsNullOrEmpty(projectDto.Title)) project.Name = projectDto.Title;
                if (projectDto.TitleAr != null) project.NameAr = projectDto.TitleAr;
                if (!string.IsNullOrEmpty(projectDto.Slug)) project.Slug = projectDto.Slug;
                if (projectDto.ShortDescription != null) project.ShortDescription = projectDto.ShortDescription;
                if (projectDto.ShortDescriptionAr != null) project.ShortDescriptionAr = projectDto.ShortDescriptionAr;
                if (projectDto.Description != null) project.Description = projectDto.Description;
                if (projectDto.DescriptionAr != null) project.DescriptionAr = projectDto.DescriptionAr;
                if (projectDto.Client != null) project.ClientName = projectDto.Client;
                if (projectDto.Location != null) project.Location = projectDto.Location;
                if (projectDto.LocationAr != null) project.LocationAr = projectDto.LocationAr;
                if (!string.IsNullOrEmpty(projectDto.ProjectType)) project.ProjectType = projectDto.ProjectType;
                if (!string.IsNullOrEmpty(projectDto.Status)) project.ProjectStatus = projectDto.Status;
                if (projectDto.Budget.HasValue) project.ProjectValue = projectDto.Budget;
                if (!string.IsNullOrEmpty(projectDto.Currency)) project.Currency = projectDto.Currency;
                if (projectDto.StartDate.HasValue) project.StartDate = projectDto.StartDate;
                if (projectDto.EndDate.HasValue) project.EndDate = projectDto.EndDate;
                if (projectDto.CompletionDate.HasValue) project.CompletionDate = projectDto.CompletionDate;
                if (projectDto.IsFeatured.HasValue) project.IsFeatured = projectDto.IsFeatured.Value;
                if (projectDto.SortOrder.HasValue) project.SortOrder = projectDto.SortOrder.Value;
                if (projectDto.SeoTitle != null) project.SeoTitle = projectDto.SeoTitle;
                if (projectDto.SeoDescription != null) project.SeoDescription = projectDto.SeoDescription;
                if (projectDto.SeoKeywords != null) project.SeoKeywords = projectDto.SeoKeywords;

                project.UpdatedAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();

                var result = MapToProjectDto(project, "en");
                return ServiceResult<ProjectDto>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating project with ID {ProjectId}", id);
                return ServiceResult<ProjectDto>.ErrorResult("Failed to update project");
            }
        }



        private ProjectDto MapToProjectDto(Project project, string language)
        {
            return new ProjectDto
            {
                Id = project.Id,
                Title = language == "ar" && !string.IsNullOrEmpty(project.NameAr) ? project.NameAr : project.Name,
                TitleAr = project.NameAr,
                Slug = project.Slug,
                ShortDescription = language == "ar" && !string.IsNullOrEmpty(project.ShortDescriptionAr) ? project.ShortDescriptionAr : project.ShortDescription,
                ShortDescriptionAr = project.ShortDescriptionAr,
                Description = language == "ar" && !string.IsNullOrEmpty(project.DescriptionAr) ? project.DescriptionAr : project.Description,
                DescriptionAr = project.DescriptionAr,
                Client = project.ClientName,
                Location = language == "ar" && !string.IsNullOrEmpty(project.LocationAr) ? project.LocationAr : project.Location,
                LocationAr = project.LocationAr,
                ProjectType = project.ProjectType,
                Status = project.ProjectStatus,
                Budget = project.ProjectValue,
                Currency = project.Currency,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                CompletionDate = project.CompletionDate,
                IsFeatured = project.IsFeatured,
                SortOrder = project.SortOrder,
                ViewCount = project.ViewCount,
                PrimaryImage = project.Images?.FirstOrDefault(i => i.IsPrimary) != null ? new ImageDto
                {
                    Id = project.Images.First(i => i.IsPrimary).Id,
                    FileUrl = project.Images.First(i => i.IsPrimary).ImageUrl,
                    AltText = project.Images.First(i => i.IsPrimary).AltText,
                    Caption = project.Images.First(i => i.IsPrimary).Caption
                } : null,
                SeoTitle = project.SeoTitle,
                SeoDescription = project.SeoDescription,
                SeoKeywords = project.SeoKeywords,
                CreatedAt = project.CreatedAt,
                UpdatedAt = project.UpdatedAt
            };
        }

        private ProjectDetailDto MapToProjectDetailDto(Project project, string language)
        {
            var dto = new ProjectDetailDto
            {
                Id = project.Id,
                Title = language == "ar" && !string.IsNullOrEmpty(project.NameAr) ? project.NameAr : project.Name,
                TitleAr = project.NameAr,
                Slug = project.Slug,
                ShortDescription = language == "ar" && !string.IsNullOrEmpty(project.ShortDescriptionAr) ? project.ShortDescriptionAr : project.ShortDescription,
                ShortDescriptionAr = project.ShortDescriptionAr,
                Description = language == "ar" && !string.IsNullOrEmpty(project.DescriptionAr) ? project.DescriptionAr : project.Description,
                DescriptionAr = project.DescriptionAr,
                Client = project.ClientName,
                Location = language == "ar" && !string.IsNullOrEmpty(project.LocationAr) ? project.LocationAr : project.Location,
                LocationAr = project.LocationAr,
                ProjectType = project.ProjectType,
                Status = project.ProjectStatus,
                Budget = project.ProjectValue,
                Currency = project.Currency,
                StartDate = project.StartDate,
                EndDate = project.EndDate,
                CompletionDate = project.CompletionDate,
                IsFeatured = project.IsFeatured,
                SortOrder = project.SortOrder,
                ViewCount = project.ViewCount,
                ClientTestimonial = language == "ar" && !string.IsNullOrEmpty(project.ClientTestimonialAr) ? project.ClientTestimonialAr : project.ClientTestimonial,
                ClientTestimonialAr = project.ClientTestimonialAr,
                VideoUrl = project.VideoUrl,
                VirtualTourUrl = project.VirtualTourUrl,
                CaseStudyUrl = project.CaseStudyPdf,
                SeoTitle = project.SeoTitle,
                SeoDescription = project.SeoDescription,
                SeoKeywords = project.SeoKeywords,
                CreatedAt = project.CreatedAt,
                UpdatedAt = project.UpdatedAt,
                Images = project.Images?.Select(i => new ImageDto
                {
                    Id = i.Id,
                    FileUrl = i.ImageUrl,
                    AltText = i.AltText,
                    Caption = i.Caption,
                    IsPrimary = i.IsPrimary
                }).ToList() ?? new List<ImageDto>()
            };

            // Parse JSON fields if they exist
            if (!string.IsNullOrEmpty(project.ChallengesFaced))
            {
                try
                {
                    dto.Challenges = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.ChallengesFaced) ?? new List<string>();
                }
                catch
                {
                    dto.Challenges = new List<string> { project.ChallengesFaced };
                }
            }

            if (!string.IsNullOrEmpty(project.SolutionsImplemented))
            {
                try
                {
                    dto.Solutions = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.SolutionsImplemented) ?? new List<string>();
                }
                catch
                {
                    dto.Solutions = new List<string> { project.SolutionsImplemented };
                }
            }

            if (!string.IsNullOrEmpty(project.TechnologiesUsed))
            {
                try
                {
                    dto.Technologies = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.TechnologiesUsed) ?? new List<string>();
                }
                catch
                {
                    dto.Technologies = new List<string> { project.TechnologiesUsed };
                }
            }

            if (!string.IsNullOrEmpty(project.MaterialsUsed))
            {
                try
                {
                    dto.Materials = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.MaterialsUsed) ?? new List<string>();
                }
                catch
                {
                    dto.Materials = new List<string> { project.MaterialsUsed };
                }
            }

            if (!string.IsNullOrEmpty(project.AwardsReceived))
            {
                try
                {
                    dto.Awards = System.Text.Json.JsonSerializer.Deserialize<List<string>>(project.AwardsReceived) ?? new List<string>();
                }
                catch
                {
                    dto.Awards = new List<string> { project.AwardsReceived };
                }
            }

            return dto;
        }

        public async Task<bool> DeleteProjectAsync(int id)
        {
            try
            {
                var project = await _context.Projects.FindAsync(id);
                if (project == null)
                {
                    return false;
                }

                _context.Projects.Remove(project);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}