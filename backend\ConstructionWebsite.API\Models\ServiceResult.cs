namespace ConstructionWebsite.API.Models
{
    public class ServiceResult<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public int? StatusCode { get; set; }

        public ServiceResult()
        {
        }

        public ServiceResult(T data)
        {
            Success = true;
            Data = data;
        }

        public ServiceResult(string message, bool success = false)
        {
            Success = success;
            Message = message;
        }

        public ServiceResult(List<string> errors)
        {
            Success = false;
            Errors = errors;
        }

        public static ServiceResult<T> SuccessResult(T data)
        {
            return new ServiceResult<T>(data);
        }

        public static ServiceResult<T> SuccessResult(T data, string message)
        {
            return new ServiceResult<T>
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        public static ServiceResult<T> ErrorResult(string message)
        {
            return new ServiceResult<T>(message, false);
        }

        public static ServiceResult<T> ErrorResult(List<string> errors)
        {
            return new ServiceResult<T>(errors);
        }

        public static ServiceResult<T> ErrorResult(string message, int statusCode)
        {
            return new ServiceResult<T>
            {
                Success = false,
                Message = message,
                StatusCode = statusCode
            };
        }
    }

    public class ServiceResult : ServiceResult<object>
    {
        public ServiceResult() : base()
        {
        }

        public ServiceResult(string message, bool success = false) : base(message, success)
        {
        }

        public ServiceResult(List<string> errors) : base(errors)
        {
        }

        public static ServiceResult SuccessResult()
        {
            return new ServiceResult { Success = true };
        }

        public static ServiceResult SuccessResult(string message)
        {
            return new ServiceResult(message, true);
        }

        public static new ServiceResult ErrorResult(string message)
        {
            return new ServiceResult(message, false);
        }

        public static new ServiceResult ErrorResult(List<string> errors)
        {
            return new ServiceResult(errors);
        }

        public static new ServiceResult ErrorResult(string message, int statusCode)
        {
            return new ServiceResult
            {
                Success = false,
                Message = message,
                StatusCode = statusCode
            };
        }
    }
}