namespace ConstructionWebsite.API.DTOs
{
    public class ContactDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? InquiryType { get; set; }
        public string? PreferredContactMethod { get; set; }
        public string? PreferredContactTime { get; set; }
        public bool IsRead { get; set; }
        public bool IsReplied { get; set; }
        public string? Reply { get; set; }
        public DateTime? RepliedAt { get; set; }
        public string? RepliedBy { get; set; }
        public string? Notes { get; set; }
        public string? Source { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateContactDto
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string Subject { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string? InquiryType { get; set; }
        public string? PreferredContactMethod { get; set; }
        public string? PreferredContactTime { get; set; }
        public string? Source { get; set; }
    }

    public class UpdateContactDto
    {
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string? Subject { get; set; }
        public string? Message { get; set; }
        public string? InquiryType { get; set; }
        public string? PreferredContactMethod { get; set; }
        public string? PreferredContactTime { get; set; }
        public bool? IsRead { get; set; }
        public bool? IsReplied { get; set; }
        public string? Reply { get; set; }
        public string? Notes { get; set; }
        public string? Source { get; set; }
    }

    public class ContactReplyDto
    {
        public string Reply { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }
}