"use client"

import { useState, useEffect } from "react"
import { notFound } from "next/navigation"
import { ProjectDetailClient } from "./project-detail-client"
import { enhancedDb, type Project } from "@/lib/database-enhanced"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import { useTranslation } from "@/components/translation-provider"

interface ProjectDetailPageProps {
  params: { slug: string }
  searchParams?: { [key: string]: string | string[] | undefined }
}

function ProjectDetailLoading() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="animate-pulse space-y-8">
          <div className="h-6 bg-gray-300 rounded w-1/3"></div>
          <div className="h-64 bg-gray-300 rounded"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded w-3/4"></div>
            <div className="h-20 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const { t, language } = useTranslation()
  const [project, setProject] = useState<Project | null>(null)
  const [relatedProjects, setRelatedProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.slug) {
      loadProject(params.slug)
    }
  }, [params.slug, language])

  const loadProject = async (slug: string) => {
    try {
      setLoading(true)
      const projectData = await enhancedDb.getProjectBySlug(slug)

      if (!projectData) {
        notFound()
        return
      }

      setProject(projectData)

      // Increment view count
      enhancedDb.incrementProjectViews(projectData.id).catch(console.error)

      // Load related projects
      const related = await enhancedDb.getRelatedProjects(projectData.id, projectData.category_id, 3)
      setRelatedProjects(related)
    } catch (error) {
      console.error("Failed to load project:", error)
      notFound()
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <ProjectDetailLoading />
  }

  if (!project) {
    notFound()
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <SEOBreadcrumbs
          items={[
            { label: t("nav.projects", "Projects"), href: "/projects" },
            { label: language === "ar" ? project.name_ar || project.name : project.name },
          ]}
        />

        <ProjectDetailClient project={project} relatedProjects={relatedProjects} />
      </div>
    </div>
  )
}
