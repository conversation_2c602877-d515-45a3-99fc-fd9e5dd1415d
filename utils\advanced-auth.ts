// Advanced authentication utilities for zero-risk security

import crypto from 'crypto'

export interface MFASession {
  userId: string
  username: string
  mfaVerified: boolean
  sessionId: string
  expiresAt: number
  lastActivity: number
  ipAddress: string
  userAgent: string
}

export interface SecurityEvent {
  type: 'login_attempt' | 'mfa_attempt' | 'suspicious_activity' | 'rate_limit_exceeded'
  userId?: string
  ipAddress: string
  userAgent: string
  timestamp: number
  details: any
}

class ZeroRiskAuth {
  private sessions = new Map<string, MFASession>()
  private securityEvents: SecurityEvent[] = []
  private maxSessions = 3
  private sessionTimeout = 15 * 60 * 1000 // 15 minutes
  private maxFailedAttempts = 3
  private lockoutDuration = 30 * 60 * 1000 // 30 minutes

  // Generate cryptographically secure tokens
  generateSecureToken(length: number = 64): string {
    return crypto.randomBytes(length).toString('hex')
  }

  // Generate MFA code
  generateMFACode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }

  // Hash passwords with salt
  async hashPassword(password: string): Promise<string> {
    const salt = crypto.randomBytes(32).toString('hex')
    const hash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha512').toString('hex')
    return `${salt}:${hash}`
  }

  // Verify password
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    const [salt, hash] = hashedPassword.split(':')
    const testHash = crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha512').toString('hex')
    return hash === testHash
  }

  // Create secure session
  createSession(userId: string, username: string, ipAddress: string, userAgent: string): MFASession {
    const sessionId = this.generateSecureToken()
    const now = Date.now()
    
    const session: MFASession = {
      userId,
      username,
      mfaVerified: false,
      sessionId,
      expiresAt: now + this.sessionTimeout,
      lastActivity: now,
      ipAddress,
      userAgent
    }

    this.sessions.set(sessionId, session)
    this.logSecurityEvent('login_attempt', userId, ipAddress, userAgent, { sessionId })
    
    return session
  }

  // Verify MFA
  verifyMFA(sessionId: string, mfaCode: string): boolean {
    const session = this.sessions.get(sessionId)
    if (!session) return false

    // In production, verify against stored MFA code
    // For now, accept any 6-digit code
    if (mfaCode.length === 6 && /^\d+$/.test(mfaCode)) {
      session.mfaVerified = true
      session.lastActivity = Date.now()
      this.sessions.set(sessionId, session)
      return true
    }

    this.logSecurityEvent('mfa_attempt', session.userId, session.ipAddress, session.userAgent, { 
      sessionId, 
      success: false 
    })
    return false
  }

  // Validate session
  validateSession(sessionId: string, ipAddress: string, userAgent: string): MFASession | null {
    const session = this.sessions.get(sessionId)
    if (!session) return null

    const now = Date.now()
    
    // Check if session expired
    if (now > session.expiresAt) {
      this.sessions.delete(sessionId)
      return null
    }

    // Check for session hijacking
    if (session.ipAddress !== ipAddress || session.userAgent !== userAgent) {
      this.logSecurityEvent('suspicious_activity', session.userId, ipAddress, userAgent, {
        type: 'session_hijacking_attempt',
        originalIp: session.ipAddress,
        originalUserAgent: session.userAgent
      })
      this.sessions.delete(sessionId)
      return null
    }

    // Update last activity
    session.lastActivity = now
    this.sessions.set(sessionId, session)
    
    return session
  }

  // Clean expired sessions
  cleanExpiredSessions(): void {
    const now = Date.now()
    for (const [sessionId, session] of this.sessions.entries()) {
      if (now > session.expiresAt) {
        this.sessions.delete(sessionId)
      }
    }
  }

  // Log security events
  private logSecurityEvent(
    type: SecurityEvent['type'],
    userId: string | undefined,
    ipAddress: string,
    userAgent: string,
    details: any
  ): void {
    const event: SecurityEvent = {
      type,
      userId,
      ipAddress,
      userAgent,
      timestamp: Date.now(),
      details
    }
    
    this.securityEvents.push(event)
    
    // Keep only last 1000 events
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000)
    }
  }

  // Get security events
  getSecurityEvents(limit: number = 100): SecurityEvent[] {
    return this.securityEvents.slice(-limit)
  }

  // Check for suspicious activity
  checkSuspiciousActivity(ipAddress: string, userId?: string): boolean {
    const recentEvents = this.securityEvents.filter(
      event => event.timestamp > Date.now() - 60 * 60 * 1000 // Last hour
    )

    const failedAttempts = recentEvents.filter(
      event => event.type === 'login_attempt' && event.ipAddress === ipAddress
    ).length

    return failedAttempts > this.maxFailedAttempts
  }
}

export const zeroRiskAuth = new ZeroRiskAuth()
