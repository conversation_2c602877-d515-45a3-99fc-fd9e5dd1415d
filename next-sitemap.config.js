module.exports = {
  siteUrl: 'https://drylexiraq.com',
  generateRobotsTxt: true,
  sitemapSize: 5000,
  changefreq: 'daily',
  priority: 0.7,
  exclude: ['/admin', '/admin/*', '/api/*', '/_next/*', '/static/*', '/favicon.ico', '/robots.txt', '/sitemap.xml'],
  transform: async (config, path) => {
    if (path === '/') {
      return {
        loc: path,
        changefreq: 'daily',
        priority: 1.0,
        lastmod: new Date().toISOString(),
        alternateRefs: [
          {
            href: 'https://drylexiraq.com/en',
            hreflang: 'en',
          },
        ],
      };
    }
    
    if (path.startsWith('/products')) {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: new Date().toISOString(),
      };
    }
    
    if (path.startsWith('/services')) {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.75,
        lastmod: new Date().toISOString(),
      };
    }
    
    if (path.startsWith('/projects')) {
      return {
        loc: path,
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: new Date().toISOString(),
      };
    }
    
    if (path.startsWith('/about')) {
      return {
        loc: path,
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: new Date().toISOString(),
      };
    }
    
    if (path.startsWith('/contact')) {
      return {
        loc: path,
        changefreq: 'monthly',
        priority: 0.6,
        lastmod: new Date().toISOString(),
      };
    }
    
    if (path.startsWith('/drylex')) {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 0.85,
        lastmod: new Date().toISOString(),
      };
    }
    
    return {
      loc: path,
      changefreq: config.changefreq,
      priority: config.priority,
      lastmod: new Date().toISOString(),
    };
  },
  additionalPaths: async (config) => {
    const paths = [];
    
    // Add dynamic product pages
    const products = await db.getProducts();
    products.forEach(product => {
      paths.push({
        loc: `/products/${product.slug}`,
        changefreq: 'weekly',
        priority: 0.8,
        lastmod: product.updatedAt ? new Date(product.updatedAt).toISOString() : new Date().toISOString(),
      });
    });
    
    // Add dynamic service pages
    const services = await db.getServices();
    services.forEach(service => {
      paths.push({
        loc: `/services/${service.slug}`,
        changefreq: 'weekly',
        priority: 0.75,
        lastmod: service.updatedAt ? new Date(service.updatedAt).toISOString() : new Date().toISOString(),
      });
    });
    
    // Add dynamic project pages
    const projects = await db.getProjects();
    projects.forEach(project => {
      paths.push({
        loc: `/projects/${project.slug}`,
        changefreq: 'monthly',
        priority: 0.7,
        lastmod: project.updatedAt ? new Date(project.updatedAt).toISOString() : new Date().toISOString(),
      });
    });
    
    return paths;
  },
  alternateRefs: [
    {
      href: 'https://drylexiraq.com/en',
      hreflang: 'en',
    },
  ],
  outDir: './public',
};