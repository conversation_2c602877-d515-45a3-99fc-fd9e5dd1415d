namespace ConstructionWebsite.API.DTOs
{
    public class CreateServiceInquiryDto
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? InquiryType { get; set; }
        public string? PreferredContactMethod { get; set; }
        public string? PreferredContactTime { get; set; }
        public string? ProjectType { get; set; }
        public string? ProjectDetails { get; set; }
        public DateTime? RequiredDate { get; set; }
        public string? Budget { get; set; }
        public string? Timeline { get; set; }
        public string? Location { get; set; }
        public string? Source { get; set; }
    }

    public class ServiceInquiryResponseDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Company { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? InquiryType { get; set; }
        public string? PreferredContactMethod { get; set; }
        public string? PreferredContactTime { get; set; }
        public string? ProjectType { get; set; }
        public string? ProjectDetails { get; set; }
        public DateTime? RequiredDate { get; set; }
        public string? Budget { get; set; }
        public string? Timeline { get; set; }
        public string? Location { get; set; }
        public string? Source { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public bool IsRead { get; set; }
        public bool IsReplied { get; set; }
        public string? Reply { get; set; }
        public DateTime? RepliedAt { get; set; }
        public string? RepliedBy { get; set; }
        public string? Notes { get; set; }
        public string? IpAddress { get; set; }
        public string? UserAgent { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}