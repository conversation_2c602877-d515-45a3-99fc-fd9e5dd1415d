"use client"

import React, { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Shield,
  Droplets,
  Wrench,
  Award,
  CheckCircle,
  Download,
  Phone,
  Mail,
  Building,
  Zap,
  Clock,
  Thermometer,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { AnimatedSection } from "@/components/animated-section"
import { StaggerContainer } from "@/components/stagger-container"
import { AnimatedCard } from "@/components/animated-card"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import { db } from "@/lib/database"


export default function DrylexPage() {
  const [activeCategory, setActiveCategory] = useState('waterproofing')
  const settings = db.getSettings() || { address: '', phone: '', email: '' }
  const allProducts = db.getProducts()
  const drylexProducts = allProducts.filter(p => p.category.includes('Drylex')).slice(0, 4)
  const featuredProjects = db.getProjects().slice(0, 2)
  const testimonials = db.getTestimonials().slice(0, 2)
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Drylex Products', href: '/drylex' }
  ]
  const waterproofingProducts = drylexProducts.filter(p => p.category.includes('Waterproofing'))
  const repairProducts = drylexProducts.filter(p => p.category.includes('Repair'))
  const features = [
    {
      title: "Premium Waterproofing Systems",
      description: "Our advanced waterproofing solutions protect buildings from water damage in even the harshest Iraqi climate conditions",
      iconName: "droplets"
    },
    {
      title: "Concrete Repair Solutions",
      description: "Comprehensive concrete repair and protection systems that extend the lifespan of construction projects",
      iconName: "wrench"
    },
    {
      title: "Construction Chemicals",
      description: "High-quality construction chemicals for bonding, sealing, and surface preparation",
      iconName: "zap"
    },
    {
      title: "Technical Expertise",
      description: "Local technical support from experienced professionals familiar with Iraqi construction challenges",
      iconName: "award"
    }
  ]
  const applications = [
    {
      title: "Infrastructure Projects",
      description: "Waterproofing for bridges, roads, and municipal infrastructure",
      image: "/images/applications/infrastructure.jpg",
      link: "/projects/infrastructure",
      iconName: "shield"
    },
    {
      title: "Building Construction",
      description: "Comprehensive waterproofing solutions for residential and commercial buildings",
      image: "/images/applications/building.jpg",
      link: "/projects/building",
      iconName: "building"
    },
    {
      title: "Industrial Facilities",
      description: "Durable protection for industrial plants and manufacturing facilities",
      image: "/images/applications/industrial.jpg",
      link: "/projects/industrial",
      iconName: "thermometer"
    }
  ]

  // JSON-LD for Product
  const productJsonLd = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: "Drylex Waterproofing Systems",
    description: "Premium waterproofing membranes and liquid systems for Iraqi construction projects",
    brand: {
      "@type": "Brand",
      name: "Drylex",
    },
    category: "Construction Materials > Waterproofing",
    image: "https://drylexiraq.com/images/products/waterproofing-system.jpg",
    offers: {
      "@type": "OfferCatalog",
      name: "Waterproofing Systems Catalog",
      url: "https://drylexiraq.com/products/waterproofing",
      numberOfItems: "20",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.7",
      reviewCount: "85",
    },
    manufacturer: {
      "@type": "Organization",
      name: "Drylex",
      url: "https://drylex.com",
      logo: "https://drylex.com/logo.png",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ali Al-Maliki",
        datePublished: "2024-08-15",
        name: "Excellent waterproofing solution",
        reviewBody: "Used Drylex waterproofing system for a basement project in Baghdad. No leaks after 6 months, even during heavy rains. Highly recommended!"
      },
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ahmed Al-Sudani",
        datePublished: "2024-07-20",
        name: "Superior quality materials",
        reviewBody: "Drylex construction chemicals have exceeded our expectations in several infrastructure projects. Durable and reliable solutions."
      },
    ]
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": settings.address,
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": settings.phone,
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(productJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
        <div className="container mx-auto px-4 py-8">
          <SEOBreadcrumbs items={breadcrumbItems} />
          
          {/* Hero Section */}
          <AnimatedSection>
            <div className="text-center mb-12">
              <Badge className="mb-4 bg-blue-100 text-blue-800 hover:bg-blue-200">
                Authorized IRAQ Distributor
              </Badge>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                Drylex Waterproofing Systems
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Premium waterproofing and repair solutions engineered for superior performance. 
                DRYLEX  Materials & Services is your trusted authorized distributor in the IRAQ.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  <Download className="mr-2 h-5 w-5" />
                  Download Catalog
                </Button>
                <Button size="lg" variant="outline">
                  <Phone className="mr-2 h-5 w-5" />
                  Contact Us
                </Button>
              </div>
            </div>
          </AnimatedSection>

          {/* Features Section */}
          <AnimatedSection>
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-12">Why Choose Drylex?</h2>
              <StaggerContainer>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {features.map((feature, index) => (
                    <AnimatedCard key={index} delay={index * 0.1}>
                      <Card className="h-full hover:shadow-lg transition-shadow">
                        <CardContent className="p-6 text-center">
                          {feature.iconName === 'droplets' && <Droplets className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                          {feature.iconName === 'wrench' && <Wrench className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                          {feature.iconName === 'zap' && <Zap className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                          {feature.iconName === 'award' && <Award className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                          <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                          <p className="text-gray-600">{feature.description}</p>
                        </CardContent>
                      </Card>
                    </AnimatedCard>
                  ))}
                </div>
              </StaggerContainer>
            </div>
          </AnimatedSection>

          {/* Products Section */}
          <AnimatedSection>
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-12">Drylex Product Range</h2>
              <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-8">
                  <TabsTrigger value="waterproofing">Waterproofing Systems</TabsTrigger>
                  <TabsTrigger value="repair">Repair Systems</TabsTrigger>
                </TabsList>
                
                <TabsContent value="waterproofing">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {waterproofingProducts.map((product, index) => (
                      <AnimatedCard key={product.id} delay={index * 0.1}>
                        <Card className="h-full hover:shadow-lg transition-shadow">
                          <div className="relative h-48 overflow-hidden rounded-t-lg">
                            <Image
                              src={product.image}
                              alt={product.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <CardHeader>
                            <CardTitle className="text-lg">{product.name}</CardTitle>
                            <p className="text-sm text-gray-600">{product.nameAr}</p>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-600 mb-4">{product.description}</p>
                            <div className="flex flex-wrap gap-2 mb-4">
                              {product.features.slice(0, 3).map((feature, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                            <div className="flex gap-2">
                              <Button size="sm" className="flex-1">
                                <Download className="mr-2 h-4 w-4" />
                                Datasheet
                              </Button>
                              <Button size="sm" variant="outline">
                                Inquiry
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </AnimatedCard>
                    ))}
                  </div>
                </TabsContent>
                
                <TabsContent value="repair">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {repairProducts.map((product, index) => (
                      <AnimatedCard key={product.id} delay={index * 0.1}>
                        <Card className="h-full hover:shadow-lg transition-shadow">
                          <div className="relative h-48 overflow-hidden rounded-t-lg">
                            <Image
                              src={product.image}
                              alt={product.name}
                              fill
                              className="object-cover"
                            />
                          </div>
                          <CardHeader>
                            <CardTitle className="text-lg">{product.name}</CardTitle>
                            <p className="text-sm text-gray-600">{product.nameAr}</p>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-600 mb-4">{product.description}</p>
                            <div className="flex flex-wrap gap-2 mb-4">
                              {product.features.slice(0, 3).map((feature, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                            <div className="flex gap-2">
                              <Button size="sm" className="flex-1">
                                <Download className="mr-2 h-4 w-4" />
                                Datasheet
                              </Button>
                              <Button size="sm" variant="outline">
                                Inquiry
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </AnimatedCard>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </AnimatedSection>

          {/* Applications Section */}
          <AnimatedSection>
            <div className="mb-16">
              <h2 className="text-3xl font-bold text-center mb-12">Applications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {applications.map((app, index) => (
                  <AnimatedCard key={index} delay={index * 0.1}>
                    <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                      {app.iconName === 'shield' && <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                      {app.iconName === 'building' && <Building className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                      {app.iconName === 'thermometer' && <Thermometer className="h-12 w-12 text-blue-600 mx-auto mb-4" />}
                      <h3 className="text-lg font-semibold mb-2">{app.title}</h3>
                      <p className="text-gray-600 text-sm">{app.description}</p>
                    </Card>
                  </AnimatedCard>
                ))}
              </div>
            </div>
          </AnimatedSection>

          {/* CTA Section */}
          <AnimatedSection>
            <Card className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
              <CardContent className="p-8 text-center">
                <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
                <p className="text-xl mb-6 opacity-90">
                  Contact our technical team for product recommendations and project consultation.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button size="lg" variant="secondary">
                    <Mail className="mr-2 h-5 w-5" />
                    Email Us
                  </Button>
                  <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-800">
                    <Phone className="mr-2 h-5 w-5" />
                    Call Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AnimatedSection>
        </div>
      </div>
    </>
  )
}