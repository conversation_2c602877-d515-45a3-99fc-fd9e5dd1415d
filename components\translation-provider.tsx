"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import enTranslations from '../public/locales/en.json'
import arTranslations from '../public/locales/ar.json'

type Language = "en" | "ar"

interface TranslationContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, fallback?: string) => string
  isRTL: boolean
  isLoading: boolean
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined)

interface TranslationProviderProps {
  children: ReactNode
}

export function TranslationProvider({ children }: TranslationProviderProps) {
  const [language, setLanguage] = useState<Language>("en")
  const [translations, setTranslations] = useState<Record<string, any>>(enTranslations)
  const [isLoading, setIsLoading] = useState(false)

  const isRTL = language === "ar"

  // Initialize language from localStorage
  useEffect(() => {
    const savedLanguage = localStorage.getItem("preferred-language") as Language
    if (savedLanguage && (savedLanguage === "en" || savedLanguage === "ar")) {
      setLanguage(savedLanguage)
    }
  }, [])

  // Load translations
  useEffect(() => {
    const loadTranslations = () => {
      try {
        if (language === "ar") {
          setTranslations(arTranslations)
        } else {
          setTranslations(enTranslations)
        }
      } catch (error) {
        console.error("Failed to load translations:", error)
        setTranslations(enTranslations)
      }
    }

    loadTranslations()
  }, [language])

  // Update document direction and language
  useEffect(() => {
    document.documentElement.dir = isRTL ? "rtl" : "ltr"
    document.documentElement.lang = language

    // Update body classes for font switching
    document.body.classList.remove("rtl", "ltr")
    document.body.classList.add(isRTL ? "rtl" : "ltr")
  }, [language, isRTL])

  // Enhanced setLanguage function
  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    localStorage.setItem("preferred-language", lang)
  }

  // Translation function with fallback support
  const t = (key: string, fallback?: string): string => {
    const keys = key.split(".")
    let value: any = translations

    for (const k of keys) {
      if (value && typeof value === "object" && k in value) {
        value = value[k]
      } else {
        // If translation not found and we have a fallback, use it
        if (fallback) {
          return fallback
        }
        // Otherwise return the key for debugging
        console.warn(`Translation missing for key: ${key}`)
        return key
      }
    }

    return typeof value === "string" ? value : fallback || key
  }

  return (
    <TranslationContext.Provider
      value={{
        language,
        setLanguage: handleSetLanguage,
        t,
        isRTL,
        isLoading,
      }}
    >
      {children}
    </TranslationContext.Provider>
  )
}

export function useTranslation() {
  const context = useContext(TranslationContext)
  if (context === undefined) {
    throw new Error("useTranslation must be used within a TranslationProvider")
  }
  return context
}
