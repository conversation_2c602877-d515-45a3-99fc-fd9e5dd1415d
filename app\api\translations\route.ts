import { type NextRequest, NextResponse } from "next/server"

// Mock translations data (replace with database in production)
const mockTranslations = [
  {
    id: 1,
    translation_key: "nav.home",
    language_code: "en",
    translation_value: "Home",
    context: "navigation",
    page: "global",
    section: "header",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    translation_key: "nav.home",
    language_code: "ar",
    translation_value: "الرئيسية",
    context: "navigation",
    page: "global",
    section: "header",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  // Add more translations...
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get("language")
    const page = searchParams.get("page")
    const section = searchParams.get("section")

    let filteredTranslations = mockTranslations

    if (language) {
      filteredTranslations = filteredTranslations.filter((t) => t.language_code === language)
    }

    if (page) {
      filteredTranslations = filteredTranslations.filter((t) => t.page === page)
    }

    if (section) {
      filteredTranslations = filteredTranslations.filter((t) => t.section === section)
    }

    // Convert to key-value format for easier use
    const translationsMap = filteredTranslations.reduce(
      (acc, translation) => {
        if (!acc[translation.language_code]) {
          acc[translation.language_code] = {}
        }
        acc[translation.language_code][translation.translation_key] = translation.translation_value
        return acc
      },
      {} as Record<string, Record<string, string>>,
    )

    return NextResponse.json({
      success: true,
      data: translationsMap,
      total: filteredTranslations.length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch translations" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { translation_key, language_code, translation_value, context, page, section } = body

    if (!translation_key || !language_code || !translation_value) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    const newTranslation = {
      id: mockTranslations.length + 1,
      translation_key,
      language_code,
      translation_value,
      context: context || null,
      page: page || null,
      section: section || null,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    mockTranslations.push(newTranslation)

    return NextResponse.json({
      success: true,
      data: newTranslation,
      message: "Translation created successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to create translation" }, { status: 500 })
  }
}
