// Security utilities for XSS prevention and input sanitization

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export const sanitizeHTML = (html: string): string => {
  if (!html) return ''
  
  return html
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .replace(/iframe/gi, '') // Remove iframe tags
    .replace(/object/gi, '') // Remove object tags
    .replace(/embed/gi, '') // Remove embed tags
    .replace(/form/gi, '') // Remove form tags
    .trim()
}

/**
 * Sanitizes JSON data before using in dangerouslySetInnerHTML
 */
export const sanitizeJSON = (data: any): string => {
  if (!data) return ''
  
  try {
    const jsonString = JSON.stringify(data, null, 2)
    return sanitizeHTML(jsonString)
  } catch (error) {
    console.error('JSON sanitization error:', error)
    return ''
  }
}

/**
 * Validates and sanitizes email addresses
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Sanitizes user input for safe display
 */
export const sanitizeInput = (input: string, maxLength: number = 1000): string => {
  if (!input) return ''
  
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim()
    .slice(0, maxLength) // Limit length
}

/**
 * Generates a secure CSRF token
 */
export const generateCSRFToken = (): string => {
  const array = new Uint8Array(32)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * Validates CSRF token
 */
export const validateCSRFToken = (token: string, sessionToken: string): boolean => {
  return token && sessionToken && token === sessionToken
}

/**
 * Sanitizes file names to prevent path traversal
 */
export const sanitizeFileName = (fileName: string): string => {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/\.\./g, '') // Remove path traversal attempts
    .replace(/^\.+/, '') // Remove leading dots
    .slice(0, 255) // Limit length
}

/**
 * Validates and sanitizes phone numbers
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

/**
 * Sanitizes URLs to prevent XSS
 */
export const sanitizeURL = (url: string): string => {
  if (!url) return ''
  
  // Remove dangerous protocols
  const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'onload:', 'onerror:']
  
  for (const protocol of dangerousProtocols) {
    if (url.toLowerCase().startsWith(protocol)) {
      return ''
    }
  }
  
  return url.trim()
}
