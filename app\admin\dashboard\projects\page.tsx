"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Plus, Edit, Trash2, Save, X } from "lucide-react"
import { db } from "@/lib/database"
import type { Project } from "@/lib/database"
import { AuthGuard } from "@/components/auth-guard"


// JSON-LD for Admin Projects Page
const adminProjectsJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Admin Projects - DRYLEX Iraq",
  description: "Admin dashboard for managing projects on DRYLEX Iraq website. Add, edit, and remove construction project case studies and details.",
  url: "https://drylexiraq.com/admin/dashboard/projects",
  mainContentOfPage: {
    "@type": "WebApplication",
    name: "DRYLEX Iraq Admin Projects",
    description: "Web application for managing construction project case studies and details for DRYLEX Iraq website",
    operatingSystem: "Web",
    applicationCategory: "BusinessApplication",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      eligibleRegion: "IQ",
    },
  },
}

export default function ProjectsPage() {
  // JSON-LD for WebPage
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Admin Projects - DRYLEX Iraq",
    "description": "Admin dashboard projects management for DRYLEX Iraq construction materials",
    "url": "https://drylexiraq.com/admin/dashboard/projects",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Admin",
          "item": "https://drylexiraq.com/admin"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Projects",
          "item": "https://drylexiraq.com/admin/dashboard/projects"
        }
      ]
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  const [projects, setProjects] = useState<Project[]>([])
  const [editingProject, setEditingProject] = useState<Project | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  const projectTypes = ["Commercial", "Residential", "Industrial", "Infrastructure"]
  const projectStatuses = ["completed", "ongoing", "planned"]

  const [formData, setFormData] = useState({
    name: "",
    nameAr: "",
    type: "",
    location: "",
    date: "",
    description: "",
    descriptionAr: "",
    beforeImage: "",
    afterImage: "",
    videoUrl: "",
    services: "",
    client: "",
    status: "completed" as "completed" | "ongoing" | "planned",
  })

  useEffect(() => {
    loadProjects()
  }, [])

  const loadProjects = () => {
    setProjects(db.getProjects())
  }

  const handleCreate = () => {
    setIsCreating(true)
    setFormData({
      name: "",
      nameAr: "",
      type: "",
      location: "",
      date: "",
      description: "",
      descriptionAr: "",
      beforeImage: "",
      afterImage: "",
      videoUrl: "",
      services: "",
      client: "",
      status: "completed",
    })
  }

  const handleEdit = (project: Project) => {
    setEditingProject(project)
    setFormData({
      name: project.name,
      nameAr: project.nameAr,
      type: project.type,
      location: project.location,
      date: project.date,
      description: project.description,
      descriptionAr: project.descriptionAr,
      beforeImage: project.beforeImage,
      afterImage: project.afterImage,
      videoUrl: project.videoUrl || "",
      services: project.services.join(", "),
      client: project.client,
      status: project.status,
    })
  }

  const handleSave = () => {
    try {
      const projectData = {
        ...formData,
        services: formData.services
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s),
      }

      if (editingProject) {
        db.updateProject(editingProject.id, projectData)
        toast({ title: "Project updated successfully!" })
      } else {
        db.createProject(projectData)
        toast({ title: "Project created successfully!" })
      }

      loadProjects()
      handleCancel()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save project",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this project?")) {
      db.deleteProject(id)
      loadProjects()
      toast({ title: "Project deleted successfully!" })
    }
  }

  const handleCancel = () => {
    setEditingProject(null)
    setIsCreating(false)
    setFormData({
      name: "",
      nameAr: "",
      type: "",
      location: "",
      date: "",
      description: "",
      descriptionAr: "",
      beforeImage: "",
      afterImage: "",
      videoUrl: "",
      services: "",
      client: "",
      status: "completed",
    })
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(adminProjectsJsonLd) }} />
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Project Management</h1>
              <p className="text-gray-600">Manage your project portfolio</p>
            </div>
            <Button onClick={handleCreate} className="bg-orange-600 hover:bg-orange-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Project
            </Button>
          </div>

          {(isCreating || editingProject) && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>{editingProject ? "Edit Project" : "Create New Project"}</CardTitle>
                <CardDescription>
                  {editingProject ? "Update project information" : "Add a new project to your portfolio"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Project Name (English)</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter project name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nameAr">Project Name (Arabic)</Label>
                    <Input
                      id="nameAr"
                      value={formData.nameAr}
                      onChange={(e) => setFormData({ ...formData, nameAr: e.target.value })}
                      placeholder="Enter Arabic name"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">Project Type</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        {projectTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      placeholder="Enter location"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date">Date</Label>
                    <Input
                      id="date"
                      value={formData.date}
                      onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                      placeholder="Enter date"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="client">Client</Label>
                    <Input
                      id="client"
                      value={formData.client}
                      onChange={(e) => setFormData({ ...formData, client: e.target.value })}
                      placeholder="Enter client name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: "completed" | "ongoing" | "planned") =>
                        setFormData({ ...formData, status: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {projectStatuses.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status.charAt(0).toUpperCase() + status.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="beforeImage">Before Image URL</Label>
                    <Input
                      id="beforeImage"
                      value={formData.beforeImage}
                      onChange={(e) => setFormData({ ...formData, beforeImage: e.target.value })}
                      placeholder="Enter before image URL"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="afterImage">After Image URL</Label>
                    <Input
                      id="afterImage"
                      value={formData.afterImage}
                      onChange={(e) => setFormData({ ...formData, afterImage: e.target.value })}
                      placeholder="Enter after image URL"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="videoUrl">Video URL (optional)</Label>
                  <Input
                    id="videoUrl"
                    value={formData.videoUrl}
                    onChange={(e) => setFormData({ ...formData, videoUrl: e.target.value })}
                    placeholder="Enter video URL"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (English)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter project description"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="descriptionAr">Description (Arabic)</Label>
                    <Textarea
                      id="descriptionAr"
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({ ...formData, descriptionAr: e.target.value })}
                      placeholder="Enter Arabic description"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="services">Services Provided (comma-separated)</Label>
                  <Textarea
                    id="services"
                    value={formData.services}
                    onChange={(e) => setFormData({ ...formData, services: e.target.value })}
                    placeholder="Service 1, Service 2, Service 3"
                    rows={2}
                  />
                </div>

                <div className="flex gap-4">
                  <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                    <Save className="h-4 w-4 mr-2" />
                    Save Project
                  </Button>
                  <Button onClick={handleCancel} variant="outline">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 gap-6">
            {projects.map((project) => (
              <Card key={project.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-2">
                        <h3 className="text-xl font-semibold">{project.name}</h3>
                        <Badge variant={project.status === "completed" ? "default" : "secondary"}>
                          {project.status}
                        </Badge>
                        <Badge variant="outline">{project.type}</Badge>
                      </div>
                      <p className="text-gray-600 mb-2">{project.nameAr}</p>
                      <p className="text-gray-700 mb-2">{project.description}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
                        <span>{project.location}</span>
                        <span>•</span>
                        <span>{project.date}</span>
                        <span>•</span>
                        <span>{project.client}</span>
                      </div>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {project.services.map((service, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {service}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-sm text-gray-500">
                        Updated: {new Date(project.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEdit(project)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(project.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </>
  )
}
