-- Create database tables for image management system

-- Main image sizes configuration table
CREATE TABLE IF NOT EXISTS image_sizes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_name VARCHAR(50) NOT NULL,
    image_type VARCHAR(50) NOT NULL,
    width INT NOT NULL,
    height INT NOT NULL,
    aspect_ratio VARCHAR(20),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_section_type (section_name, image_type)
);

-- Products table with image specifications
CREATE TABLE IF NOT EXISTS products_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('thumbnail', 'gallery', 'hero', 'detail') DEFAULT 'thumbnail',
    width INT,
    height INT,
    file_size INT, -- in bytes
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_product_id (product_id),
    INDEX idx_image_type (image_type)
);

-- Sliders table with image specifications
CREATE TABLE IF NOT EXISTS slider_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    slider_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    mobile_image_url VARCHAR(500),
    tablet_image_url VARCHAR(500),
    width INT NOT NULL,
    height INT NOT NULL,
    mobile_width INT,
    mobile_height INT,
    tablet_width INT,
    tablet_height INT,
    title VARCHAR(255),
    subtitle VARCHAR(255),
    description TEXT,
    cta_text VARCHAR(100),
    cta_link VARCHAR(255),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_slider_id (slider_id),
    INDEX idx_sort_order (sort_order)
);

-- Services table with image specifications
CREATE TABLE IF NOT EXISTS services_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('icon', 'banner', 'gallery', 'thumbnail') DEFAULT 'thumbnail',
    width INT,
    height INT,
    file_size INT,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_service_id (service_id),
    INDEX idx_image_type (image_type)
);

-- Projects table with image specifications
CREATE TABLE IF NOT EXISTS projects_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('before', 'after', 'progress', 'thumbnail', 'gallery') DEFAULT 'thumbnail',
    width INT,
    height INT,
    file_size INT,
    alt_text VARCHAR(255),
    caption TEXT,
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_project_id (project_id),
    INDEX idx_image_type (image_type),
    INDEX idx_sort_order (sort_order)
);

-- Image optimization settings
CREATE TABLE IF NOT EXISTS image_optimization_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_name VARCHAR(50) NOT NULL,
    quality INT DEFAULT 85,
    format ENUM('webp', 'jpg', 'png', 'auto') DEFAULT 'auto',
    lazy_loading BOOLEAN DEFAULT TRUE,
    compression_level INT DEFAULT 80,
    responsive_breakpoints JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_section (section_name)
);

-- Insert default image size configurations
INSERT INTO image_sizes (section_name, image_type, width, height, aspect_ratio, description) VALUES
-- Product images
('products', 'thumbnail', 400, 300, '4:3', 'Product thumbnail for grid display'),
('products', 'detail', 800, 600, '4:3', 'Product detail page main image'),
('products', 'gallery', 600, 450, '4:3', 'Product gallery images'),
('products', 'hero', 1200, 600, '2:1', 'Product hero banner'),

-- Slider images
('slider', 'desktop', 1920, 1080, '16:9', 'Desktop slider images'),
('slider', 'tablet', 1024, 768, '4:3', 'Tablet slider images'),
('slider', 'mobile', 768, 1024, '3:4', 'Mobile slider images'),

-- Service images
('services', 'icon', 64, 64, '1:1', 'Service icon images'),
('services', 'thumbnail', 400, 300, '4:3', 'Service thumbnail images'),
('services', 'banner', 1200, 400, '3:1', 'Service banner images'),
('services', 'gallery', 600, 400, '3:2', 'Service gallery images'),

-- Project images
('projects', 'thumbnail', 400, 300, '4:3', 'Project thumbnail images'),
('projects', 'before', 800, 600, '4:3', 'Project before images'),
('projects', 'after', 800, 600, '4:3', 'Project after images'),
('projects', 'gallery', 600, 450, '4:3', 'Project gallery images'),
('projects', 'hero', 1200, 600, '2:1', 'Project hero images');

-- Insert default optimization settings
INSERT INTO image_optimization_settings (section_name, quality, format, responsive_breakpoints) VALUES
('products', 85, 'webp', '{"sm": 400, "md": 600, "lg": 800, "xl": 1200}'),
('slider', 90, 'webp', '{"sm": 768, "md": 1024, "lg": 1440, "xl": 1920}'),
('services', 80, 'webp', '{"sm": 300, "md": 400, "lg": 600, "xl": 800}'),
('projects', 85, 'webp', '{"sm": 400, "md": 600, "lg": 800, "xl": 1200}');
