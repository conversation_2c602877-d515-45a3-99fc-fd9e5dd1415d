"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, AlertTriangle, CheckCircle, XCircle, Eye, Lock } from "lucide-react"

interface SecurityMetrics {
  totalEvents: number
  suspiciousActivity: number
  failedLogins: number
  mfaAttempts: number
  rateLimitHits: number
  last24Hours: number
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
}

interface SecurityEvent {
  type: string
  userId?: string
  ipAddress: string
  userAgent: string
  timestamp: number
  details: any
}

export function ZeroRiskSecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null)
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchSecurityData()
    const interval = setInterval(fetchSecurityData, 30000) // Update every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const fetchSecurityData = async () => {
    try {
      const response = await fetch('/api/security/monitor')
      const data = await response.json()
      
      if (data.success) {
        setMetrics(data.metrics)
        setEvents(data.events)
      }
    } catch (error) {
      console.error('Failed to fetch security data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'HIGH': return 'bg-red-500'
      case 'MEDIUM': return 'bg-yellow-500'
      case 'LOW': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'suspicious_activity': return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'login_attempt': return <Lock className="h-4 w-4 text-blue-500" />
      case 'mfa_attempt': return <Shield className="h-4 w-4 text-purple-500" />
      case 'rate_limit_exceeded': return <XCircle className="h-4 w-4 text-orange-500" />
      default: return <Eye className="h-4 w-4 text-gray-500" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Security Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Zero-Risk Security Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Badge className={`${getRiskColor(metrics?.riskLevel || 'LOW')} text-white`}>
              {metrics?.riskLevel || 'LOW'} RISK
            </Badge>
            <span className="text-sm text-gray-600">
              Last updated: {new Date().toLocaleTimeString()}
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Security Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Total Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.totalEvents || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Suspicious Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{metrics?.suspiciousActivity || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Failed Logins</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{metrics?.failedLogins || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">MFA Attempts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{metrics?.mfaAttempts || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Rate Limit Hits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{metrics?.rateLimitHits || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Last 24 Hours</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{metrics?.last24Hours || 0}</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Security Events */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Security Events</CardTitle>
          <CardDescription>Last 10 security events</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {events.length === 0 ? (
              <div className="text-center text-gray-500 py-4">
                No security events in the last hour
              </div>
            ) : (
              events.map((event, index) => (
                <div key={index} className="flex items-center gap-3 p-2 border rounded">
                  {getEventIcon(event.type)}
                  <div className="flex-1">
                    <div className="font-medium">{event.type.replace('_', ' ').toUpperCase()}</div>
                    <div className="text-sm text-gray-600">
                      {event.ipAddress} • {new Date(event.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <Badge variant="outline">
                    {event.userId || 'Anonymous'}
                  </Badge>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Security Alerts */}
      {metrics && metrics.riskLevel === 'HIGH' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>High Security Risk Detected!</strong> Multiple suspicious activities detected. 
            Consider implementing additional security measures.
          </AlertDescription>
        </Alert>
      )}

      {metrics && metrics.riskLevel === 'MEDIUM' && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>Medium Security Risk:</strong> Some suspicious activities detected. 
            Monitor the situation closely.
          </AlertDescription>
        </Alert>
      )}

      {metrics && metrics.riskLevel === 'LOW' && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Low Security Risk:</strong> System is operating normally with minimal security events.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
