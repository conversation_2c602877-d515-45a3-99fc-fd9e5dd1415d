import { type NextRequest, NextResponse } from "next/server"

// Mock data for image usage statistics
const mockImageUsage = {
  products: {
    total_images: 156,
    by_type: {
      thumbnail: 52,
      detail: 52,
      gallery: 45,
      hero: 7,
    },
    total_size_mb: 245.6,
    avg_size_kb: 1612,
  },
  slider: {
    total_images: 24,
    by_type: {
      desktop: 12,
      tablet: 6,
      mobile: 6,
    },
    total_size_mb: 89.4,
    avg_size_kb: 3825,
  },
  services: {
    total_images: 32,
    by_type: {
      icon: 24,
      banner: 4,
      thumbnail: 4,
    },
    total_size_mb: 12.8,
    avg_size_kb: 410,
  },
  projects: {
    total_images: 90,
    by_type: {
      before: 45,
      after: 45,
    },
    total_size_mb: 178.2,
    avg_size_kb: 2030,
  },
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get("section")

    if (section && mockImageUsage[section as keyof typeof mockImageUsage]) {
      return NextResponse.json({
        success: true,
        data: {
          section,
          ...mockImageUsage[section as keyof typeof mockImageUsage],
        },
      })
    }

    return NextResponse.json({
      success: true,
      data: mockImageUsage,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch image usage data" }, { status: 500 })
  }
}
