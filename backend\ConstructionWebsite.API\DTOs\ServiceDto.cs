namespace ConstructionWebsite.API.DTOs
{
    public class ServiceDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public decimal? Price { get; set; }
        public string Currency { get; set; } = "USD";
        public string? PriceType { get; set; }
        public string? Duration { get; set; }
        public bool IsFeatured { get; set; }
        public bool IsActive { get; set; }
        public int SortOrder { get; set; }
        public int ViewCount { get; set; }
        public int InquiryCount { get; set; }
        public CategoryDto? Category { get; set; }
        public List<string> Features { get; set; } = new List<string>();
        public List<string> Benefits { get; set; } = new List<string>();
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class ServiceDetailDto : ServiceDto
    {
        public List<string> Process { get; set; } = new List<string>();
        public List<string> Requirements { get; set; } = new List<string>();
        public List<string> Deliverables { get; set; } = new List<string>();
        public string? Portfolio { get; set; }
        public List<ImageDto> Images { get; set; } = new List<ImageDto>();
        public string? VideoUrl { get; set; }
        public string? BrochureUrl { get; set; }
    }

    public class CreateServiceDto
    {
        public int? CategoryId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public decimal? Price { get; set; }
        public string Currency { get; set; } = "USD";
        public string? PriceType { get; set; }
        public string? Duration { get; set; }
        public bool IsFeatured { get; set; }
        public bool IsActive { get; set; } = true;
        public int SortOrder { get; set; }
        public List<string> Features { get; set; } = new List<string>();
        public List<string> Benefits { get; set; } = new List<string>();
        public List<string> Process { get; set; } = new List<string>();
        public List<string> Requirements { get; set; } = new List<string>();
        public List<string> Deliverables { get; set; } = new List<string>();
        public string? Portfolio { get; set; }
        public string? VideoUrl { get; set; }
        public string? BrochureUrl { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
    }

    public class UpdateServiceDto
    {
        public int? CategoryId { get; set; }
        public string? Name { get; set; }
        public string? NameAr { get; set; }
        public string? Slug { get; set; }
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public decimal? Price { get; set; }
        public string? Currency { get; set; }
        public string? PriceType { get; set; }
        public string? Duration { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsActive { get; set; }
        public int? SortOrder { get; set; }
        public List<string>? Features { get; set; }
        public List<string>? Benefits { get; set; }
        public List<string>? Process { get; set; }
        public List<string>? Requirements { get; set; }
        public List<string>? Deliverables { get; set; }
        public string? Portfolio { get; set; }
        public string? VideoUrl { get; set; }
        public string? BrochureUrl { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
    }
}