"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Share2,
  MessageCircle,
  Phone,
  Star,
  Eye,
  MapPin,
  Calendar,
  Building,
  DollarSign,
  Clock,
  Award,
  Users,
  ChevronLeft,
  ChevronRight,
  Download,
  ExternalLink,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import type { Project } from "@/lib/database-enhanced"

interface ProjectDetailClientProps {
  project: Project
}

export function ProjectDetailClient({ project }: ProjectDetailClientProps) {
  const { t, language } = useTranslation()
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [activeTab, setActiveTab] = useState("overview")

  // Language-aware content
  const displayName = language === "ar" ? project.name_ar || project.name : project.name
  const displayDescription = language === "ar" ? project.description_ar || project.description : project.description
  const displayShortDescription =
    language === "ar" ? project.short_description_ar || project.short_description : project.short_description
  const displayLocation = language === "ar" ? project.location_ar || project.location : project.location
  const displayClientName = language === "ar" ? project.client_name_ar || project.client_name : project.client_name
  const displayKeyFeatures = language === "ar" ? project.key_features_ar || project.key_features : project.key_features

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-600"
      case "ongoing":
        return "bg-blue-600"
      case "planning":
        return "bg-yellow-600"
      default:
        return "bg-gray-600"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("status.completed", "Completed")
      case "ongoing":
        return t("status.ongoing", "Ongoing")
      case "planning":
        return t("status.planning", "Planning")
      default:
        return status
    }
  }

  const handleInquiry = () => {
    const message = t(
      "whatsapp.project_inquiry",
      "Hello! I'm interested in learning more about your {project} project. Can you provide more details about similar projects?",
    ).replace("{project}", displayName)

    const whatsappUrl = `https://wa.me/+971501234567?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: displayName,
          text: displayShortDescription,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const nextImage = () => {
    if (project.images && project.images.length > 0) {
      setSelectedImageIndex((prev) => (prev + 1) % project.images.length)
    }
  }

  const prevImage = () => {
    if (project.images && project.images.length > 0) {
      setSelectedImageIndex((prev) => (prev - 1 + project.images.length) % project.images.length)
    }
  }

  const selectedImage = project.images?.[selectedImageIndex]

  return (
    <div className="max-w-7xl mx-auto">
      {/* Back Navigation */}
      <div className="mb-6">
        <Link
          href="/projects"
          className="inline-flex items-center text-sm text-gray-600 hover:text-orange-600 transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t("nav.back_to_projects", "Back to Projects")}
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Project Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden group">
            {selectedImage ? (
              <Image
                src={selectedImage.image_url || "/placeholder.svg"}
                alt={
                  language === "ar"
                    ? selectedImage.alt_text_ar || selectedImage.alt_text || displayName
                    : selectedImage.alt_text || displayName
                }
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-400">{t("no_image", "No Image Available")}</span>
              </div>
            )}

            {/* Image Navigation */}
            {project.images && project.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </>
            )}
          </div>

          {/* Thumbnail Images */}
          {project.images && project.images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto">
              {project.images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-colors ${
                    index === selectedImageIndex ? "border-orange-600" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image.image_url || "/placeholder.svg"}
                    alt={`${displayName} ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Project Information */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{displayName}</h1>
                {project.category && <p className="text-sm text-gray-500">{project.category.name}</p>}
              </div>
              <button
                onClick={handleShare}
                className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors ml-4"
              >
                <Share2 className="h-5 w-5" />
              </button>
            </div>

            {/* Status and Featured Badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              <Badge className={`${getStatusColor(project.project_status || "")} text-white`}>
                {getStatusText(project.project_status || "")}
              </Badge>
              {project.is_featured && <Badge className="bg-orange-600">{t("badge.featured", "Featured")}</Badge>}
            </div>

            {/* Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>
                  {project.view_count || 0} {t("views", "views")}
                </span>
              </div>
              {project.completion_date && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {t("completed_in", "Completed in")} {new Date(project.completion_date).getFullYear()}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Short Description */}
          {displayShortDescription && (
            <div>
              <p className="text-gray-700 leading-relaxed">{displayShortDescription}</p>
            </div>
          )}

          {/* Project Details Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {displayClientName && (
              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-blue-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("client", "Client")}</div>
                  <div className="text-sm text-gray-600">{displayClientName}</div>
                </div>
              </div>
            )}

            {displayLocation && (
              <div className="flex items-center gap-3">
                <MapPin className="h-5 w-5 text-green-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("location", "Location")}</div>
                  <div className="text-sm text-gray-600">{displayLocation}</div>
                </div>
              </div>
            )}

            {project.project_value && (
              <div className="flex items-center gap-3">
                <DollarSign className="h-5 w-5 text-orange-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("project_value", "Project Value")}</div>
                  <div className="text-sm text-gray-600">
                    {project.currency} {(project.project_value / 1000000).toFixed(1)}M
                  </div>
                </div>
              </div>
            )}

            {project.duration_months && (
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-purple-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("duration", "Duration")}</div>
                  <div className="text-sm text-gray-600">
                    {project.duration_months} {t("months", "months")}
                  </div>
                </div>
              </div>
            )}

            {project.project_size && (
              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-indigo-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("project_size", "Project Size")}</div>
                  <div className="text-sm text-gray-600">{project.project_size}</div>
                </div>
              </div>
            )}

            {project.team_size && (
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-teal-600 flex-shrink-0" />
                <div>
                  <div className="text-sm font-medium text-gray-900">{t("team_size", "Team Size")}</div>
                  <div className="text-sm text-gray-600">
                    {project.team_size} {t("professionals", "professionals")}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Key Features */}
          {displayKeyFeatures && displayKeyFeatures.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">{t("key_features", "Key Features")}</h3>
              <ul className="space-y-2">
                {displayKeyFeatures.slice(0, 5).map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 bg-orange-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Awards */}
          {project.awards_received && project.awards_received.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">{t("awards", "Awards & Recognition")}</h3>
              <div className="flex flex-wrap gap-2">
                {project.awards_received.map((award, index) => (
                  <Badge key={index} variant="outline" className="border-yellow-600 text-yellow-600">
                    <Award className="h-3 w-3 mr-1" />
                    {award}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button onClick={handleInquiry} className="flex-1 bg-orange-600 hover:bg-orange-700 text-white" size="lg">
                <MessageCircle className="h-5 w-5 mr-2" />
                {t("btn.similar_project", "Request Similar Project")}
              </Button>
              <Button
                variant="outline"
                className="flex-1 border-orange-600 text-orange-600 hover:bg-orange-50 bg-transparent"
                size="lg"
              >
                <Phone className="h-5 w-5 mr-2" />
                {t("btn.discuss_project", "Discuss Project")}
              </Button>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                <Download className="h-4 w-4 mr-2" />
                {t("btn.download_case_study", "Download Case Study")}
              </Button>
              <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                <ExternalLink className="h-4 w-4 mr-2" />
                {t("btn.view_gallery", "View Full Gallery")}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Tabs */}
      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">{t("tab.overview", "Overview")}</TabsTrigger>
              <TabsTrigger value="timeline">{t("tab.timeline", "Timeline")}</TabsTrigger>
              <TabsTrigger value="gallery">{t("tab.gallery", "Gallery")}</TabsTrigger>
              <TabsTrigger value="testimonial">{t("tab.testimonial", "Testimonial")}</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold mb-4">{t("project_overview", "Project Overview")}</h3>
                {displayDescription ? (
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">{displayDescription}</div>
                ) : (
                  <p className="text-gray-500">{t("no_description", "No detailed description available.")}</p>
                )}

                {/* Technical Specifications */}
                {project.technical_specs && Object.keys(project.technical_specs).length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-3">{t("technical_specifications", "Technical Specifications")}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(project.technical_specs).map(([key, value]) => (
                        <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                          <span className="font-medium text-gray-900">{key}:</span>
                          <span className="text-gray-700">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="timeline" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("project_timeline", "Project Timeline")}</h3>
              <div className="space-y-6">
                {[
                  {
                    phase: t("phase.planning", "Planning & Design"),
                    date: project.start_date ? new Date(project.start_date).toLocaleDateString() : "2023-01-15",
                    description: t(
                      "phase.planning_desc",
                      "Initial planning, design development, and permit acquisition.",
                    ),
                  },
                  {
                    phase: t("phase.foundation", "Foundation Work"),
                    date: "2023-03-01",
                    description: t("phase.foundation_desc", "Site preparation and foundation construction."),
                  },
                  {
                    phase: t("phase.construction", "Main Construction"),
                    date: "2023-05-15",
                    description: t("phase.construction_desc", "Primary construction and structural work."),
                  },
                  {
                    phase: t("phase.finishing", "Finishing & Handover"),
                    date: project.completion_date
                      ? new Date(project.completion_date).toLocaleDateString()
                      : "2023-12-20",
                    description: t("phase.finishing_desc", "Final touches, quality inspection, and project handover."),
                  },
                ].map((item, index) => (
                  <div key={index} className="flex gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center font-semibold">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-semibold text-gray-900">{item.phase}</h4>
                        <span className="text-sm text-gray-500">{item.date}</span>
                      </div>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="gallery" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("project_gallery", "Project Gallery")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {project.images && project.images.length > 0 ? (
                  project.images.map((image, index) => (
                    <div
                      key={image.id}
                      className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden group cursor-pointer"
                    >
                      <Image
                        src={image.image_url || "/placeholder.svg"}
                        alt={
                          language === "ar"
                            ? image.alt_text_ar || image.alt_text || `${displayName} ${index + 1}`
                            : image.alt_text || `${displayName} ${index + 1}`
                        }
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <span className="text-white font-medium">{t("view_full_size", "View Full Size")}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 col-span-full text-center py-8">
                    {t("no_images", "No images available for this project.")}
                  </p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="testimonial" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("client_testimonial", "Client Testimonial")}</h3>
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center gap-1 mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <blockquote className="text-lg text-gray-700 mb-4 italic">
                  "DRYLEX  IRAQ delivered an exceptional project that exceeded our expectations. Their attention to
                  detail, professional approach, and commitment to quality made this collaboration outstanding. We
                  highly recommend their services for any construction project."
                </blockquote>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                    <Users className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{displayClientName || "Client Name"}</div>
                    <div className="text-sm text-gray-600">{t("project_manager", "Project Manager")}</div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
