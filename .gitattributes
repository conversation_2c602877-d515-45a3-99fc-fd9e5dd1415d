# Normalize all text files to LF in repo and working tree
* text=auto eol=lf

# Explicitly enforce LF for common code/text types
*.ts     text eol=lf
*.tsx    text eol=lf
*.js     text eol=lf
*.jsx    text eol=lf
*.css    text eol=lf
*.scss   text eol=lf
*.md     text eol=lf
*.json   text eol=lf
*.yml    text eol=lf
*.yaml   text eol=lf
*.html   text eol=lf
*.svg    text eol=lf

# Keep images/binaries untouched
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.webp binary
*.ico binary
*.pdf binary

