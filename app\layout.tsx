import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Cairo } from "next/font/google"
import "./globals.css"
import { TranslationProvider } from "@/components/translation-provider"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { WhatsAppFloat } from "@/components/whatsapp-float"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
  variable: "--font-cairo",
})

export const metadata: Metadata = {
  title: "DRYLEX - DRYLEX IRAQ Materials & Services",
  description:
    "Leading construction materials supplier in IRAQ since 1995. Premium concrete admixtures, waterproofing solutions, repair materials & expert construction services.",
  keywords:
    "construction materials IRAQ, concrete admixtures Dubai, waterproofing solutions, construction chemicals, building materials supplier",
  authors: [{ name: "DRYLEX" }],
  creator: "DRYLEX",
  publisher: "DRYLEX",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://drylexiraq.com"),
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en",
      "ar-AE": "/ar",
    },
  },
  openGraph: {
    title: "DRYLEX - New Generation Solutions",
    description:
      "Leading construction materials supplier in IRAQ. Premium concrete admixtures, waterproofing solutions, repair materials & expert construction services.",
    url: "https://drylexIraq.com",
    siteName: "DRYLEX",
    images: [
      {
        url: "/images/drylex-logo.svg",
        width: 1200,
        height: 630,
        alt: "DRYLEX - DRYLEX IRAQ Materials",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "DRYLEX - DRYLEX IRAQ Materials & Services",
    description:
      "Leading construction materials supplier in IRAQ since 1995. Premium concrete admixtures, waterproofing solutions, repair materials & expert construction services.",
    images: ["/images/drylex-logo.svg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Google Tag Manager */}
        <script dangerouslySetInnerHTML={{
          __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
          new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
          j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
          'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
          })(window,document,'script','dataLayer','GTM-W35Q4CMT');`
        }} />
        {/* End Google Tag Manager */}
        <link rel="apple-touch-icon" sizes="57x57" href="/apple-icon-57x57.png" />
        <link rel="apple-touch-icon" sizes="60x60" href="/apple-icon-60x60.png" />
        <link rel="apple-touch-icon" sizes="72x72" href="/apple-icon-72x72.png" />
        <link rel="apple-touch-icon" sizes="76x76" href="/apple-icon-76x76.png" />
        <link rel="apple-touch-icon" sizes="114x114" href="/apple-icon-114x114.png" />
        <link rel="apple-touch-icon" sizes="120x120" href="/apple-icon-120x120.png" />
        <link rel="apple-touch-icon" sizes="144x144" href="/apple-icon-144x144.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/apple-icon-152x152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-icon-180x180.png" />
        <link rel="icon" type="image/png" sizes="192x192" href="/android-icon-192x192.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="msapplication-TileColor" content="#ffffff" />
        <meta name="msapplication-TileImage" content="/ms-icon-144x144.png" />
        <meta name="theme-color" content="#1293b0" />
      </head><body
        className={`${inter.variable} ${cairo.variable} font-sans antialiased`}
        style={{
          fontFamily: "var(--font-inter), sans-serif",
        }}
      >
        {/* Google Tag Manager (noscript) */}
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W35Q4CMT"
        height="0" width="0" style={{display:"none",visibility:"hidden"}}></iframe></noscript>
        {/* End Google Tag Manager (noscript) */}
        <TranslationProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <Footer />
            <WhatsAppFloat />
          </div>
        </TranslationProvider>
      </body>
    </html>
  )
}
