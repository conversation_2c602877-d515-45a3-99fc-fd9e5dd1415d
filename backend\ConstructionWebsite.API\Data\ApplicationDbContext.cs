using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using ConstructionWebsite.API.Models;

namespace ConstructionWebsite.API.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // Product Tables
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<ProductImage> ProductImages { get; set; }
        public DbSet<ProductInquiry> ProductInquiries { get; set; }

        // Service Tables
        public DbSet<Service> Services { get; set; }
        public DbSet<ServiceCategory> ServiceCategories { get; set; }
        public DbSet<ServiceImage> ServiceImages { get; set; }
        public DbSet<ServiceInquiry> ServiceInquiries { get; set; }

        // Project Tables
        public DbSet<Project> Projects { get; set; }
        public DbSet<ProjectCategory> ProjectCategories { get; set; }
        public DbSet<ProjectImage> ProjectImages { get; set; }
        public DbSet<ProjectInquiry> ProjectInquiries { get; set; }

        // General Tables
        public DbSet<ContactMessage> ContactMessages { get; set; }
        public DbSet<WishlistItem> WishlistItems { get; set; }
        public DbSet<Translation> Translations { get; set; }
        public DbSet<SiteSettings> SiteSettings { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Product Configuration
            builder.Entity<Product>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => e.SKU).IsUnique();
                entity.HasIndex(e => new { e.Status, e.IsFeatured });
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Products)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<ProductCategory>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => new { e.IsActive, e.SortOrder });
            });

            builder.Entity<ProductImage>(entity =>
            {
                entity.HasIndex(e => new { e.ProductId, e.IsPrimary });
                entity.HasIndex(e => new { e.ProductId, e.SortOrder });

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.Images)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            builder.Entity<ProductInquiry>(entity =>
            {
                entity.HasIndex(e => new { e.ProductId, e.CreatedAt });
                entity.HasIndex(e => e.Status);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.Inquiries)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Service Configuration
            builder.Entity<Service>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => new { e.Status, e.IsFeatured });
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Services)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<ServiceCategory>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => new { e.IsActive, e.SortOrder });
            });

            builder.Entity<ServiceImage>(entity =>
            {
                entity.HasIndex(e => new { e.ServiceId, e.IsPrimary });
                entity.HasIndex(e => new { e.ServiceId, e.SortOrder });

                entity.HasOne(d => d.Service)
                    .WithMany(p => p.Images)
                    .HasForeignKey(d => d.ServiceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            builder.Entity<ServiceInquiry>(entity =>
            {
                entity.HasIndex(e => new { e.ServiceId, e.CreatedAt });
                entity.HasIndex(e => e.Status);

                entity.HasOne(d => d.Service)
                    .WithMany(p => p.Inquiries)
                    .HasForeignKey(d => d.ServiceId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Project Configuration
            builder.Entity<Project>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => new { e.IsFeatured, e.IsShowcase });
                entity.HasIndex(e => e.CreatedAt);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.Projects)
                    .HasForeignKey(d => d.CategoryId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<ProjectCategory>(entity =>
            {
                entity.HasIndex(e => e.Slug).IsUnique();
                entity.HasIndex(e => new { e.IsActive, e.SortOrder });
            });

            builder.Entity<ProjectImage>(entity =>
            {
                entity.HasIndex(e => new { e.ProjectId, e.IsPrimary });
                entity.HasIndex(e => new { e.ProjectId, e.SortOrder });

                entity.HasOne(d => d.Project)
                    .WithMany(p => p.Images)
                    .HasForeignKey(d => d.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            builder.Entity<ProjectInquiry>(entity =>
            {
                entity.HasIndex(e => new { e.ProjectId, e.CreatedAt });
                entity.HasIndex(e => e.Status);

                entity.HasOne(d => d.Project)
                    .WithMany(p => p.Inquiries)
                    .HasForeignKey(d => d.ProjectId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // General Configuration
            builder.Entity<ContactMessage>(entity =>
            {
                entity.HasIndex(e => new { e.Status, e.CreatedAt });
                entity.HasIndex(e => e.InquiryType);

                entity.HasOne(d => d.AssignedUser)
                    .WithMany(p => p.ContactMessages)
                    .HasForeignKey(d => d.AssignedTo)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<WishlistItem>(entity =>
            {
                entity.HasIndex(e => new { e.UserId, e.ProductId }).IsUnique();
                entity.HasIndex(e => new { e.SessionId, e.ProductId }).IsUnique();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.WishlistItems)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.WishlistItems)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            builder.Entity<Translation>(entity =>
            {
                entity.HasIndex(e => new { e.TranslationKey, e.LanguageCode }).IsUnique();
                entity.HasIndex(e => new { e.LanguageCode, e.IsActive });
            });

            builder.Entity<ApplicationUser>(entity =>
            {
                entity.HasIndex(e => e.CreatedAt);
                entity.HasIndex(e => e.IsActive);
            });
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity.GetType().GetProperty("UpdatedAt") != null)
                {
                    entry.Property("UpdatedAt").CurrentValue = DateTime.UtcNow;
                }

                if (entry.State == EntityState.Added && entry.Entity.GetType().GetProperty("CreatedAt") != null)
                {
                    entry.Property("CreatedAt").CurrentValue = DateTime.UtcNow;
                }
            }
        }
    }
}
