using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ConstructionWebsite.API.Services;

using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ServicesController : ControllerBase
    {
        private readonly IServiceService _serviceService;
        private readonly ILogger<ServicesController> _logger;

        public ServicesController(IServiceService serviceService, ILogger<ServicesController> logger)
        {
            _serviceService = serviceService;
            _logger = logger;
        }

        /// <summary>
        /// Get all services with optional filtering and pagination
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResult<ServiceDto>>> GetServices(
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] string? status = null,
            [FromQuery] bool? isFeatured = null,
            [FromQuery] string? language = "en",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 12,
            [FromQuery] string? sortBy = "createdAt",
            [FromQuery] string? sortOrder = "desc")
        {
            try
            {
                var result = await _serviceService.GetServicesAsync(
                    search, categoryId, status, isFeatured, language, 
                    page, pageSize, sortBy, sortOrder);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving services");
                return StatusCode(500, new { message = "An error occurred while retrieving services" });
            }
        }

        /// <summary>
        /// Get service by slug
        /// </summary>
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<ServiceDetailDto>> GetServiceBySlug(
            string slug, 
            [FromQuery] string language = "en")
        {
            try
            {
                var service = await _serviceService.GetServiceBySlugAsync(slug, language);
                if (service == null)
                {
                    return NotFound(new { message = "Service not found" });
                }

                // Increment view count
                await _serviceService.IncrementViewCountAsync(service.Id);

                return Ok(service);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving service with slug: {Slug}", slug);
                return StatusCode(500, new { message = "An error occurred while retrieving the service" });
            }
        }

        /// <summary>
        /// Get service by ID
        /// </summary>
        [HttpGet("{id:int}")]
        public async Task<ActionResult<ServiceDetailDto>> GetService(
            int id, 
            [FromQuery] string language = "en")
        {
            try
            {
                var service = await _serviceService.GetServiceByIdAsync(id, language);
                if (service == null)
                {
                    return NotFound(new { message = "Service not found" });
                }

                return Ok(service);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving service with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the service" });
            }
        }

        /// <summary>
        /// Get related services
        /// </summary>
        [HttpGet("{id:int}/related")]
        public async Task<ActionResult<List<ServiceDto>>> GetRelatedServices(
            int id,
            [FromQuery] string language = "en",
            [FromQuery] int limit = 3)
        {
            try
            {
                var relatedServices = await _serviceService.GetRelatedServicesAsync(id, language, limit);
                return Ok(relatedServices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related services for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving related services" });
            }
        }

        /// <summary>
        /// Create service inquiry
        /// </summary>
        [HttpPost("{id:int}/inquiries")]
        public async Task<ActionResult<InquiryResponseDto>> CreateServiceInquiry(
            int id, 
            [FromBody] CreateServiceInquiryDto inquiryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var result = await _serviceService.CreateInquiryAsync(
                    id, inquiryDto, clientIp, userAgent);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service inquiry for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while creating the inquiry" });
            }
        }

        /// <summary>
        /// Get service categories
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<List<CategoryDto>>> GetCategories(
            [FromQuery] string language = "en")
        {
            try
            {
                var categories = await _serviceService.GetCategoriesAsync(language);
                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving service categories");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        /// <summary>
        /// Create a new service (Admin only)
        /// </summary>
        [HttpPost]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ServiceDto>> CreateService([FromBody] CreateServiceDto serviceDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _serviceService.CreateServiceAsync(serviceDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return CreatedAtAction(nameof(GetService), new { id = result.Data.Id }, result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service");
                return StatusCode(500, new { message = "An error occurred while creating the service" });
            }
        }

        /// <summary>
        /// Update a service (Admin only)
        /// </summary>
        [HttpPut("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ServiceDto>> UpdateService(int id, [FromBody] UpdateServiceDto serviceDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _serviceService.UpdateServiceAsync(id, serviceDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the service" });
            }
        }

        /// <summary>
        /// Delete a service (Admin only)
        /// </summary>
        [HttpDelete("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult> DeleteService(int id)
        {
            try
            {
                var result = await _serviceService.DeleteServiceAsync(id);
                if (!result)
                {
                    return NotFound(new { message = "Service not found" });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting service with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the service" });
            }
        }
    }
}
