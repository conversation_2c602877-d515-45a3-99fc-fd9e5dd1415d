using ConstructionWebsite.API.Models;

namespace ConstructionWebsite.API.Services
{
    public interface IContactService
    {
        Task<IEnumerable<ContactMessage>> GetAllContactMessagesAsync();
        Task<ContactMessage?> GetContactMessageByIdAsync(int id);
        Task<ContactMessage> CreateContactMessageAsync(ContactMessage contactMessage);
        Task<ContactMessage?> UpdateContactMessageAsync(int id, ContactMessage contactMessage);
        Task<bool> DeleteContactMessageAsync(int id);
        Task<IEnumerable<ContactMessage>> GetUnreadContactMessagesAsync();
        Task<bool> MarkAsReadAsync(int id);
        Task<bool> MarkAsUnreadAsync(int id);
        Task<IEnumerable<ContactMessage>> SearchContactMessagesAsync(string searchTerm);
        Task<bool> ContactMessageExistsAsync(int id);
    }
}