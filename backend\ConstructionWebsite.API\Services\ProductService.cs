using Microsoft.EntityFrameworkCore;
using ConstructionWebsite.API.Data;
using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

using System.Text.Json;

namespace ConstructionWebsite.API.Services
{
    public class ProductService : IProductService
    {
        private readonly ApplicationDbContext _context;
        private readonly ICacheService _cacheService;
        private readonly IEmailService _emailService;
        private readonly ILogger<ProductService> _logger;

        public ProductService(
            ApplicationDbContext context,
            ICacheService cacheService,
            IEmailService emailService,
            ILogger<ProductService> logger)
        {
            _context = context;
            _cacheService = cacheService;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<PagedResult<ProductDto>> GetProductsAsync(
            string? search, int? categoryId, string? status, bool? isFeatured,
            string language, int page, int pageSize, string? sortBy, string? sortOrder)
        {
            var query = _context.Products
                .Include(p => p.Category)
                .Include(p => p.Images.Where(i => i.IsPrimary))
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(search))
            {
                query = query.Where(p => 
                    p.Name.Contains(search) || 
                    (p.NameAr != null && p.NameAr.Contains(search)) ||
                    (p.Description != null && p.Description.Contains(search)) ||
                    (p.DescriptionAr != null && p.DescriptionAr.Contains(search)));
            }

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(p => p.Status == status);
            }
            else
            {
                query = query.Where(p => p.Status == "active");
            }

            if (isFeatured.HasValue)
            {
                query = query.Where(p => p.IsFeatured == isFeatured.Value);
            }

            // Apply sorting
            query = sortBy?.ToLower() switch
            {
                "name" => sortOrder?.ToLower() == "desc" 
                    ? query.OrderByDescending(p => language == "ar" ? p.NameAr ?? p.Name : p.Name)
                    : query.OrderBy(p => language == "ar" ? p.NameAr ?? p.Name : p.Name),
                "price" => sortOrder?.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.Price)
                    : query.OrderBy(p => p.Price),
                "createdat" => sortOrder?.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.CreatedAt)
                    : query.OrderBy(p => p.CreatedAt),
                "viewcount" => sortOrder?.ToLower() == "desc" 
                    ? query.OrderByDescending(p => p.ViewCount)
                    : query.OrderBy(p => p.ViewCount),
                _ => query.OrderByDescending(p => p.CreatedAt)
            };

            var totalCount = await query.CountAsync();
            var items = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(p => new ProductDto
                {
                    Id = p.Id,
                    Name = language == "ar" ? p.NameAr ?? p.Name : p.Name,
                    Slug = p.Slug,
                    ShortDescription = language == "ar" ? p.ShortDescriptionAr ?? p.ShortDescription : p.ShortDescription,
                    Price = p.Price,
                    Currency = p.Currency,
                    Unit = p.Unit,
                    Brand = p.Brand,
                    IsFeatured = p.IsFeatured,
                    IsBestseller = p.IsBestseller,
                    IsNew = p.IsNew,
                    ViewCount = p.ViewCount,
                    InquiryCount = p.InquiryCount,
                    Category = p.Category != null ? new CategoryDto
                    {
                        Id = p.Category.Id,
                        Name = language == "ar" ? p.Category.NameAr ?? p.Category.Name : p.Category.Name,
                        Slug = p.Category.Slug
                    } : null,
                    PrimaryImage = p.Images.FirstOrDefault(i => i.IsPrimary) != null ? new ImageDto
                    {
                        Id = p.Images.First(i => i.IsPrimary).Id,
                        FileUrl = p.Images.First(i => i.IsPrimary).ImageUrl,
                        AltText = language == "ar" 
                            ? p.Images.First(i => i.IsPrimary).AltTextAr ?? p.Images.First(i => i.IsPrimary).AltText
                            : p.Images.First(i => i.IsPrimary).AltText
                    } : null,
                    CreatedAt = p.CreatedAt
                })
                .ToListAsync();

            return new PagedResult<ProductDto>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = page,
                PageSize = pageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                HasPreviousPage = page > 1,
                HasNextPage = page < (int)Math.Ceiling((double)totalCount / pageSize)
            };
        }

        public async Task<ProductDetailDto?> GetProductByIdAsync(int id, string language)
        {
            var cacheKey = $"product_detail_{id}_{language}";
            var cached = await _cacheService.GetAsync<ProductDetailDto>(cacheKey);
            if (cached != null) return cached;

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Images.OrderBy(i => i.SortOrder))
                .FirstOrDefaultAsync(p => p.Id == id && p.Status == "active");

            if (product == null) return null;

            var result = MapToProductDetailDto(product, language);
            await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(30));

            return result;
        }

        public async Task<ProductDetailDto?> GetProductBySlugAsync(string slug, string language)
        {
            var cacheKey = $"product_detail_slug_{slug}_{language}";
            var cached = await _cacheService.GetAsync<ProductDetailDto>(cacheKey);
            if (cached != null) return cached;

            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Images.OrderBy(i => i.SortOrder))
                .FirstOrDefaultAsync(p => p.Slug == slug && p.Status == "active");

            if (product == null) return null;

            var result = MapToProductDetailDto(product, language);
            await _cacheService.SetAsync(cacheKey, result, TimeSpan.FromMinutes(30));

            return result;
        }

        public async Task<List<ProductDto>> GetRelatedProductsAsync(int productId, string language, int limit)
        {
            var product = await _context.Products.FindAsync(productId);
            if (product == null) return new List<ProductDto>();

            var relatedProducts = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Images.Where(i => i.IsPrimary))
                .Where(p => p.Id != productId && 
                           p.Status == "active" && 
                           (p.CategoryId == product.CategoryId || p.Brand == product.Brand))
                .OrderByDescending(p => p.ViewCount)
                .Take(limit)
                .Select(p => new ProductDto
                {
                    Id = p.Id,
                    Name = language == "ar" ? p.NameAr ?? p.Name : p.Name,
                    Slug = p.Slug,
                    ShortDescription = language == "ar" ? p.ShortDescriptionAr ?? p.ShortDescription : p.ShortDescription,
                    Price = p.Price,
                    Currency = p.Currency,
                    Unit = p.Unit,
                    Brand = p.Brand,
                    IsFeatured = p.IsFeatured,
                    IsBestseller = p.IsBestseller,
                    IsNew = p.IsNew,
                    ViewCount = p.ViewCount,
                    InquiryCount = p.InquiryCount,
                    Category = p.Category != null ? new CategoryDto
                    {
                        Id = p.Category.Id,
                        Name = language == "ar" ? p.Category.NameAr ?? p.Category.Name : p.Category.Name,
                        Slug = p.Category.Slug
                    } : null,
                    PrimaryImage = p.Images.FirstOrDefault(i => i.IsPrimary) != null ? new ImageDto
                    {
                        Id = p.Images.First(i => i.IsPrimary).Id,
                        FileUrl = p.Images.First(i => i.IsPrimary).ImageUrl,
                        AltText = language == "ar" 
                            ? p.Images.First(i => i.IsPrimary).AltTextAr ?? p.Images.First(i => i.IsPrimary).AltText
                            : p.Images.First(i => i.IsPrimary).AltText
                    } : null,
                    CreatedAt = p.CreatedAt
                })
                .ToListAsync();

            return relatedProducts;
        }

        public async Task<List<CategoryDto>> GetCategoriesAsync(string language)
        {
            var cacheKey = $"product_categories_{language}";
            var cached = await _cacheService.GetAsync<List<CategoryDto>>(cacheKey);
            if (cached != null) return cached;

            var categories = await _context.ProductCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.SortOrder)
                .Select(c => new CategoryDto
                {
                    Id = c.Id,
                    Name = language == "ar" ? c.NameAr ?? c.Name : c.Name,
                    Slug = c.Slug,
                    Description = language == "ar" ? c.DescriptionAr ?? c.Description : c.Description,
                    Image = c.Image,
                    Icon = c.Icon
                })
                .ToListAsync();

            await _cacheService.SetAsync(cacheKey, categories, TimeSpan.FromHours(1));
            return categories;
        }

        public async Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(
            int productId, CreateProductInquiryDto inquiryDto, string? clientIp, string? userAgent)
        {
            try
            {
                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                {
                    return ServiceResult<InquiryResponseDto>.ErrorResult("Product not found");
                }

                var inquiry = new ProductInquiry
                {
                    ProductId = productId,
                    Name = inquiryDto.Name,
                    Email = inquiryDto.Email,
                    Phone = inquiryDto.Phone,
                    Company = inquiryDto.Company,
                    Subject = inquiryDto.Subject,
                    Message = inquiryDto.Message,
                    InquiryType = inquiryDto.InquiryType ?? "general",
                    Source = "website",
                    IpAddress = clientIp,
                    UserAgent = userAgent,
                    Status = "unread"
                };

                _context.ProductInquiries.Add(inquiry);
                
                // Increment inquiry count
                product.InquiryCount++;
                
                await _context.SaveChangesAsync();

                // Send notification email
                await _emailService.SendInquiryNotificationAsync(inquiry, product);

                var response = new InquiryResponseDto
                {
                    Id = inquiry.Id,
                    Name = inquiry.Name,
                    Email = inquiry.Email,
                    Phone = inquiry.Phone,
                    Company = inquiry.Company,
                    Message = inquiry.Message,
                    InquiryType = inquiry.InquiryType,
                    Source = inquiry.Source,
                    ProductId = inquiry.ProductId,
                    ProductName = product.Name,
                    IpAddress = inquiry.IpAddress,
                    UserAgent = inquiry.UserAgent,
                    CreatedAt = inquiry.CreatedAt
                };

                return ServiceResult<InquiryResponseDto>.SuccessResult(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product inquiry");
                return ServiceResult<InquiryResponseDto>.ErrorResult("Failed to create inquiry");
            }
        }

        public async Task<ServiceResult<bool>> AddToWishlistAsync(int productId, string sessionId, string? userId)
        {
            try
            {
                var product = await _context.Products.FindAsync(productId);
                if (product == null)
                {
                    return ServiceResult<bool>.ErrorResult("Product not found");
                }

                var existingItem = await _context.WishlistItems
                    .FirstOrDefaultAsync(w => w.ProductId == productId && 
                                            (w.SessionId == sessionId || w.UserId == userId));

                if (existingItem != null)
                {
                    return ServiceResult<bool>.ErrorResult("Product already in wishlist");
                }

                var wishlistItem = new WishlistItem
                {
                    ProductId = productId,
                    SessionId = sessionId,
                    UserId = userId
                };

                _context.WishlistItems.Add(wishlistItem);
                await _context.SaveChangesAsync();

                return ServiceResult<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding product to wishlist");
                return ServiceResult<bool>.ErrorResult("Failed to add to wishlist");
            }
        }

        public async Task<ServiceResult<bool>> RemoveFromWishlistAsync(int productId, string sessionId, string? userId)
        {
            try
            {
                var wishlistItem = await _context.WishlistItems
                    .FirstOrDefaultAsync(w => w.ProductId == productId && 
                                            (w.SessionId == sessionId || w.UserId == userId));

                if (wishlistItem == null)
                {
                    return ServiceResult<bool>.ErrorResult("Product not in wishlist");
                }

                _context.WishlistItems.Remove(wishlistItem);
                await _context.SaveChangesAsync();

                return ServiceResult<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing product from wishlist");
                return ServiceResult<bool>.ErrorResult("Failed to remove from wishlist");
            }
        }

        public async Task<bool> IsInWishlistAsync(int productId, string sessionId, string? userId)
        {
            return await _context.WishlistItems
                .AnyAsync(w => w.ProductId == productId && 
                              (w.SessionId == sessionId || w.UserId == userId));
        }

        public async Task IncrementViewCountAsync(int productId)
        {
            try
            {
                var product = await _context.Products.FindAsync(productId);
                if (product != null)
                {
                    product.ViewCount++;
                    await _context.SaveChangesAsync();
                    
                    // Clear cache
                    await _cacheService.RemoveAsync($"product_detail_{productId}_en");
                    await _cacheService.RemoveAsync($"product_detail_{productId}_ar");
                    await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_en");
                    await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_ar");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error incrementing view count for product {ProductId}", productId);
            }
        }

        public async Task<ServiceResult<ProductDto>> CreateProductAsync(CreateProductDto productDto)
        {
            try
            {
                var product = new Product
                {
                    CategoryId = productDto.CategoryId,
                    SKU = productDto.SKU,
                    Name = productDto.Name,
                    NameAr = productDto.NameAr,
                    Slug = productDto.Slug,
                    ShortDescription = productDto.ShortDescription,
                    ShortDescriptionAr = productDto.ShortDescriptionAr,
                    Description = productDto.Description,
                    DescriptionAr = productDto.DescriptionAr,
                    Price = productDto.Price,
                    Currency = productDto.Currency,
                    MinOrderQty = productDto.MinOrderQty,
                    Unit = productDto.Unit,
                    Weight = productDto.Weight,
                    Brand = productDto.Brand,
                    Status = productDto.Status,
                    IsFeatured = productDto.IsFeatured,
                    IsBestseller = productDto.IsBestseller,
                    IsNew = productDto.IsNew,
                    SortOrder = productDto.SortOrder,
                    CreatedAt = DateTime.UtcNow
                };

                _context.Products.Add(product);
                await _context.SaveChangesAsync();

                // Clear cache
                await _cacheService.RemoveAsync("product_categories_en");
                await _cacheService.RemoveAsync("product_categories_ar");

                var result = new ProductDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Slug = product.Slug,
                    ShortDescription = product.ShortDescription,
                    Price = product.Price,
                    Currency = product.Currency,
                    Unit = product.Unit,
                    Brand = product.Brand,
                    IsFeatured = product.IsFeatured,
                    IsBestseller = product.IsBestseller,
                    IsNew = product.IsNew,
                    ViewCount = product.ViewCount,
                    InquiryCount = product.InquiryCount,
                    CreatedAt = product.CreatedAt
                };

                return ServiceResult<ProductDto>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product");
                return ServiceResult<ProductDto>.ErrorResult("Failed to create product");
            }
        }

        public async Task<ServiceResult<ProductDto>> UpdateProductAsync(int id, UpdateProductDto productDto)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return ServiceResult<ProductDto>.ErrorResult("Product not found");
                }

                // Update properties
                if (productDto.CategoryId.HasValue) product.CategoryId = productDto.CategoryId;
                if (!string.IsNullOrEmpty(productDto.SKU)) product.SKU = productDto.SKU;
                if (!string.IsNullOrEmpty(productDto.Name)) product.Name = productDto.Name;
                if (productDto.NameAr != null) product.NameAr = productDto.NameAr;
                if (!string.IsNullOrEmpty(productDto.Slug)) product.Slug = productDto.Slug;
                if (productDto.ShortDescription != null) product.ShortDescription = productDto.ShortDescription;
                if (productDto.ShortDescriptionAr != null) product.ShortDescriptionAr = productDto.ShortDescriptionAr;
                if (productDto.Description != null) product.Description = productDto.Description;
                if (productDto.DescriptionAr != null) product.DescriptionAr = productDto.DescriptionAr;
                if (productDto.Price.HasValue) product.Price = productDto.Price;
                if (!string.IsNullOrEmpty(productDto.Currency)) product.Currency = productDto.Currency;
                if (productDto.MinOrderQty.HasValue) product.MinOrderQty = productDto.MinOrderQty.Value;
                if (productDto.Unit != null) product.Unit = productDto.Unit;
                if (productDto.Weight.HasValue) product.Weight = productDto.Weight;
                if (productDto.Brand != null) product.Brand = productDto.Brand;
                if (!string.IsNullOrEmpty(productDto.Status)) product.Status = productDto.Status;
                if (productDto.IsFeatured.HasValue) product.IsFeatured = productDto.IsFeatured.Value;
                if (productDto.IsBestseller.HasValue) product.IsBestseller = productDto.IsBestseller.Value;
                if (productDto.IsNew.HasValue) product.IsNew = productDto.IsNew.Value;
                if (productDto.SortOrder.HasValue) product.SortOrder = productDto.SortOrder.Value;

                await _context.SaveChangesAsync();

                // Clear cache
                await _cacheService.RemoveAsync($"product_detail_{id}_en");
                await _cacheService.RemoveAsync($"product_detail_{id}_ar");
                await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_en");
                await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_ar");

                var result = new ProductDto
                {
                    Id = product.Id,
                    Name = product.Name,
                    Slug = product.Slug,
                    ShortDescription = product.ShortDescription,
                    Price = product.Price,
                    Currency = product.Currency,
                    Unit = product.Unit,
                    Brand = product.Brand,
                    IsFeatured = product.IsFeatured,
                    IsBestseller = product.IsBestseller,
                    IsNew = product.IsNew,
                    ViewCount = product.ViewCount,
                    InquiryCount = product.InquiryCount,
                    CreatedAt = product.CreatedAt
                };

                return ServiceResult<ProductDto>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product");
                return ServiceResult<ProductDto>.ErrorResult("Failed to update product");
            }
        }

        public async Task<ServiceResult<bool>> DeleteProductAsync(int id)
        {
            try
            {
                var product = await _context.Products.FindAsync(id);
                if (product == null)
                {
                    return ServiceResult<bool>.ErrorResult("Product not found");
                }

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();

                // Clear cache
                await _cacheService.RemoveAsync($"product_detail_{id}_en");
                await _cacheService.RemoveAsync($"product_detail_{id}_ar");
                await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_en");
                await _cacheService.RemoveAsync($"product_detail_slug_{product.Slug}_ar");

                return ServiceResult<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product");
                return ServiceResult<bool>.ErrorResult("Failed to delete product");
            }
        }

        private ProductDetailDto MapToProductDetailDto(Product product, string language)
        {
            return new ProductDetailDto
            {
                Id = product.Id,
                Name = language == "ar" ? product.NameAr ?? product.Name : product.Name,
                Slug = product.Slug,
                ShortDescription = language == "ar" ? product.ShortDescriptionAr ?? product.ShortDescription : product.ShortDescription,
                Description = language == "ar" ? product.DescriptionAr ?? product.Description : product.Description,
                TechnicalSpecs = !string.IsNullOrEmpty(product.TechnicalSpecs) 
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(
                        language == "ar" ? product.TechnicalSpecsAr ?? product.TechnicalSpecs : product.TechnicalSpecs)
                    : null,
                Features = !string.IsNullOrEmpty(product.Features) 
                    ? JsonSerializer.Deserialize<List<string>>(
                        language == "ar" ? product.FeaturesAr ?? product.Features : product.Features)
                    : null,
                Applications = !string.IsNullOrEmpty(product.Applications) 
                    ? JsonSerializer.Deserialize<List<string>>(
                        language == "ar" ? product.ApplicationsAr ?? product.Applications : product.Applications)
                    : null,
                Benefits = !string.IsNullOrEmpty(product.Benefits) 
                    ? JsonSerializer.Deserialize<List<string>>(
                        language == "ar" ? product.BenefitsAr ?? product.Benefits : product.Benefits)
                    : null,
                UsageInstructions = language == "ar" ? product.UsageInstructionsAr ?? product.UsageInstructions : product.UsageInstructions,
                SafetyInfo = language == "ar" ? product.SafetyInfoAr ?? product.SafetyInfo : product.SafetyInfo,
                StorageConditions = language == "ar" ? product.StorageConditionsAr ?? product.StorageConditions : product.StorageConditions,
                PackagingInfo = !string.IsNullOrEmpty(product.PackagingInfo) 
                    ? JsonSerializer.Deserialize<Dictionary<string, object>>(product.PackagingInfo)
                    : null,
                CoverageArea = product.CoverageArea,
                ShelfLife = product.ShelfLife,
                Certifications = !string.IsNullOrEmpty(product.Certifications) 
                    ? JsonSerializer.Deserialize<List<string>>(product.Certifications)
                    : null,
                Price = product.Price,
                Currency = product.Currency,
                MinOrderQty = product.MinOrderQty,
                Unit = product.Unit,
                Weight = product.Weight,
                Dimensions = product.Dimensions,
                Color = product.Color,
                Brand = product.Brand,
                OriginCountry = product.OriginCountry,
                Barcode = product.Barcode,
                QRCode = product.QRCode,
                PdfDatasheet = product.PdfDatasheet,
                VideoUrl = product.VideoUrl,
                SeoTitle = language == "ar" ? product.SeoTitleAr ?? product.SeoTitle : product.SeoTitle,
                SeoDescription = language == "ar" ? product.SeoDescriptionAr ?? product.SeoDescription : product.SeoDescription,
                SeoKeywords = language == "ar" ? product.SeoKeywordsAr ?? product.SeoKeywords : product.SeoKeywords,
                IsFeatured = product.IsFeatured,
                IsBestseller = product.IsBestseller,
                IsNew = product.IsNew,
                ViewCount = product.ViewCount,
                InquiryCount = product.InquiryCount,
                Category = product.Category != null ? new CategoryDto
                {
                    Id = product.Category.Id,
                    Name = language == "ar" ? product.Category.NameAr ?? product.Category.Name : product.Category.Name,
                    Slug = product.Category.Slug,
                    Description = language == "ar" ? product.Category.DescriptionAr ?? product.Category.Description : product.Category.Description,
                    Image = product.Category.Image,
                    Icon = product.Category.Icon
                } : null,
                Images = product.Images.Select(i => new ImageDto
                {
                    Id = i.Id,
                    FileUrl = i.ImageUrl,
                    AltText = language == "ar" ? i.AltTextAr ?? i.AltText : i.AltText,
                    Caption = language == "ar" ? i.CaptionAr ?? i.Caption : i.Caption,
                    Width = i.Width,
                    Height = i.Height,
                    FileSize = i.FileSize ?? 0,
                    SortOrder = i.SortOrder,
                    IsPrimary = i.IsPrimary
                }).ToList(),
                CreatedAt = product.CreatedAt,
                UpdatedAt = product.UpdatedAt
            };
        }
    }
}
