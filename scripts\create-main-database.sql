-- Create comprehensive database for construction website with multilingual support

-- Companies/Settings table
CREATE TABLE IF NOT EXISTS companies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    logo VARCHAR(500),
    favicon VARCHAR(500),
    email VARCHAR(255),
    phone VARCHAR(50),
    whatsapp VARCHAR(50),
    address TEXT,
    address_ar TEXT,
    website VARCHAR(255),
    established_year INT,
    license_number VARCHAR(100),
    tax_number VARCHAR(100),
    business_hours JSON,
    social_media JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Product Categories
CREATE TABLE IF NOT EXISTS product_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    image VARCHAR(500),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_sort_order (sort_order)
);

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    sku VARCHAR(100) UNIQUE,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    short_description TEXT,
    short_description_ar TEXT,
    description TEXT,
    description_ar TEXT,
    technical_specs JSON,
    technical_specs_ar JSON,
    features JSON,
    features_ar JSON,
    applications JSON,
    applications_ar JSON,
    benefits JSON,
    benefits_ar JSON,
    usage_instructions TEXT,
    usage_instructions_ar TEXT,
    safety_info TEXT,
    safety_info_ar TEXT,
    storage_conditions TEXT,
    storage_conditions_ar TEXT,
    packaging_info JSON,
    coverage_area VARCHAR(100),
    shelf_life VARCHAR(100),
    certifications JSON,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'AED',
    min_order_qty INT DEFAULT 1,
    unit VARCHAR(50),
    weight DECIMAL(8,2),
    dimensions VARCHAR(100),
    color VARCHAR(100),
    brand VARCHAR(100),
    origin_country VARCHAR(100),
    barcode VARCHAR(100),
    qr_code VARCHAR(500),
    pdf_datasheet VARCHAR(500),
    video_url VARCHAR(500),
    seo_title VARCHAR(255),
    seo_title_ar VARCHAR(255),
    seo_description TEXT,
    seo_description_ar TEXT,
    seo_keywords TEXT,
    seo_keywords_ar TEXT,
    status ENUM('active', 'inactive', 'draft', 'discontinued') DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    is_bestseller BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    inquiry_count INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    FULLTEXT idx_search (name, name_ar, description, description_ar)
);

-- Product Images
CREATE TABLE IF NOT EXISTS product_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('thumbnail', 'gallery', 'hero', 'detail', 'technical') DEFAULT 'gallery',
    alt_text VARCHAR(255),
    alt_text_ar VARCHAR(255),
    caption TEXT,
    caption_ar TEXT,
    width INT,
    height INT,
    file_size INT,
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_type (image_type),
    INDEX idx_sort_order (sort_order)
);

-- Service Categories
CREATE TABLE IF NOT EXISTS service_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    image VARCHAR(500),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_sort_order (sort_order)
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    short_description TEXT,
    short_description_ar TEXT,
    description TEXT,
    description_ar TEXT,
    features JSON,
    features_ar JSON,
    process_steps JSON,
    process_steps_ar JSON,
    applications JSON,
    applications_ar JSON,
    benefits JSON,
    benefits_ar JSON,
    equipment_used JSON,
    equipment_used_ar JSON,
    materials_used JSON,
    materials_used_ar JSON,
    duration_estimate VARCHAR(100),
    duration_estimate_ar VARCHAR(100),
    warranty_info TEXT,
    warranty_info_ar TEXT,
    certifications JSON,
    service_areas JSON,
    pricing_model ENUM('fixed', 'hourly', 'sqm', 'project', 'quote') DEFAULT 'quote',
    base_price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'AED',
    unit VARCHAR(50),
    min_project_size VARCHAR(100),
    lead_time VARCHAR(100),
    availability ENUM('24/7', 'business_hours', 'appointment', 'seasonal') DEFAULT 'business_hours',
    seo_title VARCHAR(255),
    seo_title_ar VARCHAR(255),
    seo_description TEXT,
    seo_description_ar TEXT,
    seo_keywords TEXT,
    seo_keywords_ar TEXT,
    status ENUM('active', 'inactive', 'draft', 'seasonal') DEFAULT 'active',
    is_featured BOOLEAN DEFAULT FALSE,
    is_emergency BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    inquiry_count INT DEFAULT 0,
    completion_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.00,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES service_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    FULLTEXT idx_search (name, name_ar, description, description_ar)
);

-- Service Images
CREATE TABLE IF NOT EXISTS service_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('icon', 'banner', 'gallery', 'thumbnail', 'process', 'equipment') DEFAULT 'gallery',
    alt_text VARCHAR(255),
    alt_text_ar VARCHAR(255),
    caption TEXT,
    caption_ar TEXT,
    width INT,
    height INT,
    file_size INT,
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    INDEX idx_service_id (service_id),
    INDEX idx_type (image_type),
    INDEX idx_sort_order (sort_order)
);

-- Project Categories
CREATE TABLE IF NOT EXISTS project_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    description_ar TEXT,
    image VARCHAR(500),
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_slug (slug),
    INDEX idx_sort_order (sort_order)
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    short_description TEXT,
    short_description_ar TEXT,
    description TEXT,
    description_ar TEXT,
    client_name VARCHAR(255),
    client_type ENUM('government', 'private', 'individual', 'corporate') DEFAULT 'private',
    location VARCHAR(255),
    location_ar VARCHAR(255),
    city VARCHAR(100),
    country VARCHAR(100) DEFAULT 'IRAQ',
    coordinates JSON,
    project_type ENUM('commercial', 'residential', 'industrial', 'infrastructure', 'renovation') NOT NULL,
    project_size VARCHAR(100),
    project_value DECIMAL(12,2),
    currency VARCHAR(3) DEFAULT 'AED',
    start_date DATE,
    end_date DATE,
    duration_months INT,
    project_status ENUM('planning', 'ongoing', 'completed', 'on_hold', 'cancelled') DEFAULT 'completed',
    completion_percentage INT DEFAULT 100,
    services_provided JSON,
    services_provided_ar JSON,
    products_used JSON,
    products_used_ar JSON,
    challenges_faced TEXT,
    challenges_faced_ar TEXT,
    solutions_implemented TEXT,
    solutions_implemented_ar TEXT,
    results_achieved TEXT,
    results_achieved_ar TEXT,
    technologies_used JSON,
    technologies_used_ar JSON,
    team_size INT,
    project_manager VARCHAR(255),
    contractor VARCHAR(255),
    architect VARCHAR(255),
    engineer VARCHAR(255),
    certifications_earned JSON,
    awards_received JSON,
    media_coverage JSON,
    client_testimonial TEXT,
    client_testimonial_ar TEXT,
    client_rating DECIMAL(3,2),
    environmental_impact TEXT,
    environmental_impact_ar TEXT,
    sustainability_features JSON,
    sustainability_features_ar JSON,
    video_url VARCHAR(500),
    virtual_tour_url VARCHAR(500),
    case_study_pdf VARCHAR(500),
    seo_title VARCHAR(255),
    seo_title_ar VARCHAR(255),
    seo_description TEXT,
    seo_description_ar TEXT,
    seo_keywords TEXT,
    seo_keywords_ar TEXT,
    is_featured BOOLEAN DEFAULT FALSE,
    is_showcase BOOLEAN DEFAULT FALSE,
    view_count INT DEFAULT 0,
    inquiry_count INT DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES project_categories(id) ON DELETE CASCADE,
    INDEX idx_category (category_id),
    INDEX idx_slug (slug),
    INDEX idx_status (project_status),
    INDEX idx_type (project_type),
    INDEX idx_location (city, country),
    INDEX idx_date (start_date, end_date),
    INDEX idx_featured (is_featured),
    INDEX idx_sort_order (sort_order),
    FULLTEXT idx_search (name, name_ar, description, description_ar, location, location_ar)
);

-- Project Images
CREATE TABLE IF NOT EXISTS project_images (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type ENUM('before', 'after', 'progress', 'thumbnail', 'gallery', 'aerial', 'detail', 'team') DEFAULT 'gallery',
    phase ENUM('planning', 'foundation', 'structure', 'finishing', 'completion') DEFAULT 'completion',
    alt_text VARCHAR(255),
    alt_text_ar VARCHAR(255),
    caption TEXT,
    caption_ar TEXT,
    taken_date DATE,
    photographer VARCHAR(255),
    width INT,
    height INT,
    file_size INT,
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    is_before_after BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_id (project_id),
    INDEX idx_type (image_type),
    INDEX idx_phase (phase),
    INDEX idx_sort_order (sort_order)
);

-- Contact Messages
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    company VARCHAR(255),
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    inquiry_type ENUM('general', 'product', 'service', 'project', 'quote', 'support', 'partnership') DEFAULT 'general',
    related_id INT NULL,
    related_type ENUM('product', 'service', 'project') NULL,
    preferred_contact ENUM('email', 'phone', 'whatsapp') DEFAULT 'email',
    preferred_language ENUM('en', 'ar') DEFAULT 'en',
    budget_range VARCHAR(100),
    timeline VARCHAR(100),
    location VARCHAR(255),
    source VARCHAR(100),
    utm_source VARCHAR(100),
    utm_medium VARCHAR(100),
    utm_campaign VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('unread', 'read', 'replied', 'closed', 'spam') DEFAULT 'unread',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to VARCHAR(255),
    internal_notes TEXT,
    response_sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_type (inquiry_type),
    INDEX idx_date (created_at),
    INDEX idx_email (email),
    FULLTEXT idx_search (name, email, subject, message)
);

-- Translations table for dynamic content
CREATE TABLE IF NOT EXISTS translations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    translation_key VARCHAR(255) NOT NULL,
    language_code VARCHAR(5) NOT NULL,
    translation_value TEXT NOT NULL,
    context VARCHAR(100),
    page VARCHAR(100),
    section VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key_lang (translation_key, language_code),
    INDEX idx_key (translation_key),
    INDEX idx_lang (language_code),
    INDEX idx_page (page),
    INDEX idx_section (section)
);

-- SEO Meta Data
CREATE TABLE IF NOT EXISTS seo_meta (
    id INT PRIMARY KEY AUTO_INCREMENT,
    page_type ENUM('home', 'about', 'products', 'product', 'services', 'service', 'projects', 'project', 'contact', 'category') NOT NULL,
    page_id INT NULL,
    language_code VARCHAR(5) NOT NULL DEFAULT 'en',
    title VARCHAR(255),
    description TEXT,
    keywords TEXT,
    og_title VARCHAR(255),
    og_description TEXT,
    og_image VARCHAR(500),
    twitter_title VARCHAR(255),
    twitter_description TEXT,
    twitter_image VARCHAR(500),
    canonical_url VARCHAR(500),
    robots VARCHAR(100) DEFAULT 'index,follow',
    schema_markup JSON,
    custom_meta JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page_lang (page_type, page_id, language_code),
    INDEX idx_page_type (page_type),
    INDEX idx_language (language_code)
);

-- Website Settings
CREATE TABLE IF NOT EXISTS website_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_value_ar TEXT,
    data_type ENUM('string', 'text', 'number', 'boolean', 'json', 'image', 'url') DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_category (category)
);

-- Insert default categories
INSERT INTO product_categories (name, name_ar, slug, description, description_ar, icon, sort_order) VALUES
('Concrete Admixtures', 'إضافات الخرسانة', 'concrete-admixtures', 'High-performance concrete additives and admixtures', 'إضافات ومحسنات الخرسانة عالية الأداء', 'Hammer', 1),
('Repairing & Injection', 'الإصلاح والحقن', 'repairing-injection', 'Concrete repair and injection materials', 'مواد إصلاح وحقن الخرسانة', 'Wrench', 2),
('Waterproofing & Sealing', 'العزل المائي والختم', 'waterproofing-sealing', 'Waterproofing membranes and sealing solutions', 'أغشية العزل المائي وحلول الختم', 'Droplets', 3),
('Tile Adhesive & Grouting', 'لاصق البلاط والجراوت', 'tile-adhesive-grouting', 'Tile adhesives and grouting materials', 'لواصق البلاط ومواد الجراوت', 'Grid3x3', 4),
('Epoxy Systems & Painting', 'أنظمة الإيبوكسي والطلاء', 'epoxy-systems-painting', 'Epoxy coatings and painting systems', 'طلاءات الإيبوكسي وأنظمة الطلاء', 'Paintbrush', 5);

INSERT INTO service_categories (name, name_ar, slug, description, description_ar, icon, sort_order) VALUES
('Construction Services', 'خدمات البناء', 'construction-services', 'Professional construction and building services', 'خدمات البناء والتشييد المهنية', 'Building', 1),
('Repair & Maintenance', 'الإصلاح والصيانة', 'repair-maintenance', 'Building repair and maintenance services', 'خدمات إصلاح وصيانة المباني', 'Wrench', 2),
('Consultation', 'الاستشارات', 'consultation', 'Technical consultation and advisory services', 'خدمات الاستشارات الفنية والإرشادية', 'Users', 3);

INSERT INTO project_categories (name, name_ar, slug, description, description_ar, icon, sort_order) VALUES
('Commercial', 'تجاري', 'commercial', 'Commercial building projects', 'مشاريع المباني التجارية', 'Building2', 1),
('Residential', 'سكني', 'residential', 'Residential construction projects', 'مشاريع البناء السكني', 'Home', 2),
('Industrial', 'صناعي', 'industrial', 'Industrial facility projects', 'مشاريع المرافق الصناعية', 'Factory', 3),
('Infrastructure', 'البنية التحتية', 'infrastructure', 'Infrastructure development projects', 'مشاريع تطوير البنية التحتية', 'Road', 4);

-- Insert default translations
INSERT INTO translations (translation_key, language_code, translation_value, context, page, section) VALUES
-- Navigation
('nav.home', 'en', 'Home', 'navigation', 'global', 'header'),
('nav.home', 'ar', 'الرئيسية', 'navigation', 'global', 'header'),
('nav.about', 'en', 'About', 'navigation', 'global', 'header'),
('nav.about', 'ar', 'من نحن', 'navigation', 'global', 'header'),
('nav.products', 'en', 'Products', 'navigation', 'global', 'header'),
('nav.products', 'ar', 'المنتجات', 'navigation', 'global', 'header'),
('nav.services', 'en', 'Services', 'navigation', 'global', 'header'),
('nav.services', 'ar', 'الخدمات', 'navigation', 'global', 'header'),
('nav.projects', 'en', 'Projects', 'navigation', 'global', 'header'),
('nav.projects', 'ar', 'المشاريع', 'navigation', 'global', 'header'),
('nav.contact', 'en', 'Contact', 'navigation', 'global', 'header'),
('nav.contact', 'ar', 'اتصل بنا', 'navigation', 'global', 'header'),

-- Common buttons
('btn.learn_more', 'en', 'Learn More', 'button', 'global', 'common'),
('btn.learn_more', 'ar', 'اعرف المزيد', 'button', 'global', 'common'),
('btn.contact_us', 'en', 'Contact Us', 'button', 'global', 'common'),
('btn.contact_us', 'ar', 'اتصل بنا', 'button', 'global', 'common'),
('btn.get_quote', 'en', 'Get Quote', 'button', 'global', 'common'),
('btn.get_quote', 'ar', 'احصل على عرض سعر', 'button', 'global', 'common'),
('btn.view_all', 'en', 'View All', 'button', 'global', 'common'),
('btn.view_all', 'ar', 'عرض الكل', 'button', 'global', 'common'),
('btn.read_more', 'en', 'Read More', 'button', 'global', 'common'),
('btn.read_more', 'ar', 'اقرأ المزيد', 'button', 'global', 'common'),

-- Home page
('home.hero.title', 'en', 'Premium Construction Materials', 'heading', 'home', 'hero'),
('home.hero.title', 'ar', 'مواد البناء المتميزة', 'heading', 'home', 'hero'),
('home.hero.subtitle', 'en', 'Building Excellence Since 1995', 'text', 'home', 'hero'),
('home.hero.subtitle', 'ar', 'نبني التميز منذ عام 1995', 'text', 'home', 'hero'),
('home.hero.description', 'en', 'Professional-grade construction materials and expert services for your building projects', 'text', 'home', 'hero'),
('home.hero.description', 'ar', 'مواد بناء احترافية وخدمات خبيرة لمشاريع البناء الخاصة بك', 'text', 'home', 'hero'),

-- Footer
('footer.company_description', 'en', 'Professional construction materials and services company serving the IRAQ since 1995.', 'text', 'global', 'footer'),
('footer.company_description', 'ar', 'شركة مواد وخدمات البناء المهنية التي تخدم دولة الإمارات منذ عام 1995.', 'text', 'global', 'footer'),
('footer.quick_links', 'en', 'Quick Links', 'heading', 'global', 'footer'),
('footer.quick_links', 'ar', 'روابط سريعة', 'heading', 'global', 'footer'),
('footer.product_categories', 'en', 'Product Categories', 'heading', 'global', 'footer'),
('footer.product_categories', 'ar', 'فئات المنتجات', 'heading', 'global', 'footer'),
('footer.business_hours', 'en', 'Business Hours', 'heading', 'global', 'footer'),
('footer.business_hours', 'ar', 'ساعات العمل', 'heading', 'global', 'footer'),

-- Contact form
('contact.form.name', 'en', 'Full Name', 'label', 'contact', 'form'),
('contact.form.name', 'ar', 'الاسم الكامل', 'label', 'contact', 'form'),
('contact.form.email', 'en', 'Email Address', 'label', 'contact', 'form'),
('contact.form.email', 'ar', 'البريد الإلكتروني', 'label', 'contact', 'form'),
('contact.form.phone', 'en', 'Phone Number', 'label', 'contact', 'form'),
('contact.form.phone', 'ar', 'رقم الهاتف', 'label', 'contact', 'form'),
('contact.form.subject', 'en', 'Subject', 'label', 'contact', 'form'),
('contact.form.subject', 'ar', 'الموضوع', 'label', 'contact', 'form'),
('contact.form.message', 'en', 'Message', 'label', 'contact', 'form'),
('contact.form.message', 'ar', 'الرسالة', 'label', 'contact', 'form'),
('contact.form.send', 'en', 'Send Message', 'button', 'contact', 'form'),
('contact.form.send', 'ar', 'إرسال الرسالة', 'button', 'contact', 'form');

-- Insert default website settings
INSERT INTO website_settings (setting_key, setting_value, setting_value_ar, data_type, category, description) VALUES
('site_name', 'DRYLEX  Materials & Services', 'بيلد تك للمواد والخدمات', 'string', 'general', 'Website name'),
('site_tagline', 'Building Excellence Since 1995', 'نبني التميز منذ عام 1995', 'string', 'general', 'Website tagline'),
('company_phone', '+971 50 123 4567', '+971 50 123 4567', 'string', 'contact', 'Primary phone number'),
('company_email', 'info@DRYLEX .ae', 'info@DRYLEX .ae', 'string', 'contact', 'Primary email address'),
('company_whatsapp', '+971501234567', '+971501234567', 'string', 'contact', 'WhatsApp number'),
('company_address', 'Industrial Area 1, Sharjah, IRAQ', 'المنطقة الصناعية 1، الشارقة، الإمارات', 'text', 'contact', 'Company address'),
('default_language', 'en', 'en', 'string', 'localization', 'Default website language'),
('supported_languages', '["en", "ar"]', '["en", "ar"]', 'json', 'localization', 'Supported languages'),
('rtl_languages', '["ar"]', '["ar"]', 'json', 'localization', 'Right-to-left languages'),
('currency', 'AED', 'AED', 'string', 'general', 'Default currency'),
('timezone', 'Asia/Dubai', 'Asia/Dubai', 'string', 'general', 'Default timezone');
