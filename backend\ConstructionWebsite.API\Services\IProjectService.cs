using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Services
{
    public interface IProjectService
    {
        Task<IEnumerable<Project>> GetAllProjectsAsync();
        Task<Project?> GetProjectByIdAsync(int id);
        Task<Project> CreateProjectAsync(Project project);
        Task<Project?> UpdateProjectAsync(int id, Project project);
        Task<bool> DeleteProjectAsync(int id);
        Task<IEnumerable<Project>> GetProjectsByStatusAsync(string status);
        Task<IEnumerable<Project>> GetProjectsByTypeAsync(string type);
        Task<IEnumerable<Project>> SearchProjectsAsync(string searchTerm);
        Task<bool> ProjectExistsAsync(int id);
        
        // Additional methods expected by controller
        Task<PagedResult<ProjectDto>> GetProjectsAsync(
            string? search = null,
            int? categoryId = null,
            string? status = null,
            bool? isFeatured = null,
            string? language = "en",
            int page = 1,
            int pageSize = 12,
            string? sortBy = "createdAt",
            string? sortOrder = "desc");
        Task<ProjectDetailDto?> GetProjectBySlugAsync(string slug, string language = "en");
        Task<ProjectDetailDto?> GetProjectByIdAsync(int id, string language = "en");
        Task<List<ProjectDto>> GetRelatedProjectsAsync(int id, string language = "en", int limit = 3);
        Task IncrementViewCountAsync(int id);
        Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(int id, CreateProjectInquiryDto inquiryDto, string? clientIp, string? userAgent);
        Task<List<CategoryDto>> GetCategoriesAsync(string language = "en");
        Task<ServiceResult<ProjectDto>> CreateProjectAsync(CreateProjectDto projectDto);
        Task<ServiceResult<ProjectDto>> UpdateProjectAsync(int id, UpdateProjectDto projectDto);
        Task<ServiceResult<bool>> DeleteProjectAsync(int id, bool isAdmin = true);
    }
}