"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Users,
  Award,
  Heart,
  Shield,
  Lightbulb,
  Globe,
  Calendar,
  Phone,
  Mail,
  CheckCircle,
  Star,
  Building,
  Truck,
  Wrench,
} from "lucide-react"
import Image from "next/image"
import { AnimatedSection } from "@/components/animated-section"
import { StaggerContainer } from "@/components/stagger-container"
import { AnimatedCard } from "@/components/animated-card"
import { CountUpAnimation } from "@/components/count-up-animation"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import Head from "next/head"


export default function AboutPage() {
  const [activeTab, setActiveTab] = useState("story")

  // JSON-LD structured data for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex construction materials in Iraq, offering high-performance waterproofing systems, concrete admixtures, repair materials, and construction chemicals since 1995.",
    "foundingDate": "1995",
    "founders": [
      {
        "@type": "Person",
        "name": "Ahmed Al-Rashid"
      }
    ],
    "numberOfEmployees": "50-100",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Alshimoukh district, district 205, zukak 68, building 40",
      "addressLocality": "Nassiriya",
      "addressRegion": "Thi Qar",
      "addressCountry": {
        "@type": "Country",
        "name": "Iraq"
      },
      "postalCode": "64001"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+9647867100886",
      "contactType": "sales",
      "areaServed": "IQ",
      "availableLanguage": ["en", "ar"],
      "email": "<EMAIL>"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylexiraq",
      "https://www.youtube.com/@drylexiraq"
    ],
    "openingHours": [
      "Mo-Fr 08:00-18:00",
      "Sa 08:00-12:00",
      "Su closed"
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "150"
    },
    "makesOffer": {
      "@type": "OfferCatalog",
      "name": "Construction Materials Catalog",
      "url": "https://drylexiraq.com/products",
      "numberOfItems": "50"
    },
    "award": [
      "IRAQ Construction Excellence Award 2023",
      "Best Materials Supplier Award 2022",
      "Quality Excellence Certificate ISO 9001:2015"
    ],
    "brand": {
      "@type": "Brand",
      "name": "Drylex",
      "slogan": "Building Excellence Since 1995",
      "url": "https://drylexiraq.com"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Drylex Product Catalog",
      "url": "https://drylexiraq.com/products"
    },
    "image": "https://drylexiraq.com/logo.png",
    "knowsAbout": [
      "waterproofing systems",
      "concrete admixtures",
      "construction chemicals",
      "repair materials",
      "building materials"
    ],
    "memberOf": [
      {
        "@type": "Organization",
        "name": "Drylex Global"
      }
    ],
    "areaServed": {
      "@type": "City",
      "name": "Nassiriya",
      "sameAs": "https://en.wikipedia.org/wiki/Nassiriya"
    },
    "hasMap": "https://maps.google.com/?cid=**********",
    "paymentAccepted": [
      "Credit Card",
      "Bank Transfer",
      "Cash"
    ],
    "priceRange": "$$$"
  }

  // JSON-LD structured data for AboutPage
  const aboutPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "AboutPage",
    "headline": "About DRYLEX Iraq",
    "description": "Learn about DRYLEX Iraq, the authorized distributor of Drylex construction materials in Iraq. Providing premium waterproofing systems, concrete admixtures, repair materials, and construction chemicals since 1995.",
    "url": "https://drylexiraq.com/about",
    "mainContentOfPage": {
      "@type": "WebPageElement",
      "headline": "About DRYLEX Iraq",
      "description": "Learn about DRYLEX Iraq, the authorized distributor of Drylex construction materials in Iraq. Providing premium waterproofing systems, concrete admixtures, repair materials, and construction chemicals since 1995."
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "About Us",
          "item": "https://drylexiraq.com/about"
        }
      ]
    },
    "mainEntity": organizationJsonLd
  }

  const stats = [
    { icon: Calendar, label: "Years of Experience", value: 29, suffix: "+" },
    { icon: Users, label: "Happy Clients", value: 500, suffix: "+" },
    { icon: Building, label: "Projects Completed", value: 1000, suffix: "+" },
    { icon: Award, label: "Industry Awards", value: 15, suffix: "" },
  ]

  const values = [
    {
      icon: Shield,
      title: "Quality Assurance",
      description:
        "We maintain the highest standards in all our products and services, ensuring reliability and excellence in every project.",
    },
    {
      icon: Heart,
      title: "Customer Focus",
      description:
        "Our clients are at the heart of everything we do. We build lasting relationships through exceptional service and support.",
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description:
        "We continuously invest in new technologies and solutions to stay ahead in the construction materials industry.",
    },
    {
      icon: Globe,
      title: "Sustainability",
      description:
        "Committed to environmentally responsible practices and sustainable construction solutions for a better future.",
    },
  ]

  const milestones = [
    {
      year: "1995",
      title: "Company Founded",
      description: "DRYLEX  Materials & Services established in Sharjah, IRAQ",
    },
    {
      year: "2000",
      title: "First Major Contract",
      description: "Secured first major government infrastructure project",
    },
    {
      year: "2005",
      title: "ISO Certification",
      description: "Achieved ISO 9001:2000 quality management certification",
    },
    {
      year: "2010",
      title: "Expansion",
      description: "Opened new warehouse and expanded product range",
    },
    {
      year: "2015",
      title: "Technology Integration",
      description: "Implemented advanced inventory and project management systems",
    },
    {
      year: "2020",
      title: "Digital Transformation",
      description: "Launched online platform and digital services",
    },
    {
      year: "2023",
      title: "Excellence Award",
      description: "Received IRAQ Construction Excellence Award",
    },
    {
      year: "2024",
      title: "Drylex Partnership",
      description: "Became authorized distributor of Drylex waterproofing and repair systems",
    },
  ]

  const team = [
    {
      name: "Ahmed Al-Rashid",
      nameAr: "أحمد الراشد",
      position: "Chief Executive Officer",
      experience: "25+ years",
      image: "/placeholder.svg?height=300&width=300",
      description: "Visionary leader with extensive experience in construction materials industry across Middle East.",
    },
    {
      name: "Sarah Johnson",
      nameAr: "سارة جونسون",
      position: "Technical Director",
      experience: "20+ years",
      image: "/placeholder.svg?height=300&width=300",
      description: "Expert in construction chemicals and materials with international certifications.",
    },
    {
      name: "Mohammed Hassan",
      nameAr: "محمد حسن",
      position: "Operations Manager",
      experience: "18+ years",
      image: "/placeholder.svg?height=300&width=300",
      description: "Specialist in project management and logistics with proven track record.",
    },
    {
      name: "Fatima Al-Zahra",
      nameAr: "فاطمة الزهراء",
      position: "Quality Assurance Manager",
      experience: "15+ years",
      image: "/placeholder.svg?height=300&width=300",
      description: "Quality control expert ensuring highest standards in all products and services.",
    },
  ]

  const certifications = [
    {
      name: "ISO 9001:2015",
      description: "Quality Management System",
      icon: Award,
    },
    {
      name: "ISO 14001:2015",
      description: "Environmental Management",
      icon: Globe,
    },
    {
      name: "OHSAS 18001",
      description: "Occupational Health & Safety",
      icon: Shield,
    },
    {
      name: "IRAQ Quality Mark",
      description: "Emirates Quality Certification",
      icon: Star,
    },
  ]

  const services = [
    {
      icon: Truck,
      title: "Material Supply",
      description: "Comprehensive range of construction materials with reliable delivery",
    },
    {
      icon: Wrench,
      title: "Technical Support",
      description: "Expert consultation and on-site technical assistance",
    },
    {
      icon: CheckCircle,
      title: "Quality Control",
      description: "Rigorous testing and quality assurance for all products",
    },
    {
      icon: Users,
      title: "Training Programs",
      description: "Professional training for construction teams and contractors",
    },
  ]

  return (
    <>
      <Head>
        <title>About DRYLEX Iraq | Authorized Distributor</title>
        <meta
          name="description"
          content="Learn about DRYLEX Iraq, the authorized distributor of Drylex construction materials in Iraq. Providing premium waterproofing systems, concrete admixtures, repair materials, and construction chemicals since 1995."
        />
        <meta
          name="keywords"
          content="about Drylex Iraq, construction materials distributor, waterproofing solutions Iraq, concrete admixtures supplier, construction chemicals distributor, building materials company, Drylex authorized distributor, construction services provider, company history Iraq, construction industry expertise"
        />
        <link rel="canonical" href="https://drylexiraq.com/about" />
      </Head>
      {/* Organization structured data */}
      <script 
        type="application/ld+json" 
        dangerouslySetInnerHTML={{ 
          __html: JSON.stringify(organizationJsonLd) 
        }} 
      />
      {/* AboutPage structured data */}
      <script 
        type="application/ld+json" 
        dangerouslySetInnerHTML={{ 
          __html: JSON.stringify(aboutPageJsonLd) 
        }} 
      />

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Breadcrumbs */}
          <SEOBreadcrumbs items={[{ label: "About Us" }]} />

          {/* Header */}
          <AnimatedSection className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">About DRYLEX  IRAQ</h1>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Building Excellence Since 1995 - Your Trusted Partner in Construction Materials & Services Across IRAQ
            </p>
          </AnimatedSection>

          {/* Stats Section */}
          <AnimatedSection className="mb-16">
            <StaggerContainer className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <AnimatedCard key={index} className="text-center">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                      <stat.icon className="h-8 w-8 text-orange-600" />
                    </div>
                    <h3 className="text-3xl font-bold text-gray-900 mb-2">
                      <CountUpAnimation end={stat.value} suffix={stat.suffix} />
                    </h3>
                    <p className="text-gray-600 text-sm">{stat.label}</p>
                  </div>
                </AnimatedCard>
              ))}
            </StaggerContainer>
          </AnimatedSection>

          {/* Navigation Tabs */}
          <AnimatedSection className="mb-12">
            <div className="flex flex-wrap justify-center gap-4 mb-8">
              {[
                { id: "story", label: "Our Story" },
                { id: "values", label: "Our Values" },
                { id: "team", label: "Our Team" },
                { id: "certifications", label: "Certifications" },
              ].map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "outline"}
                  onClick={() => setActiveTab(tab.id)}
                  className={activeTab === tab.id ? "bg-orange-600 hover:bg-orange-700" : ""}
                >
                  {tab.label}
                </Button>
              ))}
            </div>
          </AnimatedSection>

          {/* Content Sections */}
          {activeTab === "story" && (
            <AnimatedSection>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
                <div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Journey Since 1995</h2>
                  <div className="space-y-4 text-gray-700 leading-relaxed">
                    <p>
                      DRYLEX  Materials & Services was founded in 1995 with a vision to become the IRAQ's most trusted
                      construction materials supplier. Starting as a small family business in Sharjah, we have grown to
                      become a leading name in the construction industry.
                    </p>
                    <p>
                      Over nearly three decades, we have built our reputation on quality, reliability, and exceptional
                      customer service. Our commitment to excellence has enabled us to work on some of the IRAQ's most
                      prestigious construction projects.
                    </p>
                    <p>
                      Today, we serve over 500 satisfied clients across the IRAQ, from individual contractors to major
                      construction companies, providing them with premium materials and expert technical support.
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <Image
                    src="/placeholder.svg?height=400&width=600"
                    alt="DRYLEX  IRAQ company building and facilities"
                    width={600}
                    height={400}
                    className="rounded-lg shadow-lg"
                  />
                </div>
              </div>

              {/* Timeline */}
              <div className="mb-16">
                <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Milestones</h3>
                <div className="relative">
                  <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-orange-200"></div>
                  <div className="space-y-8">
                    {milestones.map((milestone, index) => (
                      <AnimatedCard key={index} delay={index * 0.1}>
                        <div className={`flex items-center ${index % 2 === 0 ? "flex-row" : "flex-row-reverse"}`}>
                          <div className={`w-1/2 ${index % 2 === 0 ? "pr-8 text-right" : "pl-8 text-left"}`}>
                            <Card className="p-6">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge className="bg-orange-600">{milestone.year}</Badge>
                              </div>
                              <h4 className="font-bold text-lg mb-2">{milestone.title}</h4>
                              <p className="text-gray-600">{milestone.description}</p>
                            </Card>
                          </div>
                          <div className="relative z-10">
                            <div className="w-4 h-4 bg-orange-600 rounded-full border-4 border-white shadow-lg"></div>
                          </div>
                          <div className="w-1/2"></div>
                        </div>
                      </AnimatedCard>
                    ))}
                  </div>
                </div>
              </div>
            </AnimatedSection>
          )}

          {activeTab === "values" && (
            <AnimatedSection>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Core Values</h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  The principles that guide everything we do and define who we are as a company
                </p>
              </div>

              <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
                {values.map((value, index) => (
                  <AnimatedCard key={index} delay={index * 0.1}>
                    <Card className="h-full p-6 hover:shadow-lg transition-shadow">
                      <CardContent className="p-0">
                        <div className="flex items-start gap-4">
                          <div className="bg-orange-100 p-3 rounded-full">
                            <value.icon className="h-6 w-6 text-orange-600" />
                          </div>
                          <div>
                            <h3 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h3>
                            <p className="text-gray-600 leading-relaxed">{value.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </AnimatedCard>
                ))}
              </StaggerContainer>

              {/* Services Overview */}
              <div className="bg-white rounded-lg p-8 shadow-sm">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">What We Offer</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {services.map((service, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-orange-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                        <service.icon className="h-8 w-8 text-orange-600" />
                      </div>
                      <h4 className="font-semibold text-gray-900 mb-2">{service.title}</h4>
                      <p className="text-sm text-gray-600">{service.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </AnimatedSection>
          )}

          {activeTab === "team" && (
            <AnimatedSection>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Meet Our Expert Team</h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Experienced professionals dedicated to delivering excellence in construction materials and services
                </p>
              </div>

              <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {team.map((member, index) => (
                  <AnimatedCard key={index} delay={index * 0.1}>
                    <Card className="text-center hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="relative mb-4">
                          <Image
                            src={member.image || "/placeholder.svg"}
                            alt={`${member.name} - ${member.position} at DRYLEX  IRAQ`}
                            width={200}
                            height={200}
                            className="w-32 h-32 rounded-full mx-auto object-cover"
                          />
                        </div>
                        <h3 className="font-bold text-lg text-gray-900 mb-1">{member.name}</h3>
                        <p className="text-sm text-gray-500 mb-2">{member.nameAr}</p>
                        <p className="text-orange-600 font-medium mb-2">{member.position}</p>
                        <Badge variant="outline" className="mb-3">
                          {member.experience}
                        </Badge>
                        <p className="text-sm text-gray-600">{member.description}</p>
                      </CardContent>
                    </Card>
                  </AnimatedCard>
                ))}
              </StaggerContainer>
            </AnimatedSection>
          )}

          {activeTab === "certifications" && (
            <AnimatedSection>
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Certifications & Awards</h2>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                  Recognized for excellence in quality, safety, and environmental management
                </p>
              </div>

              <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                {certifications.map((cert, index) => (
                  <AnimatedCard key={index} delay={index * 0.1}>
                    <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                      <CardContent className="p-0">
                        <div className="bg-orange-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                          <cert.icon className="h-8 w-8 text-orange-600" />
                        </div>
                        <h3 className="font-bold text-lg text-gray-900 mb-2">{cert.name}</h3>
                        <p className="text-sm text-gray-600">{cert.description}</p>
                      </CardContent>
                    </Card>
                  </AnimatedCard>
                ))}
              </StaggerContainer>

              {/* Awards Section */}
              <div className="bg-white rounded-lg p-8 shadow-sm">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">Recent Awards & Recognition</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="bg-yellow-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <Award className="h-8 w-8 text-yellow-600" />
                    </div>
                    <h4 className="font-semibold mb-2">IRAQ Construction Excellence Award</h4>
                    <p className="text-sm text-gray-600">2023</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <Star className="h-8 w-8 text-blue-600" />
                    </div>
                    <h4 className="font-semibold mb-2">Best Materials Supplier</h4>
                    <p className="text-sm text-gray-600">2022</p>
                  </div>
                  <div className="text-center">
                    <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                      <Shield className="h-8 w-8 text-green-600" />
                    </div>
                    <h4 className="font-semibold mb-2">Safety Excellence Certificate</h4>
                    <p className="text-sm text-gray-600">2023</p>
                  </div>
                </div>
              </div>
            </AnimatedSection>
          )}

          {/* CTA Section */}
          <AnimatedSection className="mt-16 text-center bg-gradient-to-r from-orange-600 to-orange-700 rounded-lg p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">Ready to Work With Us?</h2>
            <p className="text-xl mb-6 opacity-90">
              Join over 500 satisfied clients who trust DRYLEX  for their construction material needs
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="bg-white text-orange-600 hover:bg-gray-100">
                <Phone className="h-5 w-5 mr-2" />
                Contact Us Today
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-orange-600 bg-transparent"
              >
                <Mail className="h-5 w-5 mr-2" />
                Request Quote
              </Button>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </>
  )
}
