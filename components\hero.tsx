"use client"

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useTranslation } from "./translation-provider"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"

type HeroSlide = {
  title: string
  subtitle?: string
  description?: string
  image?: string
  cta?: string
  href?: string
}

interface HeroProps {
  slides?: HeroSlide[]
}

export function Hero({ slides = [] }: HeroProps) {
  const { t, isRTL } = useTranslation()

  const features = [t("hero.features.quality"), t("hero.features.experience"), t("hero.features.support")]
  const ArrowIcon = isRTL ? ArrowLeft : ArrowRight

  return (
    <section className="relative py-16 lg:py-24 overflow-hidden">
      <div className="absolute inset-0 -z-10 bg-gradient-to-b from-white via-primary/5 to-white" />

      <div className="container mx-auto px-4">
        {slides.length > 0 ? (
          <Carousel className="relative">
            <CarouselContent>
              {slides.map((slide, idx) => (
                <CarouselItem key={idx}>
                  <div className="relative rounded-3xl overflow-hidden">
                    <div
                      className="absolute inset-0"
                      style={{
                        backgroundImage: `url(${slide.image || "/placeholder.svg?height=800&width=1600"})`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/20" />
                    <div className={`relative grid lg:grid-cols-2 gap-10 items-center p-8 lg:p-16 ${isRTL ? "lg:grid-flow-col-dense" : ""}`}>
                      <div className={`${isRTL ? "lg:col-start-2 text-right" : "text-left"}`}>
                        <div className="inline-block rounded-full bg-white/10 backdrop-blur px-3 py-1 text-xs font-medium text-white mb-4">
                          {slide.subtitle || t("hero.badge", "Trusted Construction Partner")}
                        </div>
                        <h1 className="text-4xl lg:text-6xl font-extrabold leading-tight">
                          <span className="bg-clip-text text-transparent bg-gradient-to-r from-white via-white to-white/70">
                            {slide.title || t("hero.title")}
                          </span>
                        </h1>
                        <p className="mt-4 text-lg lg:text-xl text-white/90 max-w-2xl ml-0">
                          {slide.description || t("hero.subtitle")}
                        </p>

                        <div className={`mt-8 flex flex-col sm:flex-row gap-4 ${isRTL ? "sm:flex-row-reverse" : ""}`}>
                          <Button size="lg" className="bg-primary hover:bg-primary/90 text-white">
                            {slide.cta || t("hero.cta.primary")}
                            <ArrowIcon className={`w-5 h-5 ${isRTL ? "mr-2" : "ml-2"}`} />
                          </Button>
                          <Button size="lg" variant="outline" className="border-white/40 text-white hover:bg-white/10">
                            {t("hero.cta.secondary")}
                          </Button>
                        </div>

                        <div className={`mt-10 grid grid-cols-1 sm:grid-cols-3 gap-4 ${isRTL ? "text-right" : "text-left"}`}>
                          {[25, 500, 100].map((val, i) => (
                            <div key={i} className="rounded-xl border border-white/10 bg-white/5 p-4 text-white/90">
                              <div className="text-3xl font-bold text-white">{val}{i === 2 ? "%" : "+"}</div>
                              <div className="text-sm opacity-80">
                                {i === 0 ? t("hero.stats.years") : i === 1 ? t("hero.stats.projects") : t("hero.stats.satisfaction")}
                              </div>
                            </div>
                          ))}
                        </div>
      </div>

                      <div className={`relative ${isRTL ? "lg:col-start-1" : ""}`}>
                        <div className="hidden lg:block" />
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-4 top-1/2 -translate-y-1/2 border-white/30 bg-white/20 text-white hover:bg-white/30" />
            <CarouselNext className="right-4 top-1/2 -translate-y-1/2 border-white/30 bg-white/20 text-white hover:bg-white/30" />
          </Carousel>
        ) : (
        <div className={`grid lg:grid-cols-2 gap-12 items-center ${isRTL ? "lg:grid-flow-col-dense" : ""}`}>
          <div className={`space-y-8 ${isRTL ? "lg:col-start-2 text-right" : "text-left"}`}>
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight">{t("hero.title")}</h1>
              <p className="text-xl text-gray-600 leading-relaxed">{t("hero.subtitle")}</p>
            </div>
            <div className="space-y-3">
              {features.map((feature, index) => (
                <div key={index} className={`flex items-center ${isRTL ? "flex-row-reverse" : ""} gap-3`}>
                  <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}
