"use client"

import { Phone, Mail } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "framer-motion"

interface ContactStickyProps {
  phone: string
  email: string
}

export function ContactSticky({ phone, email }: ContactStickyProps) {
  return (
    <motion.div
      className="fixed bottom-6 left-6 z-50 flex flex-col gap-2"
      initial={{ x: -100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ delay: 1.2, duration: 0.5 }}
    >
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          size="sm"
          variant="outline"
          className="bg-white/90 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all"
          onClick={() => window.open(`tel:${phone}`)}
        >
          <Phone className="h-4 w-4 mr-2" />
          Call Now
        </Button>
      </motion.div>
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          size="sm"
          variant="outline"
          className="bg-white/90 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all"
          onClick={() => window.open(`mailto:${email}`)}
        >
          <Mail className="h-4 w-4 mr-2" />
          Email
        </Button>
      </motion.div>
    </motion.div>
  )
}
