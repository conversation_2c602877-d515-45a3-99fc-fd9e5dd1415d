namespace ConstructionWebsite.API.DTOs
{
    public class ProjectDto
    {
        public int Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string? TitleAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Client { get; set; }
        public string? Location { get; set; }
        public string? LocationAr { get; set; }
        public string ProjectType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public decimal? Budget { get; set; }
        public string Currency { get; set; } = "USD";
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? Duration { get; set; }
        public decimal? Area { get; set; }
        public string? AreaUnit { get; set; }
        public bool IsFeatured { get; set; }
        public int SortOrder { get; set; }
        public int ViewCount { get; set; }
        public ImageDto? PrimaryImage { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class ProjectDetailDto : ProjectDto
    {
        public List<string> Challenges { get; set; } = new List<string>();
        public List<string> Solutions { get; set; } = new List<string>();
        public List<string> Technologies { get; set; } = new List<string>();
        public List<string> Materials { get; set; } = new List<string>();
        public List<string> TeamMembers { get; set; } = new List<string>();
        public string? ClientTestimonial { get; set; }
        public string? ClientTestimonialAr { get; set; }
        public List<ImageDto> Images { get; set; } = new List<ImageDto>();
        public string? VideoUrl { get; set; }
        public string? VirtualTourUrl { get; set; }
        public Dictionary<string, object>? ProjectStats { get; set; }
        public List<string> Awards { get; set; } = new List<string>();
        public string? CaseStudyUrl { get; set; }
    }

    public class CreateProjectDto
    {
        public string Title { get; set; } = string.Empty;
        public string? TitleAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Client { get; set; }
        public string? Location { get; set; }
        public string? LocationAr { get; set; }
        public string ProjectType { get; set; } = string.Empty;
        public string Status { get; set; } = "planning";
        public decimal? Budget { get; set; }
        public string Currency { get; set; } = "USD";
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? Duration { get; set; }
        public decimal? Area { get; set; }
        public string? AreaUnit { get; set; }
        public bool IsFeatured { get; set; }
        public int SortOrder { get; set; }
        public List<string> Challenges { get; set; } = new List<string>();
        public List<string> Solutions { get; set; } = new List<string>();
        public List<string> Technologies { get; set; } = new List<string>();
        public List<string> Materials { get; set; } = new List<string>();
        public List<string> TeamMembers { get; set; } = new List<string>();
        public string? ClientTestimonial { get; set; }
        public string? ClientTestimonialAr { get; set; }
        public string? VideoUrl { get; set; }
        public string? VirtualTourUrl { get; set; }
        public Dictionary<string, object>? ProjectStats { get; set; }
        public List<string> Awards { get; set; } = new List<string>();
        public string? CaseStudyUrl { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
    }

    public class UpdateProjectDto
    {
        public string? Title { get; set; }
        public string? TitleAr { get; set; }
        public string? Slug { get; set; }
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Client { get; set; }
        public string? Location { get; set; }
        public string? LocationAr { get; set; }
        public string? ProjectType { get; set; }
        public string? Status { get; set; }
        public decimal? Budget { get; set; }
        public string? Currency { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? CompletionDate { get; set; }
        public string? Duration { get; set; }
        public decimal? Area { get; set; }
        public string? AreaUnit { get; set; }
        public bool? IsFeatured { get; set; }
        public int? SortOrder { get; set; }
        public List<string>? Challenges { get; set; }
        public List<string>? Solutions { get; set; }
        public List<string>? Technologies { get; set; }
        public List<string>? Materials { get; set; }
        public List<string>? TeamMembers { get; set; }
        public string? ClientTestimonial { get; set; }
        public string? ClientTestimonialAr { get; set; }
        public string? VideoUrl { get; set; }
        public string? VirtualTourUrl { get; set; }
        public Dictionary<string, object>? ProjectStats { get; set; }
        public List<string>? Awards { get; set; }
        public string? CaseStudyUrl { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
    }
}