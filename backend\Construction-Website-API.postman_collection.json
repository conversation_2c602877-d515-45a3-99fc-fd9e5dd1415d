{"info": {"_postman_id": "construction-website-api", "name": "Construction Website API", "description": "Complete API collection for Construction Website backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "https://localhost:56266/api", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123!\",\n  \"confirmPassword\": \"Password123!\",\n  \"phoneNumber\": \"+1234567890\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.token) {", "        pm.environment.set('auth_token', response.token);", "        pm.collectionVariables.set('auth_token', response.token);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"Admin123!\",\n  \"newPassword\": \"NewPassword123!\",\n  \"confirmPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/change-password", "host": ["{{base_url}}"], "path": ["auth", "change-password"]}}}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/auth/forgot-password", "host": ["{{base_url}}"], "path": ["auth", "forgot-password"]}}}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"reset-token-here\",\n  \"newPassword\": \"NewPassword123!\",\n  \"confirmPassword\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/reset-password", "host": ["{{base_url}}"], "path": ["auth", "reset-password"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products?page=1&pageSize=12&language=en&sortBy=createdAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["products"], "query": [{"key": "search", "value": "", "disabled": true}, {"key": "categoryId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "isFeatured", "value": "", "disabled": true}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "12"}, {"key": "language", "value": "en"}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/1?language=en", "host": ["{{base_url}}"], "path": ["products", "1"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Product by Slug", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/slug/product-slug?language=en", "host": ["{{base_url}}"], "path": ["products", "slug", "product-slug"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Related Products", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/1/related?language=en&limit=4", "host": ["{{base_url}}"], "path": ["products", "1", "related"], "query": [{"key": "language", "value": "en"}, {"key": "limit", "value": "4"}]}}}, {"name": "Get Product Categories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/categories?language=en", "host": ["{{base_url}}"], "path": ["products", "categories"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Create Product Inquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"message\": \"I'm interested in this product. Please provide more details.\",\n  \"budget\": 5000,\n  \"urgency\": \"Medium\"\n}"}, "url": {"raw": "{{base_url}}/products/1/inquiries", "host": ["{{base_url}}"], "path": ["products", "1", "inquiries"]}}}, {"name": "Add to Wishlist", "request": {"method": "POST", "url": {"raw": "{{base_url}}/products/1/wishlist", "host": ["{{base_url}}"], "path": ["products", "1", "wishlist"]}}}, {"name": "Remove from Wishlist", "request": {"method": "DELETE", "url": {"raw": "{{base_url}}/products/1/wishlist", "host": ["{{base_url}}"], "path": ["products", "1", "wishlist"]}}}, {"name": "Check Wishlist Status", "request": {"method": "GET", "url": {"raw": "{{base_url}}/products/1/wishlist/status", "host": ["{{base_url}}"], "path": ["products", "1", "wishlist", "status"]}}}, {"name": "Create Product (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"New Product\",\n  \"nameAr\": \"???? ????\",\n  \"descriptionEn\": \"Product description in English\",\n  \"descriptionAr\": \"??? ?????? ????????\",\n  \"shortDescriptionEn\": \"Short description\",\n  \"shortDescriptionAr\": \"??? ?????\",\n  \"price\": 299.99,\n  \"salePrice\": 249.99,\n  \"categoryId\": 1,\n  \"status\": \"Active\",\n  \"isFeatured\": true,\n  \"tags\": [\"construction\", \"tool\"],\n  \"specifications\": {\n    \"material\": \"Steel\",\n    \"weight\": \"2kg\"\n  },\n  \"metaTitle\": \"New Product - Construction Tools\",\n  \"metaDescription\": \"High quality construction product\",\n  \"images\": [\n    {\n      \"url\": \"https://example.com/image1.jpg\",\n      \"altText\": \"Product image\",\n      \"isMain\": true\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/products", "host": ["{{base_url}}"], "path": ["products"]}}}, {"name": "Update Product (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"Updated Product Name\",\n  \"nameAr\": \"??? ?????? ??????\",\n  \"descriptionEn\": \"Updated product description\",\n  \"descriptionAr\": \"??? ?????? ??????\",\n  \"price\": 349.99,\n  \"salePrice\": 299.99,\n  \"status\": \"Active\",\n  \"isFeatured\": false\n}"}, "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}, {"name": "Delete Product (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "DELETE", "url": {"raw": "{{base_url}}/products/1", "host": ["{{base_url}}"], "path": ["products", "1"]}}}]}, {"name": "Services", "item": [{"name": "Get All Services", "request": {"method": "GET", "url": {"raw": "{{base_url}}/services?page=1&pageSize=12&language=en&sortBy=createdAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["services"], "query": [{"key": "search", "value": "", "disabled": true}, {"key": "categoryId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "isFeatured", "value": "", "disabled": true}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "12"}, {"key": "language", "value": "en"}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}}}, {"name": "Get Service by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/services/1?language=en", "host": ["{{base_url}}"], "path": ["services", "1"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Service by Slug", "request": {"method": "GET", "url": {"raw": "{{base_url}}/services/slug/service-slug?language=en", "host": ["{{base_url}}"], "path": ["services", "slug", "service-slug"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Related Services", "request": {"method": "GET", "url": {"raw": "{{base_url}}/services/1/related?language=en&limit=3", "host": ["{{base_url}}"], "path": ["services", "1", "related"], "query": [{"key": "language", "value": "en"}, {"key": "limit", "value": "3"}]}}}, {"name": "Get Service Categories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/services/categories?language=en", "host": ["{{base_url}}"], "path": ["services", "categories"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Create Service Inquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"message\": \"I need this service for my construction project.\",\n  \"projectLocation\": \"New York, NY\",\n  \"preferredDate\": \"2024-02-15\",\n  \"budget\": 10000,\n  \"urgency\": \"High\"\n}"}, "url": {"raw": "{{base_url}}/services/1/inquiries", "host": ["{{base_url}}"], "path": ["services", "1", "inquiries"]}}}, {"name": "Create Service (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"New Construction Service\",\n  \"nameAr\": \"???? ???? ?????\",\n  \"descriptionEn\": \"Professional construction service\",\n  \"descriptionAr\": \"???? ???? ????????\",\n  \"shortDescriptionEn\": \"Quality construction\",\n  \"shortDescriptionAr\": \"???? ???? ??????\",\n  \"price\": 5000,\n  \"categoryId\": 1,\n  \"status\": \"Active\",\n  \"isFeatured\": true,\n  \"duration\": \"2-4 weeks\",\n  \"location\": \"On-site\"\n}"}, "url": {"raw": "{{base_url}}/services", "host": ["{{base_url}}"], "path": ["services"]}}}, {"name": "Update Service (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"Updated Service Name\",\n  \"nameAr\": \"??? ?????? ??????\",\n  \"descriptionEn\": \"Updated service description\",\n  \"price\": 6000,\n  \"status\": \"Active\",\n  \"isFeatured\": false\n}"}, "url": {"raw": "{{base_url}}/services/1", "host": ["{{base_url}}"], "path": ["services", "1"]}}}, {"name": "Delete Service (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "DELETE", "url": {"raw": "{{base_url}}/services/1", "host": ["{{base_url}}"], "path": ["services", "1"]}}}]}, {"name": "Projects", "item": [{"name": "Get All Projects", "request": {"method": "GET", "url": {"raw": "{{base_url}}/projects?page=1&pageSize=12&language=en&sortBy=createdAt&sortOrder=desc", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "search", "value": "", "disabled": true}, {"key": "categoryId", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "isFeatured", "value": "", "disabled": true}, {"key": "page", "value": "1"}, {"key": "pageSize", "value": "12"}, {"key": "language", "value": "en"}, {"key": "sortBy", "value": "createdAt"}, {"key": "sortOrder", "value": "desc"}]}}}, {"name": "Get Project by ID", "request": {"method": "GET", "url": {"raw": "{{base_url}}/projects/1?language=en", "host": ["{{base_url}}"], "path": ["projects", "1"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Project by Slug", "request": {"method": "GET", "url": {"raw": "{{base_url}}/projects/slug/project-slug?language=en", "host": ["{{base_url}}"], "path": ["projects", "slug", "project-slug"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Get Related Projects", "request": {"method": "GET", "url": {"raw": "{{base_url}}/projects/1/related?language=en&limit=3", "host": ["{{base_url}}"], "path": ["projects", "1", "related"], "query": [{"key": "language", "value": "en"}, {"key": "limit", "value": "3"}]}}}, {"name": "Get Project Categories", "request": {"method": "GET", "url": {"raw": "{{base_url}}/projects/categories?language=en", "host": ["{{base_url}}"], "path": ["projects", "categories"], "query": [{"key": "language", "value": "en"}]}}}, {"name": "Create Project Inquiry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"message\": \"I would like to implement a similar project.\",\n  \"projectType\": \"Residential\",\n  \"estimatedBudget\": 50000,\n  \"timeframe\": \"3-6 months\",\n  \"location\": \"Los Angeles, CA\"\n}"}, "url": {"raw": "{{base_url}}/projects/1/inquiries", "host": ["{{base_url}}"], "path": ["projects", "1", "inquiries"]}}}, {"name": "Create Project (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"Modern Office Building\",\n  \"nameAr\": \"???? ????? ????\",\n  \"descriptionEn\": \"A state-of-the-art office building\",\n  \"descriptionAr\": \"???? ????? ???? ??????\",\n  \"shortDescriptionEn\": \"Modern office complex\",\n  \"shortDescriptionAr\": \"???? ????? ????\",\n  \"categoryId\": 1,\n  \"status\": \"Completed\",\n  \"isFeatured\": true,\n  \"location\": \"Downtown\",\n  \"client\": \"ABC Corporation\",\n  \"duration\": \"18 months\",\n  \"area\": \"10,000 sqm\",\n  \"budget\": 2000000,\n  \"completionDate\": \"2023-12-01\"\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}}, {"name": "Update Project (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"nameEn\": \"Updated Project Name\",\n  \"nameAr\": \"??? ??????? ??????\",\n  \"descriptionEn\": \"Updated project description\",\n  \"status\": \"Completed\",\n  \"isFeatured\": false\n}"}, "url": {"raw": "{{base_url}}/projects/1", "host": ["{{base_url}}"], "path": ["projects", "1"]}}}, {"name": "Delete Project (Admin)", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "DELETE", "url": {"raw": "{{base_url}}/projects/1", "host": ["{{base_url}}"], "path": ["projects", "1"]}}}]}]}