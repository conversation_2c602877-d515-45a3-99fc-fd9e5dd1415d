namespace ConstructionWebsite.API.DTOs
{
    public class WishlistStatusDto
    {
        public bool IsInWishlist { get; set; }
        public int WishlistCount { get; set; }
    }

    public class WishlistItemDto
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public ProductDto? Product { get; set; }
        public string? UserId { get; set; }
        public string? SessionId { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class AddToWishlistDto
    {
        public int ProductId { get; set; }
        public string? SessionId { get; set; }
    }

    public class RemoveFromWishlistDto
    {
        public int ProductId { get; set; }
        public string? SessionId { get; set; }
    }
}