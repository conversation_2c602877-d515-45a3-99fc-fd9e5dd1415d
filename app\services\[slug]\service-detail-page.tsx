"use client"

import { useState, useEffect } from "react"
import { notFound } from "next/navigation"
import { ServiceDetailClient } from "./service-detail-client"
import { enhancedDb, type Service } from "@/lib/database-enhanced"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import { useTranslation } from "@/components/translation-provider"

interface ServiceDetailPageProps {
  params: { slug: string }
}

function ServiceDetailLoading() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="animate-pulse space-y-8">
          <div className="h-6 bg-gray-300 rounded w-1/3"></div>
          <div className="h-64 bg-gray-300 rounded"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-300 rounded w-3/4"></div>
            <div className="h-20 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ServiceDetailPage({ params }: ServiceDetailPageProps) {
  const { t, language } = useTranslation()
  const [service, setService] = useState<Service | null>(null)
  const [relatedServices, setRelatedServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (params.slug) {
      loadService(params.slug)
    }
  }, [params.slug, language])

  const loadService = async (slug: string) => {
    try {
      setLoading(true)
      const serviceData = await enhancedDb.getServiceBySlug(slug)

      if (!serviceData || serviceData.status !== "active") {
        notFound()
        return
      }

      setService(serviceData)

      // Increment view count
      enhancedDb.incrementServiceViews(serviceData.id).catch(console.error)

      // Load related services
      const related = await enhancedDb.getRelatedServices(serviceData.id, serviceData.category_id, 3)
      setRelatedServices(related)
    } catch (error) {
      console.error("Failed to load service:", error)
      notFound()
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <ServiceDetailLoading />
  }

  if (!service) {
    notFound()
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <SEOBreadcrumbs
          items={[
            { label: t("nav.services", "Services"), href: "/services" },
            { label: language === "ar" ? service.name_ar || service.name : service.name },
          ]}
        />

        <ServiceDetailClient service={service} relatedServices={relatedServices} />
      </div>
    </div>
  )
}
