using System.Collections.Concurrent;
using System.Net;

namespace ConstructionWebsite.API.Middleware
{
    public class RateLimitingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<RateLimitingMiddleware> _logger;
        private static readonly ConcurrentDictionary<string, ClientRequestInfo> _clients = new();
        private readonly int _maxRequests = 100; // Max requests per window
        private readonly TimeSpan _timeWindow = TimeSpan.FromMinutes(1); // Time window

        public RateLimitingMiddleware(RequestDelegate next, ILogger<RateLimitingMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var clientId = GetClientIdentifier(context);
            var now = DateTime.UtcNow;

            var clientInfo = _clients.AddOrUpdate(clientId, 
                new ClientRequestInfo { LastRequest = now, RequestCount = 1 },
                (key, existing) =>
                {
                    if (now - existing.LastRequest > _timeWindow)
                    {
                        // Reset the window
                        existing.RequestCount = 1;
                        existing.LastRequest = now;
                    }
                    else
                    {
                        existing.RequestCount++;
                    }
                    return existing;
                });

            if (clientInfo.RequestCount > _maxRequests)
            {
                _logger.LogWarning("Rate limit exceeded for client: {ClientId}", clientId);
                context.Response.StatusCode = (int)HttpStatusCode.TooManyRequests;
                await context.Response.WriteAsync("Rate limit exceeded. Please try again later.");
                return;
            }

            await _next(context);
        }

        private string GetClientIdentifier(HttpContext context)
        {
            // Try to get the real IP address
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }

        private class ClientRequestInfo
        {
            public DateTime LastRequest { get; set; }
            public int RequestCount { get; set; }
        }
    }
}