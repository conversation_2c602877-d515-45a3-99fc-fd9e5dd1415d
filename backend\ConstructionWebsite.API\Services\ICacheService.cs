namespace ConstructionWebsite.API.Services
{
    public interface ICacheService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task<string?> GetStringAsync(string key);
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task SetStringAsync(string key, string value, TimeSpan? expiration = null);
        Task RemoveAsync(string key);
        Task RemoveByPatternAsync(string pattern);
        Task<bool> ExistsAsync(string key);
        Task ClearAllAsync();
        Task RefreshAsync(string key);
        Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class;
    }
}