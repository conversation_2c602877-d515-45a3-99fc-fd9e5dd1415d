"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: string
}

interface SessionData {
  token: string
  expiresAt: string
  timestamp: number
}

interface DecodedToken {
  username: string
  role: string
  iat: number
  exp: number
}

// Client-side JWT decoder (only decodes, doesn't verify signature)
function decodeJWT(token: string): DecodedToken | null {
  try {
    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }

    const payload = parts[1]
    const decoded = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')))
    return decoded
  } catch (error) {
    console.error('Failed to decode JWT:', error)
    return null
  }
}

export function AuthGuard({ children, requiredRole = "admin" }: AuthGuardProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuthentication()
  }, [])

  const checkAuthentication = async () => {
    try {
      const sessionData = sessionStorage.getItem("adminSession")
      
      if (!sessionData) {
        redirectToLogin()
        return
      }

      const session: SessionData = JSON.parse(sessionData)
      
      // Check if session is expired
      if (Date.now() - session.timestamp > 2 * 60 * 60 * 1000) { // 2 hours
        sessionStorage.removeItem("adminSession")
        redirectToLogin()
        return
      }

      // For simplified testing, just check if token exists and session isn't expired
      if (session.token) {
        console.log('Session found, user authenticated');
        setIsAuthenticated(true)
      } else {
        console.log('No token found, redirecting to login');
        sessionStorage.removeItem("adminSession")
        redirectToLogin()
      }
    } catch (error) {
      console.error("Authentication check failed:", error)
      redirectToLogin()
    } finally {
      setIsLoading(false)
    }
  }

  const redirectToLogin = () => {
    router.push("/admin")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}