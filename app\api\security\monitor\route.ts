import { NextRequest, NextResponse } from "next/server"
import { zeroRiskAuth } from "@/utils/advanced-auth"

export async function GET(request: NextRequest) {
  try {
    // Get security events
    const securityEvents = zeroRiskAuth.getSecurityEvents(50)
    
    // Analyze security metrics
    const metrics = {
      totalEvents: securityEvents.length,
      suspiciousActivity: securityEvents.filter(e => e.type === 'suspicious_activity').length,
      failedLogins: securityEvents.filter(e => e.type === 'login_attempt').length,
      mfaAttempts: securityEvents.filter(e => e.type === 'mfa_attempt').length,
      rateLimitHits: securityEvents.filter(e => e.type === 'rate_limit_exceeded').length,
      last24Hours: securityEvents.filter(e => e.timestamp > Date.now() - 24 * 60 * 60 * 1000).length
    }

    // Security risk assessment
    const riskLevel = metrics.suspiciousActivity > 5 ? 'HIGH' : 
                     metrics.failedLogins > 10 ? 'MEDIUM' : 'LOW'

    return NextResponse.json({
      success: true,
      metrics,
      riskLevel,
      events: securityEvents.slice(-10), // Last 10 events
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Security monitoring error:", error)
    return NextResponse.json(
      { message: "Security monitoring failed" },
      { status: 500 }
    )
  }
}
