namespace ConstructionWebsite.API.DTOs
{
    public class CategoryDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public string? Color { get; set; }
        public bool IsActive { get; set; }
        public bool IsFeatured { get; set; }
        public int SortOrder { get; set; }
        public int? ParentId { get; set; }
        public CategoryDto? Parent { get; set; }
        public List<CategoryDto> Children { get; set; } = new List<CategoryDto>();
        public int ProductCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateCategoryDto
    {
        public string Name { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public string? Color { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsFeatured { get; set; }
        public int SortOrder { get; set; }
        public int? ParentId { get; set; }
    }

    public class UpdateCategoryDto
    {
        public string? Name { get; set; }
        public string? NameAr { get; set; }
        public string? Slug { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public string? Icon { get; set; }
        public string? Image { get; set; }
        public string? Color { get; set; }
        public bool? IsActive { get; set; }
        public bool? IsFeatured { get; set; }
        public int? SortOrder { get; set; }
        public int? ParentId { get; set; }
    }
}