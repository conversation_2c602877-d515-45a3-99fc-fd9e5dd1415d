// Simple in-memory database simulation (replace with real database in production)
export interface Product {
  id: number
  name: string
  nameAr: string
  category: string
  image: string
  description: string
  descriptionAr: string
  features: string[]
  pdfUrl: string
  status: "active" | "draft"
  createdAt: string
  updatedAt: string
}

export interface Project {
  id: number
  name: string
  nameAr: string
  type: string
  location: string
  date: string
  description: string
  descriptionAr: string
  beforeImage: string
  afterImage: string
  videoUrl?: string
  services: string[]
  client: string
  status: "completed" | "ongoing" | "planned"
  createdAt: string
  updatedAt: string
}

export interface Service {
  id: number
  name: string
  nameAr: string
  icon: string
  image: string
  description: string
  descriptionAr: string
  features: string[]
  applications: string[]
  process: string[]
  status: "active" | "inactive"
  createdAt: string
  updatedAt: string
}

export interface ContactMessage {
  id: number
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  status: "unread" | "read" | "replied"
  createdAt: string
}

export interface Testimonial {
  id: number
  name: string
  nameAr: string
  position: string
  positionAr: string
  company: string
  companyAr: string
  content: string
  contentAr: string
  rating: number
  image: string
  projectId?: number
  createdAt: string
}

export interface SiteSettings {
  companyName: string
  companyNameAr: string
  logo: string
  favicon: string
  whatsappNumber: string
  email: string
  phone: string
  address: string
  addressAr: string
  businessHours: string[]
  heroSlides: {
    title: string
    subtitle: string
    description: string
    image: string
    cta: string
  }[]
}

// In-memory storage (replace with real database)
const products: Product[] = [
  {
    id: 1,
    name: "SuperPlast 400",
    nameAr: "سوبر بلاست 400",
    category: "Concrete Admixtures",
    image: "/placeholder.svg?height=300&width=400",
    description: "High-performance superplasticizer for concrete with extended workability",
    descriptionAr: "ملدن فائق الأداء للخرسانة مع قابلية تشغيل ممتدة",
    features: ["Reduces water content by 25%", "Increases strength", "Improves workability"],
    pdfUrl: "/datasheets/superplast-400.pdf",
    status: "active",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-10",
  },
  {
    id: 2,
    name: "CrackSeal Pro",
    nameAr: "كراك سيل برو",
    category: "Repairing & Injection",
    image: "/placeholder.svg?height=300&width=400",
    description: "Advanced crack injection resin for structural repairs",
    descriptionAr: "راتنج حقن الشقوق المتقدم للإصلاحات الهيكلية",
    features: ["Low viscosity", "High strength", "Chemical resistant"],
    pdfUrl: "/datasheets/crackseal-pro.pdf",
    status: "active",
    createdAt: "2024-01-02",
    updatedAt: "2024-01-08",
  },
  {
    id: 3,
    name: "AquaShield Elite",
    nameAr: "أكوا شيلد إليت",
    category: "Waterproofing & Sealing",
    image: "/placeholder.svg?height=300&width=400",
    description: "Premium waterproofing membrane for all surfaces",
    descriptionAr: "غشاء عزل مائي متميز لجميع الأسطح",
    features: ["UV resistant", "Flexible", "Long-lasting"],
    pdfUrl: "/datasheets/aquashield-elite.pdf",
    status: "active",
    createdAt: "2024-01-03",
    updatedAt: "2024-01-05",
  },
  // Drylex Product Line
  {
    id: 4,
    name: "Drylex Waterproof Membrane",
    nameAr: "غشاء درايلكس المقاوم للماء",
    category: "Drylex Waterproofing",
    image: "/placeholder.svg?height=300&width=400",
    description: "Advanced polymer-modified waterproofing membrane for superior protection",
    descriptionAr: "غشاء عزل مائي معدل بالبوليمر للحماية الفائقة",
    features: ["Polymer-modified", "Crack-bridging", "UV resistant", "Easy application"],
    pdfUrl: "/datasheets/Digital catalouge of drylex product.pdf",
    status: "active",
    createdAt: "2024-01-04",
    updatedAt: "2024-01-15",
  },
  {
    id: 5,
    name: "Drylex Liquid Membrane",
    nameAr: "غشاء درايلكس السائل",
    category: "Drylex Waterproofing",
    image: "/placeholder.svg?height=300&width=400",
    description: "Single-component liquid waterproofing membrane for complex geometries",
    descriptionAr: "غشاء عزل مائي سائل أحادي المكون للأشكال المعقدة",
    features: ["Single component", "Self-leveling", "Seamless application", "Chemical resistant"],
    pdfUrl: "/datasheets/Digital catalouge of drylex product.pdf",
    status: "active",
    createdAt: "2024-01-05",
    updatedAt: "2024-01-15",
  },
  {
    id: 6,
    name: "Drylex Foundation Sealer",
    nameAr: "مانع تسرب الأساسات درايلكس",
    category: "Drylex Waterproofing",
    image: "/placeholder.svg?height=300&width=400",
    description: "Penetrating sealer for concrete foundations and basement walls",
    descriptionAr: "مانع تسرب نافذ لأساسات الخرسانة وجدران الطوابق السفلية",
    features: ["Deep penetration", "Crystalline technology", "Permanent protection", "Breathable"],
    pdfUrl: "/datasheets/Digital catalouge of drylex product.pdf",
    status: "active",
    createdAt: "2024-01-06",
    updatedAt: "2024-01-15",
  },
  {
    id: 7,
    name: "Drylex Roof Coating",
    nameAr: "طلاء الأسطح درايلكس",
    category: "Drylex Waterproofing",
    image: "/placeholder.svg?height=300&width=400",
    description: "Reflective roof coating for energy efficiency and waterproofing",
    descriptionAr: "طلاء عاكس للأسطح لكفاءة الطاقة والعزل المائي",
    features: ["Heat reflective", "Energy saving", "Weather resistant", "Long-lasting"],
    pdfUrl: "/datasheets/Digital catalouge of drylex product.pdf",
    status: "active",
    createdAt: "2024-01-07",
    updatedAt: "2024-01-15",
  },
  {
    id: 8,
    name: "Drylex Injection Resin",
    nameAr: "راتنج الحقن درايلكس",
    category: "Drylex Repair Systems",
    image: "/placeholder.svg?height=300&width=400",
    description: "Low-viscosity injection resin for crack sealing and structural repair",
    descriptionAr: "راتنج حقن منخفض اللزوجة لإغلاق الشقوق والإصلاح الهيكلي",
    features: ["Ultra-low viscosity", "High strength", "Fast curing", "Structural grade"],
    pdfUrl: "/datasheets/Digital catalouge of drylex product.pdf",
    status: "active",
    createdAt: "2024-01-08",
    updatedAt: "2024-01-15",
  },
]

const projects: Project[] = [
  {
    id: 1,
    name: "Dubai Marina Tower",
    nameAr: "برج ذي قار مارينا",
    type: "Commercial",
    location: "Nassiriya, Iraq",
    date: "2023",
    description: "Complete waterproofing and concrete repair solution for 50-story tower",
    descriptionAr: "حل شامل للعزل المائي وإصلاح الخرسانة لبرج من 50 طابق",
    beforeImage: "/placeholder.svg?height=300&width=400",
    afterImage: "/placeholder.svg?height=300&width=400",
    videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    services: ["Waterproofing", "Concrete Repair", "Protective Coating"],
    client: "Emirates Development",
    status: "completed",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-12",
  },
  {
    id: 2,
    name: "Al Reem Island Residential Complex",
    nameAr: "مجمع الريم السكني",
    type: "Residential",
    location: "Baghdad, Iraq",
    date: "2023",
    description: "Comprehensive construction materials supply and technical support",
    descriptionAr: "توريد شامل لمواد البناء والدعم الفني",
    beforeImage: "/placeholder.svg?height=300&width=400",
    afterImage: "/placeholder.svg?height=300&width=400",
    services: ["Material Supply", "Technical Consultation", "Quality Control"],
    client: "Aldar Properties",
    status: "ongoing",
    createdAt: "2024-01-02",
    updatedAt: "2024-01-10",
  },
]

const services: Service[] = [
  {
    id: 1,
    name: "Concrete Repair & Restoration",
    nameAr: "إصلاح وترميم الخرسانة",
    icon: "Hammer",
    image: "/placeholder.svg?height=300&width=400",
    description: "Professional concrete repair services for structural integrity and longevity",
    descriptionAr: "خدمات إصلاح الخرسانة المهنية للسلامة الهيكلية والمتانة",
    features: [
      "Crack injection and sealing",
      "Spalling concrete repair",
      "Structural strengthening",
      "Surface preparation",
      "Quality assurance testing",
    ],
    applications: ["Buildings", "Bridges", "Parking structures", "Industrial facilities"],
    process: [
      "Site inspection and assessment",
      "Damage analysis and reporting",
      "Repair method selection",
      "Professional execution",
      "Quality control and testing",
    ],
    status: "active",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01",
  },
  {
    id: 2,
    name: "Waterproofing Solutions",
    nameAr: "حلول العزل المائي",
    icon: "Droplets",
    image: "/placeholder.svg?height=300&width=400",
    description: "Complete waterproofing systems for all types of structures and surfaces",
    descriptionAr: "أنظمة عزل مائي شاملة لجميع أنواع الهياكل والأسطح",
    features: [
      "Membrane waterproofing",
      "Liquid applied systems",
      "Injection waterproofing",
      "Basement waterproofing",
      "Roof waterproofing",
    ],
    applications: ["Basements", "Roofs", "Bathrooms", "Swimming pools"],
    process: [
      "Moisture assessment",
      "Surface preparation",
      "System selection",
      "Professional application",
      "Performance testing",
    ],
    status: "active",
    createdAt: "2024-01-01",
    updatedAt: "2024-01-01",
  },
]

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "Ahmed Al-Rashid",
    nameAr: "أحمد الراشد",
    position: "Project Manager",
    positionAr: "مدير المشروع",
    company: "Emirates Development",
    companyAr: "تطوير الإمارات",
    content: "DRYLEX provided exceptional waterproofing solutions for our 50-story tower. Their expertise and quality materials ensured our project's success.",
    contentAr: "قدمت درايلكس حلول عزل مائي استثنائية لبرجنا المكون من 50 طابقاً. خبرتهم ومواد الجودة ضمنت نجاح مشروعنا.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
    projectId: 1,
    createdAt: "2024-01-10T10:00:00Z"
  },
  {
    id: 2,
    name: "Sarah Johnson",
    nameAr: "سارة جونسون",
    position: "Construction Director",
    positionAr: "مدير البناء",
    company: "Aldar Properties",
    companyAr: "شركة الدار العقارية",
    content: "Outstanding service and technical support. DRYLEX's team delivered high-quality materials on time and provided excellent consultation throughout our residential project.",
    contentAr: "خدمة متميزة ودعم فني ممتاز. فريق درايلكس قدم مواد عالية الجودة في الوقت المحدد وقدم استشارة ممتازة طوال مشروعنا السكني.",
    rating: 5,
    image: "/placeholder.svg?height=100&width=100",
    projectId: 2,
    createdAt: "2024-01-12T14:30:00Z"
  },
  {
    id: 3,
    name: "Mohammed Hassan",
    nameAr: "محمد حسن",
    position: "Site Engineer",
    positionAr: "مهندس موقع",
    company: "Baghdad Construction Co.",
    companyAr: "شركة بغداد للإنشاءات",
    content: "The Drylex waterproofing products exceeded our expectations. Easy to apply and highly effective in protecting our structures from water damage.",
    contentAr: "منتجات العزل المائي من درايلكس فاقت توقعاتنا. سهلة التطبيق وفعالة جداً في حماية هياكلنا من أضرار المياه.",
    rating: 4,
    image: "/placeholder.svg?height=100&width=100",
    createdAt: "2024-01-08T09:15:00Z"
  }
]

const contactMessages: ContactMessage[] = [
  {
    id: 1,
    name: "Ahmed Al-Rashid",
    email: "<EMAIL>",
    phone: "+9647867100886",
    subject: "Waterproofing Quote Request",
    message: "I need a quote for waterproofing a 2000 sqm warehouse.",
    status: "unread",
    createdAt: "2024-01-15T10:30:00Z",
  },
  {
    id: 2,
    name: "Sarah Johnson",
    email: "<EMAIL>",
    subject: "Bulk Order Inquiry",
    message: "Interested in bulk pricing for concrete admixtures.",
    status: "read",
    createdAt: "2024-01-14T14:20:00Z",
  },
]

let siteSettings: SiteSettings = {
  companyName: "DRYLEX",
  companyNameAr: "درايلكس",
  logo: "/placeholder.svg?height=40&width=120",
  favicon: "/favicon.ico",
  whatsappNumber: "+9647867100886",
    email: "<EMAIL>",
    phone: "+9647867100886",
    address: "Alshimoukh district, district 205, zukak 68, building 40, Nassiriya, Thi Qar, Iraq",
  addressAr: "حي الشموخ - مقابل مجمع الحضارات (تينا) - الناصرية- ذي قار - العراق",
  businessHours: ["Sunday - Thursday: 8:00 AM - 6:00 PM", "Friday: 8:00 AM - 12:00 PM", "Saturday: Closed"],
  heroSlides: [
    {
      title: "DRYLEX IRAQ Materials",
      subtitle: "Building Excellence Since 1995",
      description: "Professional-grade construction materials and expert services for your building projects",
      image: "/placeholder.svg?height=600&width=1200",
      cta: "Explore Products",
    },
    {
      title: "Expert Construction Services",
      subtitle: "Your Trusted Construction Partner",
      description: "From concrete repair to waterproofing, we deliver quality solutions that last",
      image: "/placeholder.svg?height=600&width=1200",
      cta: "View Services",
    },
    {
      title: "Proven Project Success",
      subtitle: "1000+ Completed Projects",
      description: "See how we've transformed construction projects across the region",
      image: "/placeholder.svg?height=600&width=1200",
      cta: "View Projects",
    },
  ],
}

// Database operations
export const db = {
  // Products
  getProducts: () => products.filter((p) => p.status === "active"),
  getAllProducts: () => products,
  getProduct: (id: number) => products.find((p) => p.id === id),
  createProduct: (product: Omit<Product, "id" | "createdAt" | "updatedAt">) => {
    const newProduct = {
      ...product,
      id: Math.max(...products.map((p) => p.id), 0) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    products.push(newProduct)
    return newProduct
  },
  updateProduct: (id: number, updates: Partial<Product>) => {
    const index = products.findIndex((p) => p.id === id)
    if (index !== -1) {
      products[index] = { ...products[index], ...updates, updatedAt: new Date().toISOString() }
      return products[index]
    }
    return null
  },
  deleteProduct: (id: number) => {
    const index = products.findIndex((p) => p.id === id)
    if (index !== -1) {
      products.splice(index, 1)
      return true
    }
    return false
  },

  // Projects
  getProjects: () => projects,
  getProject: (id: number) => projects.find((p) => p.id === id),
  createProject: (project: Omit<Project, "id" | "createdAt" | "updatedAt">) => {
    const newProject = {
      ...project,
      id: Math.max(...projects.map((p) => p.id), 0) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    projects.push(newProject)
    return newProject
  },
  updateProject: (id: number, updates: Partial<Project>) => {
    const index = projects.findIndex((p) => p.id === id)
    if (index !== -1) {
      projects[index] = { ...projects[index], ...updates, updatedAt: new Date().toISOString() }
      return projects[index]
    }
    return null
  },
  deleteProject: (id: number) => {
    const index = projects.findIndex((p) => p.id === id)
    if (index !== -1) {
      projects.splice(index, 1)
      return true
    }
    return false
  },

  // Services
  getServices: () => services.filter((s) => s.status === "active"),
  getAllServices: () => services,
  getService: (id: number) => services.find((s) => s.id === id),
  createService: (service: Omit<Service, "id" | "createdAt" | "updatedAt">) => {
    const newService = {
      ...service,
      id: Math.max(...services.map((s) => s.id), 0) + 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
    services.push(newService)
    return newService
  },
  updateService: (id: number, updates: Partial<Service>) => {
    const index = services.findIndex((s) => s.id === id)
    if (index !== -1) {
      services[index] = { ...services[index], ...updates, updatedAt: new Date().toISOString() }
      return services[index]
    }
    return null
  },
  deleteService: (id: number) => {
    const index = services.findIndex((s) => s.id === id)
    if (index !== -1) {
      services.splice(index, 1)
      return true
    }
    return false
  },

  // Contact Messages
  getMessages: () => contactMessages,
  getMessage: (id: number) => contactMessages.find((m) => m.id === id),
  createMessage: (message: Omit<ContactMessage, "id" | "createdAt">) => {
    const newMessage = {
      ...message,
      id: Math.max(...contactMessages.map((m) => m.id), 0) + 1,
      createdAt: new Date().toISOString(),
    }
    contactMessages.push(newMessage)
    return newMessage
  },
  updateMessage: (id: number, updates: Partial<ContactMessage>) => {
    const index = contactMessages.findIndex((m) => m.id === id)
    if (index !== -1) {
      contactMessages[index] = { ...contactMessages[index], ...updates }
      return contactMessages[index]
    }
    return null
  },
  deleteMessage: (id: number) => {
    const index = contactMessages.findIndex((m) => m.id === id)
    if (index !== -1) {
      contactMessages.splice(index, 1)
      return true
    }
    return false
  },

  // Testimonials
  getTestimonials: () => testimonials,
  getTestimonial: (id: number) => testimonials.find((t) => t.id === id),
  createTestimonial: (testimonial: Omit<Testimonial, "id" | "createdAt">) => {
    const newTestimonial = {
      ...testimonial,
      id: Math.max(...testimonials.map((t) => t.id), 0) + 1,
      createdAt: new Date().toISOString(),
    }
    testimonials.push(newTestimonial)
    return newTestimonial
  },
  updateTestimonial: (id: number, updates: Partial<Testimonial>) => {
    const index = testimonials.findIndex((t) => t.id === id)
    if (index !== -1) {
      testimonials[index] = { ...testimonials[index], ...updates }
      return testimonials[index]
    }
    return null
  },
  deleteTestimonial: (id: number) => {
    const index = testimonials.findIndex((t) => t.id === id)
    if (index !== -1) {
      testimonials.splice(index, 1)
      return true
    }
    return false
  },

  // Settings
  getSettings: () => siteSettings,
  updateSettings: (updates: Partial<SiteSettings>) => {
    siteSettings = { ...siteSettings, ...updates }
    return siteSettings
  },
}
