import { sanitizeJSON } from "@/utils/security"

interface SecureJSONLDProps {
  data: any
  id?: string
}

export function SecureJSONLD({ data, id }: SecureJSONLDProps) {
  const sanitizedData = sanitizeJSON(data)
  
  if (!sanitizedData) {
    return null
  }

  return (
    <script
      type="application/ld+json"
      id={id}
      dangerouslySetInnerHTML={{ 
        __html: sanitizedData 
      }}
    />
  )
}
