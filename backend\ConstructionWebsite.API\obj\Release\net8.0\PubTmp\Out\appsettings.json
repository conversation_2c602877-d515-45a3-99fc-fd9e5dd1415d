{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\MSSQLLocalDB;Database=ConstructionWebsiteDB;Trusted_Connection=true;MultipleActiveResultSets=true", "Redis": "localhost:6379"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong123456789", "Issuer": "ConstructionWebsiteAPI", "Audience": "ConstructionWebsiteClient", "ExpiryInMinutes": 60}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SenderEmail": "<EMAIL>", "SenderPassword": "your-app-password", "SenderName": "DRYLEX  Support"}, "FileUpload": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx"], "UploadPath": "wwwroot/uploads"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}