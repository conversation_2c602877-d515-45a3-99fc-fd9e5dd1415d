"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ImageIcon, Plus, Edit, Trash2, Download, Bar<PERSON><PERSON>3, <PERSON>, <PERSON>fresh<PERSON><PERSON>, LogIn } from "lucide-react"
import Link from "next/link"

interface ImageSize {
  id: number
  section_name: string
  image_type: string
  width: number
  height: number
  aspect_ratio: string
  description: string
  is_active: boolean
  usage_count: number
  last_updated: string
}

interface ImageUsage {
  total_images: number
  by_type: Record<string, number>
  total_size_mb: number
  avg_size_kb: number
}

export default function ImageManagementPage() {
  const [imageSizes, setImageSizes] = useState<ImageSize[]>([])
  
  // Enhanced JSON-LD for Image Management Page
  const imageManagementJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Image Management - DRYLEX Iraq",
    "description": "Image management system for DRYLEX Iraq website media assets",
    "url": "https://drylexiraq.com/image-management",
    "mainContentOfPage": {
      "@type": "ImageGallery",
      "name": "DRYLEX Iraq Media Gallery",
      "description": "Collection of product images, project photos, and other media assets for DRYLEX Iraq website",
      "itemCount": 200,
      "hasPart": [
        {
          "@type": "ImageObject",
          "name": "Product Images",
          "description": "High-quality images of construction materials and products offered by DRYLEX Iraq",
          "numberOfItems": 120,
          "keywords": "construction materials, building products, drywall, plaster, insulation"
        },
        {
          "@type": "ImageObject",
          "name": "Project Photos",
          "description": "Photos of completed construction projects by DRYLEX Iraq",
          "numberOfItems": 50,
          "keywords": "construction projects, building sites, completed works, project galleries"
        },
        {
          "@type": "ImageObject",
          "name": "Technical Diagrams",
          "description": "Technical illustrations and diagrams of construction systems",
          "numberOfItems": 30,
          "keywords": "technical drawings, construction diagrams, installation guides, system layouts"
        }
      ],
      "image": [
        {
          "@type": "ImageObject",
          "url": "https://drylexiraq.com/images/og-image-management.jpg",
          "description": "Overview of DRYLEX Iraq media assets"
        }
      ],
    "publisher": {
      "@type": "Organization",
      "name": "DRYLEX Iraq",
      "url": "https://drylexiraq.com",
      "logo": {
        "@type": "ImageObject",
        "url": "https://drylexiraq.com/logo.png"
      }
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Image Management"
        }
      ]
    }
    }
  }

  const [imageUsage, setImageUsage] = useState<Record<string, ImageUsage>>({})
  const [loading, setLoading] = useState(true)
  const [selectedSection, setSelectedSection] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingSize, setEditingSize] = useState<ImageSize | null>(null)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    section_name: "",
    image_type: "",
    width: "",
    height: "",
    description: "",
    is_active: true,
  })

  const sections = ["all", "products", "slider", "services", "projects"]
  const sectionColors = {
    products: "bg-blue-100 text-blue-800",
    slider: "bg-green-100 text-green-800",
    services: "bg-purple-100 text-purple-800",
    projects: "bg-orange-100 text-orange-800",
  }

  useEffect(() => {
    fetchImageSizes()
    fetchImageUsage()
  }, [selectedSection])

  const fetchImageSizes = async () => {
    try {
      const params = selectedSection !== "all" ? `?section=${selectedSection}` : ""
      const response = await fetch(`/api/image-sizes${params}`)
      const result = await response.json()

      if (result.success) {
        setImageSizes(result.data)
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch image sizes",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchImageUsage = async () => {
    try {
      const response = await fetch("/api/image-usage")
      const result = await response.json()

      if (result.success) {
        setImageUsage(result.data)
      }
    } catch (error) {
      console.error("Failed to fetch image usage:", error)
    }
  }

  const handleCreate = async () => {
    try {
      const response = await fetch("/api/image-sizes", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...formData,
          width: Number.parseInt(formData.width),
          height: Number.parseInt(formData.height),
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({ title: "Success", description: "Image size configuration created" })
        setIsCreateDialogOpen(false)
        resetForm()
        fetchImageSizes()
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create image size configuration",
        variant: "destructive",
      })
    }
  }

  const handleUpdate = async () => {
    if (!editingSize) return

    try {
      const response = await fetch("/api/image-sizes", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: editingSize.id,
          ...formData,
          width: Number.parseInt(formData.width),
          height: Number.parseInt(formData.height),
        }),
      })

      const result = await response.json()

      if (result.success) {
        toast({ title: "Success", description: "Image size configuration updated" })
        setEditingSize(null)
        resetForm()
        fetchImageSizes()
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update image size configuration",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this image size configuration?")) return

    try {
      const response = await fetch(`/api/image-sizes?id=${id}`, {
        method: "DELETE",
      })

      const result = await response.json()

      if (result.success) {
        toast({ title: "Success", description: "Image size configuration deleted" })
        fetchImageSizes()
      } else {
        toast({
          title: "Error",
          description: result.error,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete image size configuration",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      section_name: "",
      image_type: "",
      width: "",
      height: "",
      description: "",
      is_active: true,
    })
  }

  const startEdit = (size: ImageSize) => {
    setEditingSize(size)
    setFormData({
      section_name: size.section_name,
      image_type: size.image_type,
      width: size.width.toString(),
      height: size.height.toString(),
      description: size.description,
      is_active: size.is_active,
    })
  }

  const filteredImageSizes = imageSizes.filter((size) => {
    const matchesSearch =
      size.section_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      size.image_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      size.description.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesSearch
  })

  const exportToCSV = () => {
    const headers = ["Section", "Type", "Width", "Height", "Aspect Ratio", "Description", "Usage Count", "Status"]
    const csvContent = [
      headers.join(","),
      ...filteredImageSizes.map((size) =>
        [
          size.section_name,
          size.image_type,
          size.width,
          size.height,
          size.aspect_ratio,
          `"${size.description}"`,
          size.usage_count,
          size.is_active ? "Active" : "Inactive",
        ].join(","),
      ),
    ].join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `image-sizes-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          <div className="animate-pulse space-y-8">
            <div className="h-8 bg-gray-300 rounded w-1/3"></div>
            <div className="h-64 bg-gray-300 rounded"></div>
            <div className="h-96 bg-gray-300 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
              <ImageIcon className="h-8 w-8 text-orange-600" />
              Image Size Management
            </h1>
            <p className="text-gray-600 mt-2">Manage image dimensions and specifications across all website sections</p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/admin/dashboard">
                <LogIn className="h-4 w-4 mr-2" />
                Admin Dashboard
              </Link>
            </Button>
            <Button onClick={exportToCSV} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-orange-600 hover:bg-orange-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Image Size
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Image Size Configuration</DialogTitle>
                  <DialogDescription>Define new image dimensions for a specific section and type</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="section">Section</Label>
                      <Select
                        value={formData.section_name}
                        onValueChange={(value) => setFormData({ ...formData, section_name: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select section" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="products">Products</SelectItem>
                          <SelectItem value="slider">Slider</SelectItem>
                          <SelectItem value="services">Services</SelectItem>
                          <SelectItem value="projects">Projects</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="type">Image Type</Label>
                      <Input
                        value={formData.image_type}
                        onChange={(e) => setFormData({ ...formData, image_type: e.target.value })}
                        placeholder="e.g., thumbnail, hero, gallery"
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="width">Width (px)</Label>
                      <Input
                        type="number"
                        value={formData.width}
                        onChange={(e) => setFormData({ ...formData, width: e.target.value })}
                        placeholder="800"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="height">Height (px)</Label>
                      <Input
                        type="number"
                        value={formData.height}
                        onChange={(e) => setFormData({ ...formData, height: e.target.value })}
                        placeholder="600"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe the usage of this image size"
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-4">
                    <Button onClick={handleCreate} className="bg-green-600 hover:bg-green-700">
                      Create Configuration
                    </Button>
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sizes">Image Sizes</TabsTrigger>
            <TabsTrigger value="usage">Usage Statistics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {Object.entries(imageUsage).map(([section, data]) => (
                <Card key={section}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium capitalize flex items-center justify-between">
                      {section}
                      <Badge
                        className={sectionColors[section as keyof typeof sectionColors] || "bg-gray-100 text-gray-800"}
                      >
                        {data.total_images}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Total Size:</span>
                        <span className="font-medium">{data.total_size_mb} MB</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Avg Size:</span>
                        <span className="font-medium">{data.avg_size_kb} KB</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Section Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Image Size Configurations by Section</CardTitle>
                <CardDescription>
                  Overview of all configured image sizes across different website sections
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {sections
                    .filter((s) => s !== "all")
                    .map((section) => {
                      const sectionSizes = imageSizes.filter((size) => size.section_name === section)
                      return (
                        <div key={section} className="p-4 border rounded-lg">
                          <h3 className="font-semibold capitalize mb-2">{section}</h3>
                          <p className="text-sm text-gray-600 mb-3">{sectionSizes.length} configurations</p>
                          <div className="space-y-1">
                            {sectionSizes.slice(0, 3).map((size) => (
                              <div key={size.id} className="text-xs text-gray-500">
                                {size.image_type}: {size.width}×{size.height}
                              </div>
                            ))}
                            {sectionSizes.length > 3 && (
                              <div className="text-xs text-gray-400">+{sectionSizes.length - 3} more</div>
                            )}
                          </div>
                        </div>
                      )
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="sizes" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search image sizes..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={selectedSection} onValueChange={setSelectedSection}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Sections</SelectItem>
                      <SelectItem value="products">Products</SelectItem>
                      <SelectItem value="slider">Slider</SelectItem>
                      <SelectItem value="services">Services</SelectItem>
                      <SelectItem value="projects">Projects</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" onClick={fetchImageSizes}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Image Sizes Table */}
            <Card>
              <CardHeader>
                <CardTitle>Image Size Configurations</CardTitle>
                <CardDescription>Manage image dimensions for different sections and types</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Section</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Dimensions</TableHead>
                        <TableHead>Aspect Ratio</TableHead>
                        <TableHead>Usage</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredImageSizes.map((size) => (
                        <TableRow key={size.id}>
                          <TableCell>
                            <Badge
                              className={
                                sectionColors[size.section_name as keyof typeof sectionColors] ||
                                "bg-gray-100 text-gray-800"
                              }
                            >
                              {size.section_name}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-medium">{size.image_type}</TableCell>
                          <TableCell>
                            <span className="font-mono text-sm">
                              {size.width} × {size.height}px
                            </span>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{size.aspect_ratio}</Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm text-gray-600">{size.usage_count} images</span>
                          </TableCell>
                          <TableCell>
                            <Badge variant={size.is_active ? "default" : "secondary"}>
                              {size.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-gray-500">
                            {new Date(size.last_updated).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm" onClick={() => startEdit(size)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(size.id)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-6">
            {/* Usage Statistics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {Object.entries(imageUsage).map(([section, data]) => (
                <Card key={section}>
                  <CardHeader>
                    <CardTitle className="capitalize flex items-center gap-2">
                      <BarChart3 className="h-5 w-5" />
                      {section} Images
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Total Images</p>
                          <p className="text-2xl font-bold">{data.total_images}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Total Size</p>
                          <p className="text-2xl font-bold">{data.total_size_mb} MB</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-2">By Type</p>
                        <div className="space-y-2">
                          {Object.entries(data.by_type).map(([type, count]) => (
                            <div key={type} className="flex justify-between items-center">
                              <span className="text-sm capitalize">{type}</span>
                              <Badge variant="outline">{count}</Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Edit Dialog */}
        {editingSize && (
          <Dialog open={!!editingSize} onOpenChange={() => setEditingSize(null)}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Image Size Configuration</DialogTitle>
                <DialogDescription>Update the image dimensions and settings</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="section">Section</Label>
                    <Select
                      value={formData.section_name}
                      onValueChange={(value) => setFormData({ ...formData, section_name: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="products">Products</SelectItem>
                        <SelectItem value="slider">Slider</SelectItem>
                        <SelectItem value="services">Services</SelectItem>
                        <SelectItem value="projects">Projects</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Image Type</Label>
                    <Input
                      value={formData.image_type}
                      onChange={(e) => setFormData({ ...formData, image_type: e.target.value })}
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">Width (px)</Label>
                    <Input
                      type="number"
                      value={formData.width}
                      onChange={(e) => setFormData({ ...formData, width: e.target.value })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height (px)</Label>
                    <Input
                      type="number"
                      value={formData.height}
                      onChange={(e) => setFormData({ ...formData, height: e.target.value })}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    rows={3}
                  />
                </div>
                <div className="flex gap-4">
                  <Button onClick={handleUpdate} className="bg-blue-600 hover:bg-blue-700">
                    Update Configuration
                  </Button>
                  <Button variant="outline" onClick={() => setEditingSize(null)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
    </>
  )
}
