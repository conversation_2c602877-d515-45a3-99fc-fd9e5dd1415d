"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Plus, Edit, Trash2, Save, X } from "lucide-react"
import { db } from "@/lib/database"
import type { Product } from "@/lib/database"
import { AuthGuard } from "@/components/auth-guard"


export default function AdminProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  // JSON-LD for Admin Products Page
  const adminProductsJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": "https://drylexiraq.com/admin/dashboard/products#products",
    "name": "Admin Products - DRYLEX Iraq",
    "description": "Admin dashboard for managing products on DRYLEX Iraq website. Add, edit, and remove construction materials and their details.",
    "url": "https://drylexiraq.com/admin/dashboard/products",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Admin Dashboard",
          "item": "https://drylexiraq.com/admin/dashboard"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Products",
          "item": "https://drylexiraq.com/admin/dashboard/products"
        }
      ]
    },
    "mainContentOfPage": {
      "@type": "WebApplication",
      "name": "DRYLEX Iraq Admin Products",
      "description": "Web application for managing construction materials product catalog for DRYLEX Iraq website",
      "operatingSystem": "Web",
      "applicationCategory": "BusinessApplication",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "eligibleRegion": "IQ",
        "availability": "https://schema.org/InStock",
        "seller": {
          "@type": "Organization",
          "name": "DRYLEX Iraq",
          "url": "https://drylexiraq.com"
        }
      },
      "featureList": {
        "@type": "CategoryCodeSet",
        "name": "Product Management Features",
        "includesCategoryCode": [
          {
            "@type": "CategoryCode",
            "codeValue": "create",
            "name": "Create New Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "edit",
            "name": "Edit Existing Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "delete",
            "name": "Delete Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "category",
            "name": "Manage Product Categories"
          }
        ]
      }
    },
    "potentialAction": {
      "@type": "Action",
      "name": "Manage Products",
      "description": "Add, edit, and delete construction material products in the catalog",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://drylexiraq.com/admin/dashboard/products",
        "actionPlatform": [
          "https://schema.org/DesktopWebPlatform",
          "https://schema.org/MobileWebPlatform"
        ]
      }
    },
    "inLanguage": {
      "@type": "Language",
      "name": "English",
      "alternateName": "en"
    },
    "isPartOf": {
      "@type": "WebSite",
      "name": "DRYLEX Iraq Admin",
      "url": "https://drylexiraq.com/admin",
      "description": "Administrative dashboard for managing DRYLEX Iraq website content",
      "publisher": {
        "@type": "Organization",
        "name": "DRYLEX Iraq",
        "logo": {
          "@type": "ImageObject",
          "url": "https://drylexiraq.com/logo.png"
        },
        "url": "https://drylexiraq.com"
      }
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  // JSON-LD for Product List
  const productListJsonLd = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Construction Materials Product Catalog",
    "description": "List of construction materials products managed through the DRYLEX IRAQ admin dashboard",
    "itemListElement": products.map((product, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "url": `https://drylexiraq.com/admin/dashboard/products/edit/${product.id}`,
      "item": {
        "@type": "Product",
        "name": product.name,
        "description": product.description,
        "category": product.category,
        "image": product.image,
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "seller": {
            "@type": "Organization",
            "name": "DRYLEX Iraq"
          }
        }
      }
    }))
  }

  const categories = [
    "Concrete Admixtures",
    "Repairing & Injection",
    "Waterproofing & Sealing",
    "Tile Adhesive & Grouting",
    "Epoxy Systems & Painting",
  ]

  const [formData, setFormData] = useState({
    name: "",
    nameAr: "",
    category: "",
    image: "",
    description: "",
    descriptionAr: "",
    features: "",
    pdfUrl: "",
    status: "active" as "active" | "draft",
  })

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = () => {
    setProducts(db.getAllProducts())
  }

  const handleCreate = () => {
    setIsCreating(true)
    setFormData({
      name: "",
      nameAr: "",
      category: "",
      image: "",
      description: "",
      descriptionAr: "",
      features: "",
      pdfUrl: "",
      status: "active",
    })
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      nameAr: product.nameAr,
      category: product.category,
      image: product.image,
      description: product.description,
      descriptionAr: product.descriptionAr,
      features: product.features.join(", "),
      pdfUrl: product.pdfUrl,
      status: product.status,
    })
  }

  const handleSave = () => {
    try {
      const productData = {
        ...formData,
        features: formData.features
          .split(",")
          .map((f) => f.trim())
          .filter((f) => f),
      }

      if (editingProduct) {
        db.updateProduct(editingProduct.id, productData)
        toast({ title: "Product updated successfully!" })
      } else {
        db.createProduct(productData)
        toast({ title: "Product created successfully!" })
      }

      loadProducts()
      handleCancel()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save product",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this product?")) {
      db.deleteProduct(id)
      loadProducts()
      toast({ title: "Product deleted successfully!" })
    }
  }

  const handleCancel = () => {
    setEditingProduct(null)
    setIsCreating(false)
    setFormData({
      name: "",
      nameAr: "",
      category: "",
      image: "",
      description: "",
      descriptionAr: "",
      features: "",
      pdfUrl: "",
      status: "active",
    })
  }

  return (
    <AuthGuard>
      <>
        <>
          <script 
            type="application/ld+json" 
            dangerouslySetInnerHTML={{ __html: JSON.stringify(adminProductsJsonLd) }} 
          />
          <script 
            type="application/ld+json" 
            dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} 
          />
          <script 
            type="application/ld+json" 
            dangerouslySetInnerHTML={{ __html: JSON.stringify(productListJsonLd) }} 
          />
        </>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Product Management</h1>
              <p className="text-gray-600">Manage your construction materials catalog</p>
            </div>
            <Button onClick={handleCreate} className="bg-orange-600 hover:bg-orange-700">
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </div>

          {(isCreating || editingProduct) && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>{editingProduct ? "Edit Product" : "Create New Product"}</CardTitle>
                <CardDescription>
                  {editingProduct ? "Update product information" : "Add a new product to your catalog"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Product Name (English)</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter product name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nameAr">Product Name (Arabic)</Label>
                    <Input
                      id="nameAr"
                      value={formData.nameAr}
                      onChange={(e) => setFormData({ ...formData, nameAr: e.target.value })}
                      placeholder="Enter Arabic name"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({ ...formData, category: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: "active" | "draft") => setFormData({ ...formData, status: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">Image URL</Label>
                  <Input
                    id="image"
                    value={formData.image}
                    onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                    placeholder="Enter image URL"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (English)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter product description"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="descriptionAr">Description (Arabic)</Label>
                    <Textarea
                      id="descriptionAr"
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({ ...formData, descriptionAr: e.target.value })}
                      placeholder="Enter Arabic description"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="features">Features (comma-separated)</Label>
                    <Textarea
                      id="features"
                      value={formData.features}
                      onChange={(e) => setFormData({ ...formData, features: e.target.value })}
                      placeholder="Feature 1, Feature 2, Feature 3"
                      rows={2}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="pdfUrl">PDF Data Sheet URL</Label>
                    <Input
                      id="pdfUrl"
                      value={formData.pdfUrl}
                      onChange={(e) => setFormData({ ...formData, pdfUrl: e.target.value })}
                      placeholder="Enter PDF URL"
                    />
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                    <Save className="h-4 w-4 mr-2" />
                    Save Product
                  </Button>
                  <Button onClick={handleCancel} variant="outline">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 gap-6">
            {products.map((product) => (
              <Card key={product.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-2">
                        <h3 className="text-xl font-semibold">{product.name}</h3>
                        <Badge variant={product.status === "active" ? "default" : "secondary"}>{product.status}</Badge>
                        <Badge variant="outline">{product.category}</Badge>
                      </div>
                      <p className="text-gray-600 mb-2">{product.nameAr}</p>
                      <p className="text-gray-700 mb-4">{product.description}</p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {product.features.map((feature, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                      <p className="text-sm text-gray-500">
                        Updated: {new Date(product.updatedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" onClick={() => handleEdit(product)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleDelete(product.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </>
  </AuthGuard>
  )
}
