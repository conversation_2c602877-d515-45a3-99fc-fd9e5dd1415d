"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Heart,
  Share2,
  MessageCircle,
  Phone,
  Mail,
  Star,
  Eye,
  Download,
  Truck,
  Shield,
  Award,
  Clock,
  ChevronLeft,
  ChevronRight,
  ZoomInIcon as Zoom,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import type { Product } from "@/lib/database-enhanced"
import { incrementViewCount } from "@/lib/database-enhanced"

interface ProductDetailClientProps {
  product: Product
}

export function ProductDetailClient({ product }: ProductDetailClientProps) {
  const { t, language } = useTranslation()
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [quantity, setQuantity] = useState(1)
  const [activeTab, setActiveTab] = useState("overview")

  // Language-aware content with defensive programming
  const displayName = language === "ar" ? (product.name_ar || product.name || "Product") : (product.name || "Product")
  const displayDescription = language === "ar" ? (product.description_ar || product.description || "") : (product.description || "")
  const displayShortDescription =
    language === "ar" ? (product.short_description_ar || product.short_description || "") : (product.short_description || "")
  const displayFeatures = language === "ar" ? (product.features_ar || product.features || []) : (product.features || [])
  const displayApplications = language === "ar" ? (product.applications_ar || product.applications || []) : (product.applications || [])

  useEffect(() => {
    // Check if product is in wishlist
    try {
      const wishlist = JSON.parse(localStorage.getItem("wishlist") || "[]")
      setIsWishlisted(wishlist.includes(product.id))
    } catch (error) {
      console.error("Error parsing wishlist:", error)
      setIsWishlisted(false)
    }

    // Increment view count when component mounts
    incrementViewCount("products", product.id).catch(console.error)
  }, [product.id])

  const handleWishlistToggle = async () => {
    try {
      const response = await fetch(`/api/products/${product.id}/wishlist`, {
        method: isWishlisted ? "DELETE" : "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })

      if (response.ok) {
        setIsWishlisted(!isWishlisted)

        // Update localStorage
        const wishlist = JSON.parse(localStorage.getItem("wishlist") || "[]")
        if (isWishlisted) {
          const updatedWishlist = wishlist.filter((id: string) => id !== product.id)
          localStorage.setItem("wishlist", JSON.stringify(updatedWishlist))
        } else {
          wishlist.push(product.id)
          localStorage.setItem("wishlist", JSON.stringify(wishlist))
        }
      }
    } catch (error) {
      console.error("Error updating wishlist:", error)
    }
  }

  const handleInquiry = () => {
    const message = t(
      "whatsapp.product_inquiry",
      "Hello! I'm interested in {product}. Please provide more information including pricing and availability.",
    ).replace("{product}", displayName)

    const whatsappUrl = `https://wa.me/+971501234567?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: displayName,
          text: displayShortDescription,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  const nextImage = () => {
    if (product.images && product.images.length > 0) {
      setSelectedImageIndex((prev) => (prev + 1) % product.images.length)
    }
  }

  const prevImage = () => {
    if (product.images && product.images.length > 0) {
      setSelectedImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length)
    }
  }

  const selectedImage = product.images?.[selectedImageIndex]

  return (
    <div className="max-w-7xl mx-auto">
      {/* Back Navigation */}
      <div className="mb-6">
        <Link
          href="/products"
          className="inline-flex items-center text-sm text-gray-600 hover:text-orange-600 transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t("nav.back_to_products", "Back to Products")}
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
            {selectedImage ? (
              <Image
                src={selectedImage.image_url || "/placeholder.svg"}
                alt={
                  language === "ar"
                    ? selectedImage.alt_text_ar || selectedImage.alt_text || displayName
                    : selectedImage.alt_text || displayName
                }
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-400">{t("no_image", "No Image Available")}</span>
              </div>
            )}

            {/* Image Navigation */}
            {product.images && product.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={t("btn.previous_image", "Previous Image")}
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={t("btn.next_image", "Next Image")}
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </>
            )}

            {/* Zoom Button */}
            <button className="absolute top-4 right-4 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Zoom className="h-4 w-4" />
            </button>
          </div>

          {/* Thumbnail Images */}
          {product.images && product.images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto">
              {product.images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-colors ${
                    index === selectedImageIndex ? "border-orange-600" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image.image_url || "/placeholder.svg"}
                    alt={`${displayName} ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Information */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{displayName}</h1>
                {product.sku && <p className="text-sm text-gray-500 font-mono">SKU: {product.sku}</p>}
              </div>
              <div className="flex items-center gap-2 ml-4">
                <button
                  onClick={handleWishlistToggle}
                  className={`p-2 rounded-full transition-colors ${
                    isWishlisted ? "bg-red-100 text-red-600" : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                  }`}
                  aria-label={t("btn.wishlist", "Add to Wishlist")}
                >
                  <Heart className={`h-5 w-5 ${isWishlisted ? "fill-current" : ""}`} />
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors"
                  aria-label={t("btn.share", "Share")}
                >
                  <Share2 className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              {product.is_new && <Badge className="bg-green-600">{t("badge.new", "New")}</Badge>}
              {product.is_bestseller && <Badge className="bg-orange-600">{t("badge.bestseller", "Bestseller")}</Badge>}
              {product.is_featured && <Badge className="bg-blue-600">{t("badge.featured", "Featured")}</Badge>}
              {product.brand && <Badge variant="outline">{product.brand}</Badge>}
            </div>

            {/* Rating and Views */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">4.8</span>
                <span>(127 {t("reviews", "reviews")})</span>
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>
                  {product.view_count || 0} {t("views", "views")}
                </span>
              </div>
            </div>

            {/* Price */}
            {product.price && (
              <div className="mb-6">
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-bold text-orange-600">
                    {product.currency} {product.price}
                  </span>
                  {product.unit && <span className="text-gray-500">/{product.unit}</span>}
                </div>
                {product.original_price && product.original_price > product.price && (
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-lg text-gray-500 line-through">
                      {product.currency} {product.original_price}
                    </span>
                    <Badge className="bg-red-600">
                      {Math.round(((product.original_price - product.price) / product.original_price) * 100)}%{" "}
                      {t("off", "OFF")}
                    </Badge>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Short Description */}
          {displayShortDescription && (
            <div>
              <p className="text-gray-700 leading-relaxed">{displayShortDescription}</p>
            </div>
          )}

          {/* Key Features */}
          {displayFeatures && displayFeatures.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">{t("key_features", "Key Features")}</h3>
              <ul className="space-y-2">
                {displayFeatures.slice(0, 5).map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 bg-orange-600 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Quantity and Actions */}
          <div className="space-y-4">
            {/* Quantity Selector */}
            <div className="flex items-center gap-4">
              <label className="font-medium text-gray-900">{t("quantity", "Quantity")}:</label>
              <div className="flex items-center border border-gray-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 hover:bg-gray-100 transition-colors"
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <span className="px-4 py-2 border-x border-gray-300 min-w-[60px] text-center">{quantity}</span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 hover:bg-gray-100 transition-colors"
                >
                  +
                </button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3">
              <Button onClick={handleInquiry} className="flex-1 bg-orange-600 hover:bg-orange-700 text-white" size="lg">
                <MessageCircle className="h-5 w-5 mr-2" />
                {t("btn.get_quote", "Get Quote")}
              </Button>
              <Button
                variant="outline"
                className="flex-1 border-orange-600 text-orange-600 hover:bg-orange-50 bg-transparent"
                size="lg"
              >
                <Phone className="h-5 w-5 mr-2" />
                {t("btn.call_now", "Call Now")}
              </Button>
            </div>

            {/* Additional Actions */}
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                <Download className="h-4 w-4 mr-2" />
                {t("btn.download_spec", "Download Spec")}
              </Button>
              <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                <Mail className="h-4 w-4 mr-2" />
                {t("btn.email_inquiry", "Email Inquiry")}
              </Button>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3 text-sm">
              <Truck className="h-4 w-4 text-green-600" />
              <span>{t("service.delivery", "Free delivery within IRAQ")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Shield className="h-4 w-4 text-blue-600" />
              <span>{t("service.warranty", "1 year warranty included")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Award className="h-4 w-4 text-purple-600" />
              <span>{t("service.certified", "ISO certified products")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Clock className="h-4 w-4 text-orange-600" />
              <span>{t("service.support", "24/7 customer support")}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Tabs */}
      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">{t("tab.overview", "Overview")}</TabsTrigger>
              <TabsTrigger value="specifications">{t("tab.specifications", "Specifications")}</TabsTrigger>
              <TabsTrigger value="applications">{t("tab.applications", "Applications")}</TabsTrigger>
              <TabsTrigger value="reviews">{t("tab.reviews", "Reviews")}</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold mb-4">{t("product_overview", "Product Overview")}</h3>
                {displayDescription ? (
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">{displayDescription}</div>
                ) : (
                  <p className="text-gray-500">{t("no_description", "No detailed description available.")}</p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="specifications" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">
                {t("technical_specifications", "Technical Specifications")}
              </h3>
              {product.technical_specs && Object.keys(product.technical_specs).length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(product.technical_specs).map(([key, value]) => (
                    <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                      <span className="font-medium text-gray-900">{key}:</span>
                      <span className="text-gray-700">{value}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">{t("no_specifications", "No technical specifications available.")}</p>
              )}
            </TabsContent>

            <TabsContent value="applications" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("applications", "Applications")}</h3>
              {displayApplications && displayApplications.length > 0 ? (
                <ul className="space-y-3">
                  {displayApplications.map((application, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <span className="w-2 h-2 bg-orange-600 rounded-full mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700">{application}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500">{t("no_applications", "No application information available.")}</p>
              )}
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("customer_reviews", "Customer Reviews")}</h3>
              <div className="space-y-4">
                {/* Review Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">4.8</div>
                      <div className="flex items-center justify-center gap-1 mb-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <div className="text-sm text-gray-600">127 {t("reviews", "reviews")}</div>
                    </div>
                    <div className="flex-1">
                      {[5, 4, 3, 2, 1].map((rating) => (
                        <div key={rating} className="flex items-center gap-2 mb-1">
                          <span className="text-sm w-3">{rating}</span>
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-yellow-400 h-2 rounded-full"
                              style={{
                                width: `${rating === 5 ? 70 : rating === 4 ? 20 : rating === 3 ? 7 : rating === 2 ? 2 : 1}%`,
                              }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-8">
                            {rating === 5 ? 89 : rating === 4 ? 25 : rating === 3 ? 9 : rating === 2 ? 3 : 1}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Sample Reviews */}
                <div className="space-y-4">
                  {[
                    {
                      name: "Ahmed Al-Rashid",
                      rating: 5,
                      date: "2024-01-15",
                      comment:
                        "Excellent quality product. Delivered on time and exactly as described. Highly recommended for construction projects.",
                    },
                    {
                      name: "Sarah Johnson",
                      rating: 4,
                      date: "2024-01-10",
                      comment:
                        "Good product overall. The quality is solid and the price is competitive. Customer service was helpful.",
                    },
                  ].map((review, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <div className="font-medium text-gray-900">{review.name}</div>
                          <div className="flex items-center gap-1 mt-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-4 w-4 ${star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
