"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { Eye, EyeOff, Lock } from "lucide-react"

export default function AdminLoginPage() {
  // JSON-LD for WebPage
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Admin Panel - DRYLEX Iraq",
    "description": "Admin panel for managing DRYLEX Iraq construction materials website",
    "url": "https://drylexiraq.com/admin",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Admin",
          "item": "https://drylexiraq.com/admin"
        }
      ]
    }
  };

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  };

  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const { toast } = useToast()
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: credentials.email,
          password: credentials.password
        })
      });

      if (response.ok) {
        const data = await response.json();

        if (data.success) {
          if (data.requiresMFA) {
            // Handle MFA flow - for now, we'll simulate MFA verification
            // In production, you'd show an MFA input form
            const mfaCode = prompt("Enter MFA code (any 6 digits for demo):");

            if (mfaCode && mfaCode.length === 6) {
              // Verify MFA
              const mfaResponse = await fetch('/api/auth/verify-mfa', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  sessionId: data.sessionId,
                  mfaCode: mfaCode
                })
              });

              if (mfaResponse.ok) {
                const mfaData = await mfaResponse.json();

                if (mfaData.success) {
                  // Store session data with JWT token
                  const sessionData = {
                    token: mfaData.token,
                    refreshToken: mfaData.refreshToken,
                    expiresAt: mfaData.expiresAt,
                    sessionId: data.sessionId,
                    timestamp: Date.now()
                  };

                  sessionStorage.setItem("adminSession", JSON.stringify(sessionData));

                  toast({
                    title: "Success",
                    description: "Login successful!",
                  });

                  // Use window.location for reliable redirect
                  window.location.href = "/admin/dashboard";
                } else {
                  setError(mfaData.message || "Invalid MFA code");
                }
              } else {
                const errorData = await mfaResponse.json();
                setError(errorData.message || "MFA verification failed");
              }
            } else {
              setError("MFA code is required");
            }
          } else {
            // Direct login without MFA (shouldn't happen with current setup)
            setError("Unexpected login flow");
          }
        } else {
          setError(data.message || "Invalid credentials");
        }
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Login failed");
      }
    } catch (error) {
      console.error('Login error:', error);
      setError("Login failed. Please check if the backend is running.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-orange-600 rounded-full flex items-center justify-center">
            <Lock className="h-6 w-6 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">Admin Login</h2>
          <p className="mt-2 text-sm text-gray-600">Sign in to access the admin dashboard</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Login to Dashboard</CardTitle>
            <CardDescription>Enter your credentials to access the admin panel</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-6" method="post">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  required
                  value={credentials.email}
                  onChange={(e) =>
                    setCredentials({
                      ...credentials,
                      email: e.target.value,
                    })
                  }
                  
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    required
                    value={credentials.password}
                    onChange={(e) =>
                      setCredentials({
                        ...credentials,
                        password: e.target.value,
                      })
                    }
                    placeholder="Enter your password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              {error && (
                <div className="text-red-600 text-sm mt-2">
                  {error}
                </div>
              )}

              <Button type="submit" className="w-full bg-orange-600 hover:bg-orange-700" disabled={isLoading}>
                {isLoading ? "Signing in..." : "Sign In"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
      
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
      />
    </div>
    </>
  )
}

