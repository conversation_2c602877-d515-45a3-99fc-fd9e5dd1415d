using Microsoft.AspNetCore.Identity;
using ConstructionWebsite.API.Models;
using Microsoft.EntityFrameworkCore;

namespace ConstructionWebsite.API.Data
{
    public static class SeedData
    {
        public static async Task Initialize(ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
        {
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();
    
            // Seed Roles
            await SeedRoles(roleManager);
    
            // Seed Admin User
            await SeedAdminUser(userManager);
    
            // Seed Categories
            await SeedCategories(context);
            
            // Save categories before seeding services
            await context.SaveChangesAsync();
    
            // Seed Services
            await SeedServices(context);
    
            // Save final changes
            await context.SaveChangesAsync();
        }

        private static async Task SeedRoles(RoleManager<IdentityRole> roleManager)
        {
            string[] roles = { "Admin", "Manager", "User" };

            foreach (string role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role))
                {
                    await roleManager.CreateAsync(new IdentityRole(role));
                }
            }
        }

        private static async Task SeedAdminUser(UserManager<ApplicationUser> userManager)
        {
            string adminEmail = "<EMAIL>";
            string adminPassword = "AdminPassword123!";
            string oldAdminEmail = "admin@DRYLEX .ae"; // Old email with space

            // Remove old admin user if it exists
            var oldAdmin = await userManager.FindByEmailAsync(oldAdminEmail);
            if (oldAdmin != null)
            {
                await userManager.DeleteAsync(oldAdmin);
            }

            // Create new admin user if it doesn't exist
            if (await userManager.FindByEmailAsync(adminEmail) == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = adminEmail,
                    Email = adminEmail,
                    EmailConfirmed = true,
                    FirstName = "Admin",
                    LastName = "User",
                    PhoneNumber = "+971501234567",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                var result = await userManager.CreateAsync(adminUser, adminPassword);
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "Admin");
                }
            }
        }

        private static async Task SeedCategories(ApplicationDbContext context)
        {
            if (!context.ServiceCategories.Any())
            {
                var categories = new List<ServiceCategory>
                {
                    new ServiceCategory
                    {
                        Name = "Concrete Admixtures",
                        NameAr = "إضافات الخرسانة",
                        Slug = "concrete-admixtures",
                        Description = "High-quality concrete admixtures for enhanced performance",
                        DescriptionAr = "إضافات خرسانة عالية الجودة لتحسين الأداء",
                        IsActive = true,
                        SortOrder = 1,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new ServiceCategory
                    {
                        Name = "Waterproofing & Sealing",
                        NameAr = "العزل المائي والختم",
                        Slug = "waterproofing-sealing",
                        Description = "Complete waterproofing and sealing solutions",
                        DescriptionAr = "حلول العزل المائي والختم الكاملة",
                        IsActive = true,
                        SortOrder = 2,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new ServiceCategory
                    {
                        Name = "Repairing & Injection",
                        NameAr = "الإصلاح والحقن",
                        Slug = "repairing-injection",
                        Description = "Professional repair and injection materials",
                        DescriptionAr = "مواد الإصلاح والحقن المهنية",
                        IsActive = true,
                        SortOrder = 3,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.ServiceCategories.AddRange(categories);
            }
        }

        private static async Task SeedServices(ApplicationDbContext context)
        {
            if (!context.Services.Any())
            {
                var category = context.ServiceCategories.First();
                
                var services = new List<Service>
                {
                    new Service
                    {
                        CategoryId = category.Id,
                        Name = "Concrete Repair",
                        NameAr = "إصلاح الخرسانة",
                        Slug = "concrete-repair",
                        ShortDescription = "Professional concrete repair services",
                        ShortDescriptionAr = "خدمات إصلاح الخرسانة المهنية",
                        Description = "Complete concrete repair and restoration services using advanced materials and techniques.",
                        DescriptionAr = "خدمات إصلاح وترميم الخرسانة الكاملة باستخدام مواد وتقنيات متقدمة.",
                        Status = "active",
                        IsFeatured = true,
                        SortOrder = 1,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    },
                    new Service
                    {
                        CategoryId = category.Id,
                        Name = "Waterproofing Solutions",
                        NameAr = "حلول العزل المائي",
                        Slug = "waterproofing-solutions",
                        ShortDescription = "Advanced waterproofing systems",
                        ShortDescriptionAr = "أنظمة العزل المائي المتقدمة",
                        Description = "Comprehensive waterproofing solutions for all types of structures.",
                        DescriptionAr = "حلول العزل المائي الشاملة لجميع أنواع الهياكل.",
                        Status = "active",
                        IsFeatured = false,
                        SortOrder = 2,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    }
                };

                context.Services.AddRange(services);
            }
        }
    }
}