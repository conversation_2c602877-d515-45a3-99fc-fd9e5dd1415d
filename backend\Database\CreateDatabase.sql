-- Construction Website Database Setup Script
-- This script creates the complete database structure for the Construction Website

USE master;
GO

-- Create Database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'ConstructionWebsiteDB')
BEGIN
    CREATE DATABASE ConstructionWebsiteDB;
END
GO

USE ConstructionWebsiteDB;
GO

-- Create AspNetUsers table (Identity Framework)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetUsers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AspNetUsers] (
        [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
        [UserName] NVARCHAR(256) NULL,
        [NormalizedUserName] NVARCHAR(256) NULL,
        [Email] NVARCHAR(256) NULL,
        [NormalizedEmail] NVARCHAR(256) NULL,
        [EmailConfirmed] BIT NOT NULL,
        [PasswordHash] NVARCHAR(MAX) NULL,
        [SecurityStamp] NVARCHAR(MAX) NULL,
        [ConcurrencyStamp] NVARCHAR(MAX) NULL,
        [PhoneNumber] NVARCHAR(MAX) NULL,
        [PhoneNumberConfirmed] BIT NOT NULL,
        [TwoFactorEnabled] BIT NOT NULL,
        [LockoutEnd] DATETIMEOFFSET(7) NULL,
        [LockoutEnabled] BIT NOT NULL,
        [AccessFailedCount] INT NOT NULL,
        [FirstName] NVARCHAR(100) NULL,
        [LastName] NVARCHAR(100) NULL,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- Create AspNetRoles table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetRoles' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AspNetRoles] (
        [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
        [Name] NVARCHAR(256) NULL,
        [NormalizedName] NVARCHAR(256) NULL,
        [ConcurrencyStamp] NVARCHAR(MAX) NULL
    );
END
GO

-- Create AspNetUserRoles table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AspNetUserRoles' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[AspNetUserRoles] (
        [UserId] NVARCHAR(450) NOT NULL,
        [RoleId] NVARCHAR(450) NOT NULL,
        PRIMARY KEY ([UserId], [RoleId]),
        FOREIGN KEY ([UserId]) REFERENCES [AspNetUsers] ([Id]) ON DELETE CASCADE,
        FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE
    );
END
GO

-- Create Categories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Categories' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Categories] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(100) NOT NULL,
        [NameAr] NVARCHAR(100) NULL,
        [Description] NVARCHAR(500) NULL,
        [DescriptionAr] NVARCHAR(500) NULL,
        [Slug] NVARCHAR(100) NOT NULL UNIQUE,
        [ImageUrl] NVARCHAR(255) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [DisplayOrder] INT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- Create Products table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Products] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(200) NOT NULL,
        [NameAr] NVARCHAR(200) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DescriptionAr] NVARCHAR(MAX) NULL,
        [Slug] NVARCHAR(200) NOT NULL UNIQUE,
        [SKU] NVARCHAR(50) NULL,
        [Price] DECIMAL(18,2) NULL,
        [CategoryId] INT NOT NULL,
        [Brand] NVARCHAR(100) NULL,
        [ImageUrl] NVARCHAR(255) NULL,
        [Images] NVARCHAR(MAX) NULL, -- JSON array of image URLs
        [Specifications] NVARCHAR(MAX) NULL, -- JSON object
        [Features] NVARCHAR(MAX) NULL, -- JSON array
        [Applications] NVARCHAR(MAX) NULL, -- JSON array
        [DatasheetUrl] NVARCHAR(255) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [IsFeatured] BIT NOT NULL DEFAULT 0,
        [ViewCount] INT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id])
    );
END
GO

-- Create Services table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Services' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Services] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(200) NOT NULL,
        [NameAr] NVARCHAR(200) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DescriptionAr] NVARCHAR(MAX) NULL,
        [Slug] NVARCHAR(200) NOT NULL UNIQUE,
        [ShortDescription] NVARCHAR(500) NULL,
        [ShortDescriptionAr] NVARCHAR(500) NULL,
        [ImageUrl] NVARCHAR(255) NULL,
        [Icon] NVARCHAR(100) NULL,
        [Features] NVARCHAR(MAX) NULL, -- JSON array
        [IsActive] BIT NOT NULL DEFAULT 1,
        [IsFeatured] BIT NOT NULL DEFAULT 0,
        [DisplayOrder] INT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- Create Projects table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Projects' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Projects] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Title] NVARCHAR(200) NOT NULL,
        [TitleAr] NVARCHAR(200) NULL,
        [Description] NVARCHAR(MAX) NULL,
        [DescriptionAr] NVARCHAR(MAX) NULL,
        [Slug] NVARCHAR(200) NOT NULL UNIQUE,
        [Client] NVARCHAR(200) NULL,
        [Location] NVARCHAR(200) NULL,
        [StartDate] DATE NULL,
        [EndDate] DATE NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Completed',
        [ImageUrl] NVARCHAR(255) NULL,
        [Images] NVARCHAR(MAX) NULL, -- JSON array of image URLs
        [Technologies] NVARCHAR(MAX) NULL, -- JSON array
        [Challenges] NVARCHAR(MAX) NULL,
        [ChallengesAr] NVARCHAR(MAX) NULL,
        [Solutions] NVARCHAR(MAX) NULL,
        [SolutionsAr] NVARCHAR(MAX) NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [IsFeatured] BIT NOT NULL DEFAULT 0,
        [ViewCount] INT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- Create ContactMessages table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ContactMessages' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ContactMessages] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Name] NVARCHAR(100) NOT NULL,
        [Email] NVARCHAR(255) NOT NULL,
        [Phone] NVARCHAR(20) NULL,
        [Company] NVARCHAR(200) NULL,
        [Subject] NVARCHAR(200) NOT NULL,
        [Message] NVARCHAR(MAX) NOT NULL,
        [IsRead] BIT NOT NULL DEFAULT 0,
        [IsReplied] BIT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE()
    );
END
GO

-- Create NewsletterSubscriptions table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='NewsletterSubscriptions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[NewsletterSubscriptions] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [Email] NVARCHAR(255) NOT NULL UNIQUE,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [SubscribedAt] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        [UnsubscribedAt] DATETIME2 NULL
    );
END
GO

-- Insert default categories
IF NOT EXISTS (SELECT * FROM Categories)
BEGIN
    INSERT INTO Categories (Name, NameAr, Description, DescriptionAr, Slug, DisplayOrder) VALUES
    ('Waterproofing', 'العزل المائي', 'Complete waterproofing solutions for all construction needs', 'حلول العزل المائي الشاملة لجميع احتياجات البناء', 'waterproofing', 1),
    ('Thermal Insulation', 'العزل الحراري', 'High-performance thermal insulation materials', 'مواد العزل الحراري عالية الأداء', 'thermal-insulation', 2),
    ('Concrete Additives', 'إضافات الخرسانة', 'Advanced concrete additives and admixtures', 'إضافات ومحسنات الخرسانة المتقدمة', 'concrete-additives', 3),
    ('Sealants & Adhesives', 'المواد اللاصقة والمانعة للتسرب', 'Professional grade sealants and adhesives', 'مواد لاصقة ومانعة للتسرب بجودة احترافية', 'sealants-adhesives', 4),
    ('Drylex Products', 'منتجات درايلكس', 'Premium Drylex waterproofing and construction materials', 'مواد البناء والعزل المائي المتميزة من درايلكس', 'drylex-products', 5);
END
GO

-- Insert default services
IF NOT EXISTS (SELECT * FROM Services)
BEGIN
    INSERT INTO Services (Name, NameAr, Description, DescriptionAr, Slug, ShortDescription, ShortDescriptionAr, Icon, DisplayOrder) VALUES
    ('Waterproofing Solutions', 'حلول العزل المائي', 'Complete waterproofing services for residential and commercial projects', 'خدمات العزل المائي الشاملة للمشاريع السكنية والتجارية', 'waterproofing-solutions', 'Professional waterproofing for all surfaces', 'عزل مائي احترافي لجميع الأسطح', 'droplets', 1),
    ('Thermal Insulation', 'العزل الحراري', 'Energy-efficient thermal insulation installation', 'تركيب العزل الحراري الموفر للطاقة', 'thermal-insulation', 'Reduce energy costs with proper insulation', 'قلل تكاليف الطاقة بالعزل المناسب', 'thermometer', 2),
    ('Concrete Repair', 'إصلاح الخرسانة', 'Structural concrete repair and restoration services', 'خدمات إصلاح وترميم الخرسانة الإنشائية', 'concrete-repair', 'Restore structural integrity', 'استعادة السلامة الإنشائية', 'hammer', 3),
    ('Consultation Services', 'خدمات الاستشارة', 'Expert technical consultation for construction projects', 'استشارات تقنية متخصصة لمشاريع البناء', 'consultation-services', 'Professional technical guidance', 'إرشاد تقني احترافي', 'users', 4);
END
GO

-- Create indexes for better performance
CREATE NONCLUSTERED INDEX [IX_Products_CategoryId] ON [dbo].[Products] ([CategoryId]);
CREATE NONCLUSTERED INDEX [IX_Products_Slug] ON [dbo].[Products] ([Slug]);
CREATE NONCLUSTERED INDEX [IX_Products_IsActive] ON [dbo].[Products] ([IsActive]);
CREATE NONCLUSTERED INDEX [IX_Products_IsFeatured] ON [dbo].[Products] ([IsFeatured]);
CREATE NONCLUSTERED INDEX [IX_Categories_Slug] ON [dbo].[Categories] ([Slug]);
CREATE NONCLUSTERED INDEX [IX_Services_Slug] ON [dbo].[Services] ([Slug]);
CREATE NONCLUSTERED INDEX [IX_Projects_Slug] ON [dbo].[Projects] ([Slug]);
CREATE NONCLUSTERED INDEX [IX_ContactMessages_CreatedAt] ON [dbo].[ContactMessages] ([CreatedAt]);
GO

PRINT 'Database setup completed successfully!';
GO