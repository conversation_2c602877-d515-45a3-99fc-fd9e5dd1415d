version: 1
frontend:
  phases:
    preBuild:
      commands:
        - echo Using Node $(node -v)
        - echo Activating pnpm via Corepack
        - corepack enable || true
        - corepack prepare pnpm@10.6.3 --activate || npm i -g pnpm@10.6.3
        - pnpm -v
        - pnpm install --frozen-lockfile
    build:
      commands:
        - pnpm run build
  artifacts:
    # Amplify detects Next.js SSR and serves from .next automatically
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .pnpm-store/**/*
      - .next/cache/**/*

