"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { MapPin, Phone, Mail, Clock, MessageCircle, Send } from "lucide-react"
import { db } from "@/lib/database"
import { AnimatedSection } from "@/components/animated-section"
import Head from "next/head"
import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card"
import { Settings } from "@/types"

// Get settings with default values
const settings = db.getSettings() || {
  address: '', phone: '', email: '', whatsappNumber: ''
}

// JSON-LD for Organization
const organizationJsonLd = {
  "@context": "https://schema.org",
  "@type": "Organization",
  "@id": "https://drylexiraq.com/#organization",
  "name": "DRYLEX IRAQ",
  "legalName": "DRYLEX Iraq Construction Materials Distribution Company",
  "alternateName": "Drylex Iraq Distribution",
  "url": "https://drylexiraq.com",
  "logo": {
    "@type": "ImageObject",
    "url": "https://drylexiraq.com/images/logo.png",
    "width": "300",
    "height": "300"
  },
  "description": "Authorized distributor of Drylex construction materials in Iraq since 1995, providing premium construction materials and professional services across Iraq",
  "foundingDate": "1995",
  "founder": {
    "@type": "Organization",
    "name": "DRYLEX Group"
  },
  "numberOfEmployees": "50",
  "industry": "Construction Materials",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": settings.address,
    "addressLocality": "Nassiriya",
    "addressRegion": "Thi Qar Governorate",
    "postalCode": "64001",
    "addressCountry": "Iraq"
  },
  "contactPoint": {
    "@type": "ContactPoint",
    "telephone": settings.phone,
    "email": settings.email,
    "contactType": "customer service",
    "areaServed": "IQ",
    "availableLanguage": ["en", "ar"],
    "contactOption": "TollFree"
  },
  "sameAs": [
    "https://facebook.com/drylexiraq",
    "https://instagram.com/drylexiraq",
    "https://linkedin.com/company/drylexiraq",
    "https://youtube.com/drylexiraq"
  ],
  "areaServed": {
    "@type": "Country",
    "name": "Iraq"
  },
  "image": "https://drylexiraq.com/images/logo.png",
  "knowsAbout": [
    "construction materials",
    "building materials",
    "construction services",
    "construction supplies",
    "building supplies"
  ]
}

// JSON-LD for ContactPage
const contactJsonLd = {
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "@id": "https://drylexiraq.com/contact",
  "name": "Contact DRYLEX Iraq",
  "description": "Get in touch with DRYLEX Iraq for premium construction materials and services",
  "url": "https://drylexiraq.com/contact",
  "mainEntityOfPage": {
    "@type": "ContactPoint",
    "telephone": settings.phone,
    "contactType": "customer service",
    "areaServed": "IQ",
    "availableLanguage": ["en", "ar"],
    "contactOption": "TollFree",
    "hoursAvailable": [
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday"
        ],
        "opens": "08:00",
        "closes": "18:00"
      },
      {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": "Friday",
        "opens": "08:00",
        "closes": "12:00"
      }
    ]
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://drylexiraq.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Contact",
        "item": "https://drylexiraq.com/contact"
      }
    ]
  }
}

// JSON-LD for LocalBusiness (enhanced location information)
const localBusinessJsonLd = {
  "@context": "https://schema.org",
  "@type": "LocalBusiness",
  "name": "DRYLEX IRAQ",
  "address": {
    "@type": "PostalAddress",
    "streetAddress": settings.address,
    "addressLocality": "Nassiriya",
    "addressRegion": "Thi Qar Governorate",
    "postalCode": "64001",
    "addressCountry": "Iraq"
  },
  "telephone": settings.phone,
  "email": settings.email,
  "url": "https://drylexiraq.com",
  "openingHoursSpecification": [
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday"
      ],
      "opens": "08:00",
      "closes": "18:00"
    },
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": "Friday",
      "opens": "08:00",
      "closes": "12:00"
    }
  ],
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 31.0461,
    "longitude": 46.2595
  }
}

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  
  // Fetch settings from database
  const settings = db.getSettings() as Settings

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Save message to database
      await db.createMessage({
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        subject: formData.subject,
        message: formData.message,
        status: "unread",
      })

      toast({
        title: "Message Sent Successfully!",
        description: "We'll get back to you within 24 hours.",
      })

      setFormData({
        name: "",
        email: "",
        phone: "",
        subject: "",
        message: "",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const contactInfo = [
    {
      icon: MapPin,
      title: "Our Location",
      details: ["DRYLEX Iraq", "Alshimoukh district, district 205, zukak 68, building 40", "Nassiriya, Thi Qar, Iraq", "حي الشموخ - مقابل مجمع الحضارات (تينا) - الناصرية- ذي قار - العراق"],
    },
    {
      icon: Phone,
      title: "Phone Number",
      details: ["+9647867100886"],
    },
    {
      icon: Mail,
      title: "Email Address",
      details: ["<EMAIL>"],
    },
    {
      icon: Clock,
      title: "Business Hours",
      details: ["Sunday - Thursday: 8:00 AM - 6:00 PM", "Friday: 8:00 AM - 12:00 PM", "Saturday: Closed"],
    },
  ]

  return (
    <>
      <Head>
        <title>Contact DRYLEX Iraq | Construction Materials & Services - Get Expert Consultation</title>
        <meta
          name="description"
          content="Contact DRYLEX Iraq for construction materials, expert consultation and professional services. Located in Nassiriya, Thi Qar, serving all Iraq. Call +9647867100886."
        />
        <meta
          name="keywords"
          content="contact DRYLEX Iraq, construction materials consultation, building materials supplier contact, construction services Iraq, Nassiriya construction company"
        />
        <link rel="canonical" href="https://drylexiraq.com/contact" />
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(contactJsonLd) }} />
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(localBusinessJsonLd) }} />
      </Head>

      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 py-8">
        {/* Hero Section with Animation */}
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16 px-4"
        >
          <motion.div
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="inline-block p-1 rounded-full bg-orange-100 mb-6"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-amber-500">
              Contact DRYLEX Iraq
            </h1>
          </motion.div>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Get in touch with our construction materials experts for professional consultation and support across Iraq
          </motion.p>
        </motion.section>

        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form with Glassmorphism Effect */}
            <motion.section
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-2"
            >
              <Card className="backdrop-blur-sm bg-white/80 border-none shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-orange-600" />
                    Send us a Message
                  </CardTitle>
                  <CardDescription>
                    Fill out the form below and we'll get back to you within 24 hours with expert advice
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name *</Label>
                        <motion.div whileFocus={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            required
                            placeholder="Your full name"
                            className="transition-all duration-300 focus:ring-2 focus:ring-orange-500/30"
                          />
                        </motion.div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address *</Label>
                        <motion.div whileFocus={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            required
                            placeholder="<EMAIL>"
                            className="transition-all duration-300 focus:ring-2 focus:ring-orange-500/30"
                          />
                        </motion.div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <motion.div whileFocus={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                          <Input
                            id="phone"
                            name="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={handleInputChange}
                            placeholder="+9647867100886"
                            className="transition-all duration-300 focus:ring-2 focus:ring-orange-500/30"
                          />
                        </motion.div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="subject">Subject *</Label>
                        <motion.div whileFocus={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                          <Input
                            id="subject"
                            name="subject"
                            value={formData.subject}
                            onChange={handleInputChange}
                            required
                            placeholder="How can we help you?"
                            className="transition-all duration-300 focus:ring-2 focus:ring-orange-500/30"
                          />
                        </motion.div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="message">Message *</Label>
                      <motion.div whileFocus={{ scale: 1.02 }} transition={{ type: "spring", stiffness: 300 }}>
                        <Textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleInputChange}
                          required
                          rows={6}
                          placeholder="Please provide details about your construction materials or services inquiry..."
                          className="transition-all duration-300 focus:ring-2 focus:ring-orange-500/30"
                        />
                      </motion.div>
                    </div>

                    <div className="flex flex-col sm:flex-row gap-4 pt-2">
                      <motion.div
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className="bg-gradient-to-r from-orange-600 to-amber-500 hover:from-orange-700 hover:to-amber-600 text-white font-medium py-6 px-8 shadow-lg hover:shadow-xl transform transition-all duration-300"
                          aria-label="Send message to DRYLEX Iraq"
                        >
                          {isSubmitting ? (
                            "Sending..."
                          ) : (
                            <>
                              <Send className="h-5 w-5 mr-2" />
                              Send Message
                            </>
                          )}
                        </Button>
                      </motion.div>
                      <motion.div
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      >
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            const message =
                              "Hello! I'm interested in your construction materials and services. Please contact me."
                            const whatsappUrl = `https://wa.me/+9647867100886?text=${encodeURIComponent(message)}`
                            window.open(whatsappUrl, "_blank")
                          }}
                          className="border-2 border-green-500 text-green-600 hover:bg-green-50 font-medium py-6 px-8 shadow-md hover:shadow-lg transform transition-all duration-300"
                          aria-label="Contact DRYLEX via WhatsApp"
                        >
                          <MessageCircle className="h-5 w-5 mr-2" />
                          WhatsApp
                        </Button>
                      </motion.div>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </motion.section>

            {/* Contact Information with Tabs */}
            <motion.section
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="space-y-6"
            >
              <Card className="backdrop-blur-sm bg-white/80 border-none shadow-xl">
                <CardHeader>
                  <CardTitle className="text-2xl">Contact Information</CardTitle>
                  <CardDescription>
                    Get in touch through your preferred channel
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="general" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-6">
                      <TabsTrigger value="general" className="data-[state=active]:bg-orange-100">
                        General
                      </TabsTrigger>
                      <TabsTrigger value="support" className="data-[state=active]:bg-orange-100">
                        Support
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="general" className="mt-0">
                      <div className="space-y-6">
                        {contactInfo.filter(info => info.title !== "Business Hours").map((info, index) => (
                          <HoverCard key={index}>
                            <HoverCardTrigger asChild>
                              <div className="flex items-start gap-3 p-3 rounded-lg hover:bg-orange-50 transition-colors duration-200 cursor-pointer">
                                <div className="bg-orange-100 p-2 rounded-full flex-shrink-0">
                                  <info.icon className="h-5 w-5 text-orange-600" />
                                </div>
                                <div className="space-y-1">
                                  <p className="font-medium text-gray-900">{info.title}</p>
                                  {info.details.map((detail, idx) => (
                                    <p key={idx} className="text-sm text-gray-600">
                                      {detail}
                                    </p>
                                  ))}
                                </div>
                              </div>
                            </HoverCardTrigger>
                            <HoverCardContent className="w-80">
                              <div className="space-y-1">
                                {info.details.map((detail, idx) => (
                                  <p key={idx} className="text-sm text-gray-600">
                                    {detail}
                                  </p>
                                ))}
                              </div>
                            </HoverCardContent>
                          </HoverCard>
                        ))}
                      </div>
                    </TabsContent>
                    <TabsContent value="support" className="mt-0">
                      <div className="space-y-6">
                        {contactInfo.filter(info => info.title === "Business Hours").map((info, index) => (
                          <HoverCard key={index}>
                            <HoverCardTrigger asChild>
                              <div className="flex items-start gap-3 p-3 rounded-lg hover:bg-orange-50 transition-colors duration-200 cursor-pointer">
                                <div className="bg-orange-100 p-2 rounded-full flex-shrink-0">
                                  <info.icon className="h-5 w-5 text-orange-600" />
                                </div>
                                <div className="space-y-1">
                                  <p className="font-medium text-gray-900">{info.title}</p>
                                  {info.details.map((detail, idx) => (
                                    <p key={idx} className="text-sm text-gray-600">
                                      {detail}
                                    </p>
                                  ))}
                                </div>
                              </div>
                            </HoverCardTrigger>
                            <HoverCardContent className="w-80">
                              <div className="space-y-1">
                                {info.details.map((detail, idx) => (
                                  <p key={idx} className="text-sm text-gray-600">
                                    {detail}
                                  </p>
                                ))}
                              </div>
                            </HoverCardContent>
                          </HoverCard>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>

              {/* Quick Actions with Enhanced Design */}
              <Card className="backdrop-blur-sm bg-white/80 border-none shadow-xl">
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 300 }}>
                    <Button
                      variant="outline"
                      className="w-full justify-start bg-transparent hover:bg-orange-50 transition-colors duration-200"
                      onClick={() => window.open("tel:+9647867100886")}
                      aria-label="Call DRYLEX Iraq now"
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Call Now
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 300 }}>
                    <Button
                      variant="outline"
                      className="w-full justify-start bg-transparent hover:bg-orange-50 transition-colors duration-200"
                      onClick={() => window.open("mailto:<EMAIL>")}
                      aria-label="Send email to DRYLEX Iraq"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Send Email
                    </Button>
                  </motion.div>
                  <motion.div whileHover={{ x: 5 }} transition={{ type: "spring", stiffness: 300 }}>
                    <Button
                      variant="outline"
                      className="w-full justify-start bg-transparent hover:bg-orange-50 transition-colors duration-200"
                      onClick={() => {
                        const message = "Hello! I'm interested in your construction materials and services."
                        const whatsappUrl = `https://wa.me/+9647867100886?text=${encodeURIComponent(message)}`
                        window.open(whatsappUrl, "_blank")
                      }}
                      aria-label="Chat with DRYLEX Iraq on WhatsApp"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      WhatsApp Chat
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.section>
          </div>

          {/* Enhanced Map Section with Interactive Elements */}
          <motion.section
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="mt-12"
          >
            <Card className="backdrop-blur-sm bg-white/80 border-none shadow-xl overflow-hidden">
              <CardHeader>
                <CardTitle className="text-2xl flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-orange-600" />
                  Find DRYLEX Iraq
                </CardTitle>
                <CardDescription>
                  Visit our showroom and warehouse facility in Nassiriya, Thi Qar
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="aspect-video w-full rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300">
                  <div className="h-full w-full relative">
                    {/* Replace with actual map integration */}
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-indigo-200 flex items-center justify-center">
                      <div className="text-center p-6 bg-white/90 rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105">
                        <MapPin className="h-16 w-16 text-red-500 mx-auto mb-4" />
                        <p className="text-gray-800 font-medium text-lg mb-2">Alshimoukh District</p>
                        <p className="text-sm text-gray-600 mb-1">Nassiriya, Thi Qar, Iraq</p>
                        <p className="text-xs text-gray-400 mt-1">Click to view on Google Maps</p>
                      </div>
                    </div>
                    <button
                      className="absolute inset-0 w-full h-full cursor-pointer opacity-0"
                      onClick={() => window.open("https://www.google.com/maps", "_blank")}
                      aria-label="Open Google Maps"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.section>
        </div>
      </div>
    </>
  )
}
