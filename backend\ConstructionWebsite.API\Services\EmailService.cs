using ConstructionWebsite.API.Models;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;

namespace ConstructionWebsite.API.Services
{
    public class EmailService : IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        private readonly SmtpClient _smtpClient;
        private readonly string _fromEmail;
        private readonly string _fromName;

        public EmailService(IConfiguration configuration, ILogger<EmailService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            var smtpHost = _configuration["Email:SmtpHost"] ?? "smtp.gmail.com";
            var smtpPort = _configuration.GetValue<int>("Email:SmtpPort", 587);
            var username = _configuration["Email:Username"] ?? "";
            var password = _configuration["Email:Password"] ?? "";
            var enableSsl = _configuration.GetValue<bool>("Email:EnableSsl", true);
            
            _fromEmail = _configuration["Email:FromEmail"] ?? username;
            _fromName = _configuration["Email:FromName"] ?? "Construction Website";

            _smtpClient = new SmtpClient(smtpHost, smtpPort)
            {
                Credentials = new NetworkCredential(username, password),
                EnableSsl = enableSsl,
                DeliveryMethod = SmtpDeliveryMethod.Network
            };
        }

        public async Task SendEmailAsync(string to, string subject, string body, bool isHtml = true)
        {
            await SendEmailAsync(to, subject, body, null, null, isHtml);
        }

        public async Task SendEmailAsync(string to, string subject, string body, IEnumerable<string>? cc = null, IEnumerable<string>? bcc = null, bool isHtml = true)
        {
            try
            {
                using var message = new MailMessage();
                message.From = new MailAddress(_fromEmail, _fromName);
                message.To.Add(to);
                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = isHtml;

                if (cc != null)
                {
                    foreach (var ccEmail in cc)
                    {
                        message.CC.Add(ccEmail);
                    }
                }

                if (bcc != null)
                {
                    foreach (var bccEmail in bcc)
                    {
                        message.Bcc.Add(bccEmail);
                    }
                }

                await _smtpClient.SendMailAsync(message);
                _logger.LogInformation("Email sent successfully to {To} with subject: {Subject}", to, subject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {To} with subject: {Subject}", to, subject);
                throw;
            }
        }

        public async Task SendContactNotificationAsync(ContactMessage contactMessage)
        {
            try
            {
                var adminEmail = _configuration["Email:AdminEmail"] ?? _fromEmail;
                var subject = $"New Contact Message: {contactMessage.Subject}";
                
                var body = $@"
                    <h2>New Contact Message Received</h2>
                    <p><strong>Name:</strong> {contactMessage.Name}</p>
                    <p><strong>Email:</strong> {contactMessage.Email}</p>
                    <p><strong>Phone:</strong> {contactMessage.Phone ?? "Not provided"}</p>
                    <p><strong>Subject:</strong> {contactMessage.Subject}</p>
                    <p><strong>Message:</strong></p>
                    <div style='background-color: #f5f5f5; padding: 10px; border-left: 4px solid #007bff;'>
                        {contactMessage.Message.Replace("\n", "<br>")}
                    </div>
                    <p><strong>Received:</strong> {contactMessage.CreatedAt:yyyy-MM-dd HH:mm:ss} UTC</p>
                ";

                await SendEmailAsync(adminEmail, subject, body, isHtml: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send contact notification email for message ID {ContactMessageId}", contactMessage.Id);
                throw;
            }
        }

        public async Task SendWelcomeEmailAsync(string email, string userName)
        {
            try
            {
                var subject = "Welcome to Construction Website!";
                var body = $@"
                    <h2>Welcome to Construction Website!</h2>
                    <p>Dear {userName},</p>
                    <p>Thank you for registering with us. We're excited to have you as part of our community.</p>
                    <p>You can now access all the features of our platform including:</p>
                    <ul>
                        <li>Browse our construction services</li>
                        <li>View our project portfolio</li>
                        <li>Contact us for quotes</li>
                        <li>Manage your account preferences</li>
                    </ul>
                    <p>If you have any questions, feel free to contact our support team.</p>
                    <p>Best regards,<br>Construction Website Team</p>
                ";

                await SendEmailAsync(email, subject, body, isHtml: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send welcome email to {Email}", email);
                throw;
            }
        }

        public async Task SendPasswordResetEmailAsync(string email, string resetToken, string resetUrl)
        {
            try
            {
                var subject = "Password Reset Request";
                var body = $@"
                    <h2>Password Reset Request</h2>
                    <p>You have requested to reset your password for your Construction Website account.</p>
                    <p>Click the link below to reset your password:</p>
                    <p><a href='{resetUrl}?token={resetToken}&email={Uri.EscapeDataString(email)}' 
                         style='background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>
                         Reset Password
                       </a></p>
                    <p>This link will expire in 1 hour for security reasons.</p>
                    <p>If you did not request this password reset, please ignore this email.</p>
                    <p>Best regards,<br>Construction Website Team</p>
                ";

                await SendEmailAsync(email, subject, body, isHtml: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send password reset email to {Email}", email);
                throw;
            }
        }

        public async Task SendPasswordChangedNotificationAsync(string email, string userName)
        {
            try
            {
                var subject = "Password Changed Successfully";
                var body = $@"
                    <h2>Password Changed</h2>
                    <p>Dear {userName},</p>
                    <p>Your password has been successfully changed for your Construction Website account.</p>
                    <p>If you did not make this change, please contact our support team immediately.</p>
                    <p>For security reasons, you may need to log in again on all your devices.</p>
                    <p>Best regards,<br>Construction Website Team</p>
                ";

                await SendEmailAsync(email, subject, body, isHtml: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send password changed notification to {Email}", email);
                throw;
            }
        }

        public async Task SendBulkEmailAsync(IEnumerable<string> recipients, string subject, string body, bool isHtml = true)
        {
            var tasks = recipients.Select(recipient => 
                SendEmailAsync(recipient, subject, body, isHtml: isHtml));
            
            try
            {
                await Task.WhenAll(tasks);
                _logger.LogInformation("Bulk email sent successfully to {RecipientCount} recipients", recipients.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send bulk email to some recipients");
                throw;
            }
        }

        public async Task<bool> ValidateEmailAddressAsync(string email)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(email))
                    return false;

                // Basic email format validation
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
                if (!emailRegex.IsMatch(email))
                    return false;

                // Additional validation can be added here (e.g., DNS lookup)
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating email address: {Email}", email);
                return false;
            }
        }

        public async Task SendInquiryNotificationAsync(ProductInquiry inquiry, Product product)
        {
            try
            {
                var adminEmail = _configuration["Email:AdminEmail"] ?? _fromEmail;
                var subject = $"New Product Inquiry: {product.Name}";
                
                var body = $@"
                    <h2>New Product Inquiry Received</h2>
                    <p><strong>Product:</strong> {product.Name}</p>
                    <p><strong>Customer Name:</strong> {inquiry.Name}</p>
                    <p><strong>Email:</strong> {inquiry.Email}</p>
                    <p><strong>Phone:</strong> {inquiry.Phone ?? "Not provided"}</p>
                    <p><strong>Company:</strong> {inquiry.Company ?? "Not provided"}</p>
                    <p><strong>Subject:</strong> {inquiry.Subject}</p>
                    <p><strong>Inquiry Type:</strong> {inquiry.InquiryType}</p>
                    <p><strong>Source:</strong> {inquiry.Source}</p>
                    <p><strong>Message:</strong></p>
                    <div style='background-color: #f5f5f5; padding: 10px; border-left: 4px solid #007bff;'>
                        {inquiry.Message.Replace("\n", "<br>")}
                    </div>
                    <p><strong>Received:</strong> {inquiry.CreatedAt:yyyy-MM-dd HH:mm:ss} UTC</p>
                ";

                await SendEmailAsync(adminEmail, subject, body, true);
                _logger.LogInformation("Product inquiry notification sent for inquiry ID: {InquiryId}", inquiry.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending product inquiry notification for inquiry ID: {InquiryId}", inquiry.Id);
                throw;
            }
        }

        public void Dispose()
        {
            _smtpClient?.Dispose();
        }
    }
}