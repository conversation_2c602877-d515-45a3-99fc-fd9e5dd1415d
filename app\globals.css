@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 193 79% 38%; /* DRYLEX Primary Blue */
    --primary-foreground: 0 0% 100%;
    --secondary: 188 94% 95%;
    --secondary-foreground: 188 94% 20%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 188 94% 95%;
    --accent-foreground: 188 94% 20%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 193 79% 38%;
    --radius: 0.5rem;
    --chart-1: 193 79% 38%;
    --chart-2: 188 70% 35%;
    --chart-3: 188 50% 25%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 193 79% 55%; /* DRYLEX Primary Blue - lighter for dark mode */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 193 79% 55%;
    --chart-1: 193 79% 55%;
    --chart-2: 188 70% 45%;
    --chart-3: 188 50% 35%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", sans-serif;
    transition: font-family 0.3s ease;
  }

  /* RTL Support */
  html[dir="rtl"] {
    direction: rtl;
  }

  html[dir="ltr"] {
    direction: ltr;
  }

  /* Font switching for RTL */
  body.rtl {
    font-family: "Cairo", sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
  }

  body.ltr {
    font-family: "Inter", sans-serif;
    line-height: 1.6;
    letter-spacing: normal;
  }

  /* RTL Typography */
  body.rtl h1,
  body.rtl h2,
  body.rtl h3,
  body.rtl h4,
  body.rtl h5,
  body.rtl h6 {
    line-height: 1.4;
    font-weight: 600;
  }

  /* RTL Flexbox utilities */
  .rtl .flex-row {
    flex-direction: row-reverse;
  }

  .rtl .space-x-1 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.25rem * var(--tw-space-x-reverse));
    margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .rtl .space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  /* RTL Margin utilities */
  .rtl .ml-1 {
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .rtl .ml-2 {
    margin-right: 0.5rem;
    margin-left: 0;
  }
  .rtl .ml-3 {
    margin-right: 0.75rem;
    margin-left: 0;
  }
  .rtl .ml-4 {
    margin-right: 1rem;
    margin-left: 0;
  }
  .rtl .ml-6 {
    margin-right: 1.5rem;
    margin-left: 0;
  }
  .rtl .ml-8 {
    margin-right: 2rem;
    margin-left: 0;
  }
  .rtl .ml-auto {
    margin-right: auto;
    margin-left: 0;
  }

  .rtl .mr-1 {
    margin-left: 0.25rem;
    margin-right: 0;
  }
  .rtl .mr-2 {
    margin-left: 0.5rem;
    margin-right: 0;
  }
  .rtl .mr-3 {
    margin-left: 0.75rem;
    margin-right: 0;
  }
  .rtl .mr-4 {
    margin-left: 1rem;
    margin-right: 0;
  }
  .rtl .mr-6 {
    margin-left: 1.5rem;
    margin-right: 0;
  }
  .rtl .mr-8 {
    margin-left: 2rem;
    margin-right: 0;
  }
  .rtl .mr-auto {
    margin-left: auto;
    margin-right: 0;
  }

  /* RTL Padding utilities */
  .rtl .pl-1 {
    padding-right: 0.25rem;
    padding-left: 0;
  }
  .rtl .pl-2 {
    padding-right: 0.5rem;
    padding-left: 0;
  }
  .rtl .pl-3 {
    padding-right: 0.75rem;
    padding-left: 0;
  }
  .rtl .pl-4 {
    padding-right: 1rem;
    padding-left: 0;
  }
  .rtl .pl-6 {
    padding-right: 1.5rem;
    padding-left: 0;
  }
  .rtl .pl-8 {
    padding-right: 2rem;
    padding-left: 0;
  }

  .rtl .pr-1 {
    padding-left: 0.25rem;
    padding-right: 0;
  }
  .rtl .pr-2 {
    padding-left: 0.5rem;
    padding-right: 0;
  }
  .rtl .pr-3 {
    padding-left: 0.75rem;
    padding-right: 0;
  }
  .rtl .pr-4 {
    padding-left: 1rem;
    padding-right: 0;
  }
  .rtl .pr-6 {
    padding-left: 1.5rem;
    padding-right: 0;
  }
  .rtl .pr-8 {
    padding-left: 2rem;
    padding-right: 0;
  }

  /* RTL Position utilities */
  .rtl .left-0 {
    right: 0;
    left: auto;
  }
  .rtl .left-1 {
    right: 0.25rem;
    left: auto;
  }
  .rtl .left-2 {
    right: 0.5rem;
    left: auto;
  }
  .rtl .left-3 {
    right: 0.75rem;
    left: auto;
  }
  .rtl .left-4 {
    right: 1rem;
    left: auto;
  }
  .rtl .left-6 {
    right: 1.5rem;
    left: auto;
  }
  .rtl .left-8 {
    right: 2rem;
    left: auto;
  }

  .rtl .right-0 {
    left: 0;
    right: auto;
  }
  .rtl .right-1 {
    left: 0.25rem;
    right: auto;
  }
  .rtl .right-2 {
    left: 0.5rem;
    right: auto;
  }
  .rtl .right-3 {
    left: 0.75rem;
    right: auto;
  }
  .rtl .right-4 {
    left: 1rem;
    right: auto;
  }
  .rtl .right-6 {
    left: 1.5rem;
    right: auto;
  }
  .rtl .right-8 {
    left: 2rem;
    right: auto;
  }

  /* RTL Text alignment */
  .rtl .text-left {
    text-align: right;
  }
  .rtl .text-right {
    text-align: left;
  }

  /* RTL Border radius */
  .rtl .rounded-l {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .rtl .rounded-r {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .rtl .rounded-tl {
    border-top-right-radius: 0.25rem;
    border-top-left-radius: 0;
  }
  .rtl .rounded-tr {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .rtl .rounded-bl {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
  .rtl .rounded-br {
    border-bottom-left-radius: 0.25rem;
    border-bottom-right-radius: 0;
  }

  /* RTL Transform utilities */
  .rtl .rotate-90 {
    transform: rotate(-90deg);
  }
  .rtl .rotate-180 {
    transform: rotate(180deg);
  }
  .rtl .-rotate-90 {
    transform: rotate(90deg);
  }

  /* Custom animations */
  @keyframes slide-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes bounce-in {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
  }

  /* Scrollbar styling for RTL */
  .rtl ::-webkit-scrollbar {
    width: 8px;
  }

  .rtl ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .rtl ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }

  .rtl ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

@layer components {
  /* Custom component styles */
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-lg font-medium transition-colors;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* RTL-aware grid layouts */
  .rtl .grid-flow-col {
    grid-auto-flow: column reverse;
  }

  /* RTL-aware animations */
  .rtl .animate-slide-in-left {
    animation: slide-in-right 0.5s ease-out;
  }

  .rtl .animate-slide-in-right {
    animation: slide-in-left 0.5s ease-out;
  }

  @keyframes slide-in-left {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

@layer utilities {
  /* Custom utility classes */
  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  /* RTL-specific utilities */
  .rtl-flip {
    transform: scaleX(-1);
  }

  .rtl .rtl-flip {
    transform: scaleX(1);
  }

  /* Responsive RTL utilities */
  @media (min-width: 640px) {
    .rtl .sm\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (min-width: 768px) {
    .rtl .md\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }

  @media (min-width: 1024px) {
    .rtl .lg\:flex-row-reverse {
      flex-direction: row-reverse;
    }
  }
}
