import { type NextRequest, NextResponse } from "next/server"
import { enhancedDb } from "@/lib/database-enhanced"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const productId = Number.parseInt(params.id)

    // Get session ID from cookies or create one
    const sessionId =
      request.cookies.get("session_id")?.value || `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Check if product exists
    const product = await enhancedDb.getProductById(productId)
    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    // Add to wishlist
    await enhancedDb.addToWishlist(sessionId, productId)

    const response = NextResponse.json({
      success: true,
      message: "Product added to wishlist",
    })

    // Set session cookie if it doesn't exist
    if (!request.cookies.get("session_id")) {
      response.cookies.set("session_id", sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        maxAge: 30 * 24 * 60 * 60, // 30 days
      })
    }

    return response
  } catch (error) {
    console.error("Error adding to wishlist:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const productId = Number.parseInt(params.id)
    const sessionId = request.cookies.get("session_id")?.value

    if (!sessionId) {
      return NextResponse.json({ error: "No session found" }, { status: 400 })
    }

    // Remove from wishlist
    await enhancedDb.removeFromWishlist(sessionId, productId)

    return NextResponse.json({
      success: true,
      message: "Product removed from wishlist",
    })
  } catch (error) {
    console.error("Error removing from wishlist:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const productId = Number.parseInt(params.id)
    const sessionId = request.cookies.get("session_id")?.value

    if (!sessionId) {
      return NextResponse.json({
        success: true,
        is_wishlisted: false,
      })
    }

    const isWishlisted = await enhancedDb.isInWishlist(sessionId, productId)

    return NextResponse.json({
      success: true,
      is_wishlisted: isWishlisted,
    })
  } catch (error) {
    console.error("Error checking wishlist status:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
