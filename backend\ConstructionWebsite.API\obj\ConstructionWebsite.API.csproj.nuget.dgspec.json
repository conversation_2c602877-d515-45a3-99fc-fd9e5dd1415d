{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\construction-website\\backend\\ConstructionWebsite.API\\ConstructionWebsite.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\construction-website\\backend\\ConstructionWebsite.API\\ConstructionWebsite.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\construction-website\\backend\\ConstructionWebsite.API\\ConstructionWebsite.API.csproj", "projectName": "ConstructionWebsite.API", "projectPath": "C:\\Users\\<USER>\\Desktop\\construction-website\\backend\\ConstructionWebsite.API\\ConstructionWebsite.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\construction-website\\backend\\ConstructionWebsite.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.7.20, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}