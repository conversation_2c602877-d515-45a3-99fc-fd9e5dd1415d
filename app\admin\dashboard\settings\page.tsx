"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Save, Upload } from "lucide-react"
import { AuthGuard } from "@/components/auth-guard"
import { db } from "@/lib/database"
import type { SiteSettings } from "@/lib/database"

// JSON-LD for Admin Settings Page
const adminSettingsJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Admin Settings - DRYLEX Iraq",
  description: "Admin dashboard for managing website settings on DRYLEX Iraq website. Configure general settings, SEO, and other website parameters.",
  url: "https://drylexiraq.com/admin/dashboard/settings",
  mainContentOfPage: {
    "@type": "WebApplication",
    name: "DRYLEX Iraq Admin Settings",
    description: "Web application for managing website settings and configuration for DRYLEX Iraq website",
    operatingSystem: "Web",
    applicationCategory: "BusinessApplication",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      eligibleRegion: "IQ",
    },
  },
}

export default function SettingsPage() {
  // JSON-LD for WebPage
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Admin Settings - DRYLEX Iraq",
    "description": "Admin dashboard settings management for DRYLEX Iraq construction materials",
    "url": "https://drylexiraq.com/admin/dashboard/settings",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Admin",
          "item": "https://drylexiraq.com/admin"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Settings",
          "item": "https://drylexiraq.com/admin/dashboard/settings"
        }
      ]
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  const [settings, setSettings] = useState<SiteSettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    companyName: "",
    companyNameAr: "",
    logo: "",
    favicon: "",
    whatsappNumber: "",
    email: "",
    phone: "",
    address: "",
    addressAr: "",
    businessHours: "",
  })

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = () => {
    const currentSettings = db.getSettings()
    setSettings(currentSettings)
    setFormData({
      companyName: currentSettings.companyName,
      companyNameAr: currentSettings.companyNameAr,
      logo: currentSettings.logo,
      favicon: currentSettings.favicon,
      whatsappNumber: currentSettings.whatsappNumber,
      email: currentSettings.email,
      phone: currentSettings.phone,
      address: currentSettings.address,
      addressAr: currentSettings.addressAr,
      businessHours: currentSettings.businessHours.join("\n"),
    })
    setIsLoading(false)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      const updatedSettings = {
        ...formData,
        businessHours: formData.businessHours.split("\n").filter((h) => h.trim()),
        heroSlides: settings?.heroSlides || [],
      }

      db.updateSettings(updatedSettings)

      toast({
        title: "Settings Updated",
        description: "Your website settings have been saved successfully.",
      })

      loadSettings()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2 mb-8"></div>
              <div className="space-y-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg p-6">
                    <div className="h-4 bg-gray-300 rounded w-1/4 mb-4"></div>
                    <div className="h-10 bg-gray-300 rounded"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </AuthGuard>
    )
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(adminSettingsJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">Settings</h1>
            <p className="text-gray-600 mb-8">Manage your website settings and configuration.</p>
          </div>
        </div>
      </AuthGuard>
    </>
  )
}
