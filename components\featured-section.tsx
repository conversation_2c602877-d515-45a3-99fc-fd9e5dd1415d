import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowR<PERSON>, Star, Users, Award } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { AnimatedSection } from "./animated-section"
import { StaggerContainer } from "./stagger-container"
import { AnimatedCard } from "./animated-card"
import { CountUpAnimation } from "./count-up-animation"
import type { Product, Project } from "@/lib/database"

interface FeaturedSectionProps {
  products: Product[]
  projects: Project[]
}

export function FeaturedSection({ products, projects }: FeaturedSectionProps) {
  const stats = [
    { icon: Users, label: "Happy Clients", value: 500 },
    { icon: Award, label: "Projects Completed", value: 1000 },
    { icon: Star, label: "Years Experience", value: 25 },
  ]

  return (
    <div className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Stats Section */}
        <AnimatedSection>
          <StaggerContainer className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {stats.map((stat, index) => (
              <AnimatedCard key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 rounded-full mb-4">
                  <stat.icon className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">
                  <CountUpAnimation end={stat.value} suffix="+" />
                </h3>
                <p className="text-gray-600">{stat.label}</p>
              </AnimatedCard>
            ))}
          </StaggerContainer>
        </AnimatedSection>

        {/* Drylex Partnership Banner */}
        <AnimatedSection>
          <Card className="mb-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white overflow-hidden">
            <CardContent className="p-8 md:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <Badge className="mb-4 bg-white/20 text-white hover:bg-white/30">
                    New Partnership
                  </Badge>
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">
                    Now Authorized Drylex Distributor
                  </h2>
                  <p className="text-xl mb-6 opacity-90">
                    Discover premium waterproofing and repair systems from Drylex. 
                    Advanced polymer technology for superior protection.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Link href="/drylex">
                      <Button size="lg" variant="secondary">
                        Explore Drylex Products
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                    <Button size="lg" variant="outline" className="text-white border-white hover:bg-white hover:text-blue-800">
                      Download Catalog
                    </Button>
                  </div>
                </div>
                <div className="relative">
                  <div className="bg-white/10 rounded-lg p-6 backdrop-blur-sm">
                    <h3 className="text-xl font-semibold mb-4">Key Benefits:</h3>
                    <ul className="space-y-2">
                      <li className="flex items-center">
                        <Star className="h-5 w-5 mr-2 text-yellow-300" />
                        Superior water resistance
                      </li>
                      <li className="flex items-center">
                        <Star className="h-5 w-5 mr-2 text-yellow-300" />
                        Easy application methods
                      </li>
                      <li className="flex items-center">
                        <Star className="h-5 w-5 mr-2 text-yellow-300" />
                        Long-lasting protection
                      </li>
                      <li className="flex items-center">
                        <Star className="h-5 w-5 mr-2 text-yellow-300" />
                        International quality standards
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </AnimatedSection>

        {/* Featured Products */}
        <section className="mb-16">
          <AnimatedSection className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              Featured Products
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">DRYLEX IRAQ Materials</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our range of high-quality construction materials trusted by professionals
            </p>
          </AnimatedSection>

          <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {products.map((product, index) => (
              <AnimatedCard key={product.id} delay={index * 0.1}>
                <Card className="group hover:shadow-lg transition-shadow h-full">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={400}
                      height={300}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <Badge className="absolute top-4 left-4 bg-orange-600">{product.category}</Badge>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl">{product.name}</CardTitle>
                    <CardDescription>{product.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {product.features.slice(0, 3).map((feature, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    <Button className="w-full bg-transparent" variant="outline">
                      Request Quote
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggerContainer>

          <AnimatedSection className="text-center">
            <Link href="/products">
              <Button size="lg" className="bg-orange-600 hover:bg-orange-700">
                View All Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </AnimatedSection>
        </section>

        {/* Featured Projects */}
        <section>
          <AnimatedSection className="text-center mb-12">
            <Badge variant="secondary" className="mb-4">
              Featured Projects
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Latest Success Stories</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              See how we've helped transform construction projects across the region
            </p>
          </AnimatedSection>

          <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {projects.map((project, index) => (
              <AnimatedCard key={project.id} delay={index * 0.2}>
                <Card className="group hover:shadow-lg transition-shadow">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <Image
                      src={project.afterImage || "/placeholder.svg"}
                      alt={project.name}
                      width={400}
                      height={300}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-green-600">{project.type}</Badge>
                    </div>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl">{project.name}</CardTitle>
                    <CardDescription>
                      {project.location} • {project.date}
                    </CardDescription>
                  </CardHeader>
                </Card>
              </AnimatedCard>
            ))}
          </StaggerContainer>

          <AnimatedSection className="text-center">
            <Link href="/projects">
              <Button size="lg" variant="outline">
                View All Projects
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </AnimatedSection>
        </section>
      </div>
    </div>
  )
}
