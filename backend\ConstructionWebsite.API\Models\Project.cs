using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ConstructionWebsite.API.Models
{
    public class Project
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Category")]
        public int? CategoryId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? ShortDescription { get; set; }

        [StringLength(500)]
        public string? ShortDescriptionAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Description { get; set; }

        [Column(TypeName = "ntext")]
        public string? DescriptionAr { get; set; }

        [StringLength(200)]
        public string? ClientName { get; set; }

        [StringLength(200)]
        public string? ClientNameAr { get; set; }

        [StringLength(50)]
        public string ClientType { get; set; } = "private";

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(200)]
        public string? LocationAr { get; set; }

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(100)]
        public string Country { get; set; } = "IRAQ";

        [Column(TypeName = "ntext")]
        public string? Coordinates { get; set; }

        [StringLength(50)]
        public string ProjectType { get; set; } = "commercial";

        [StringLength(50)]
        public string? ProjectTypeAr { get; set; }

        [StringLength(100)]
        public string? ProjectSize { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? ProjectValue { get; set; }

        [StringLength(3)]
        public string Currency { get; set; } = "AED";

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime? CompletionDate { get; set; }

        public int? DurationMonths { get; set; }

        [StringLength(50)]
        public string ProjectStatus { get; set; } = "planning";

        public int CompletionPercentage { get; set; } = 0;

        [Column(TypeName = "ntext")]
        public string? ScopeOfWork { get; set; }

        [Column(TypeName = "ntext")]
        public string? ScopeOfWorkAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? KeyFeatures { get; set; }

        [Column(TypeName = "ntext")]
        public string? KeyFeaturesAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? ServicesProvided { get; set; }

        [Column(TypeName = "ntext")]
        public string? ServicesProvidedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? ProductsUsed { get; set; }

        [Column(TypeName = "ntext")]
        public string? ProductsUsedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? ChallengesFaced { get; set; }

        [Column(TypeName = "ntext")]
        public string? ChallengesFacedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? SolutionsImplemented { get; set; }

        [Column(TypeName = "ntext")]
        public string? SolutionsImplementedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? ResultsAchieved { get; set; }

        [Column(TypeName = "ntext")]
        public string? ResultsAchievedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? TechnologiesUsed { get; set; }

        [Column(TypeName = "ntext")]
        public string? TechnologiesUsedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? MaterialsUsed { get; set; }

        [Column(TypeName = "ntext")]
        public string? MaterialsUsedAr { get; set; }

        public int? TeamSize { get; set; }

        [StringLength(100)]
        public string? ProjectManager { get; set; }

        [StringLength(100)]
        public string? Contractor { get; set; }

        [StringLength(100)]
        public string? Architect { get; set; }

        [StringLength(100)]
        public string? Engineer { get; set; }

        [StringLength(100)]
        public string? Consultant { get; set; }

        [Column(TypeName = "ntext")]
        public string? Certifications { get; set; }

        [Column(TypeName = "ntext")]
        public string? AwardsReceived { get; set; }

        [Column(TypeName = "ntext")]
        public string? AwardsReceivedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? MediaCoverage { get; set; }

        [Column(TypeName = "ntext")]
        public string? ClientTestimonial { get; set; }

        [Column(TypeName = "ntext")]
        public string? ClientTestimonialAr { get; set; }

        [Column(TypeName = "decimal(3,2)")]
        public decimal? ClientRating { get; set; }

        [Column(TypeName = "ntext")]
        public string? EnvironmentalImpact { get; set; }

        [Column(TypeName = "ntext")]
        public string? EnvironmentalImpactAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? SustainabilityFeatures { get; set; }

        [Column(TypeName = "ntext")]
        public string? SustainabilityFeaturesAr { get; set; }

        [StringLength(500)]
        public string? VideoUrl { get; set; }

        [StringLength(500)]
        public string? VirtualTourUrl { get; set; }

        [StringLength(500)]
        public string? CaseStudyPdf { get; set; }

        [StringLength(200)]
        public string? SeoTitle { get; set; }

        [StringLength(200)]
        public string? SeoTitleAr { get; set; }

        [StringLength(500)]
        public string? SeoDescription { get; set; }

        [StringLength(500)]
        public string? SeoDescriptionAr { get; set; }

        [StringLength(500)]
        public string? SeoKeywords { get; set; }

        [StringLength(500)]
        public string? SeoKeywordsAr { get; set; }

        public bool IsFeatured { get; set; } = false;

        public bool IsShowcase { get; set; } = false;

        public int ViewCount { get; set; } = 0;

        public int InquiryCount { get; set; } = 0;

        public int SortOrder { get; set; } = 0;

        public bool IsDeleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ProjectCategory? Category { get; set; }
        public virtual ICollection<ProjectImage> Images { get; set; } = new List<ProjectImage>();
        public virtual ICollection<ProjectInquiry> Inquiries { get; set; } = new List<ProjectInquiry>();
    }

    public class ProjectCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<Project> Projects { get; set; } = new List<Project>();
    }

    public class ProjectImage
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Project")]
        public int ProjectId { get; set; }

        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ImageType { get; set; } = string.Empty;

        [StringLength(50)]
        public string? Phase { get; set; }

        [StringLength(200)]
        public string? AltText { get; set; }

        [StringLength(200)]
        public string? AltTextAr { get; set; }

        [StringLength(500)]
        public string? Caption { get; set; }

        [StringLength(500)]
        public string? CaptionAr { get; set; }

        public DateTime? TakenDate { get; set; }

        [StringLength(100)]
        public string? Photographer { get; set; }

        public int? Width { get; set; }

        public int? Height { get; set; }

        public long? FileSize { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsPrimary { get; set; } = false;

        public bool IsBeforeAfter { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Project Project { get; set; } = null!;
    }

    public class ProjectInquiry
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Project")]
        public int ProjectId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Company { get; set; }

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "ntext")]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string InquiryType { get; set; } = "general";

        [StringLength(100)]
        public string? BudgetRange { get; set; }

        [StringLength(100)]
        public string? Timeline { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(50)]
        public string Source { get; set; } = "website";

        [StringLength(45)]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "unread";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Project Project { get; set; } = null!;
    }
}
