using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Services
{
    public interface IServiceService
    {
        Task<IEnumerable<Service>> GetAllServicesAsync();
        Task<Service?> GetServiceByIdAsync(int id);
        Task<Service> CreateServiceAsync(Service service);
        Task<Service?> UpdateServiceAsync(int id, Service service);
        Task<bool> DeleteServiceAsync(int id);
        Task<IEnumerable<Service>> GetServicesByCategoryAsync(string category);
        Task<IEnumerable<Service>> SearchServicesAsync(string searchTerm);
        Task<bool> ServiceExistsAsync(int id);
        
        // Additional methods expected by controller
        Task<PagedResult<ServiceDto>> GetServicesAsync(
            string? search = null,
            int? categoryId = null,
            string? status = null,
            bool? isFeatured = null,
            string? language = "en",
            int page = 1,
            int pageSize = 12,
            string? sortBy = "createdAt",
            string? sortOrder = "desc");
        Task<ServiceDetailDto?> GetServiceBySlugAsync(string slug, string language = "en");
        Task<ServiceDetailDto?> GetServiceByIdAsync(int id, string language = "en");
        Task<List<ServiceDto>> GetRelatedServicesAsync(int id, string language = "en", int limit = 3);
        Task IncrementViewCountAsync(int id);
        Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(int id, CreateServiceInquiryDto inquiryDto, string? clientIp, string? userAgent);
        Task<List<CategoryDto>> GetCategoriesAsync(string language = "en");
        Task<ServiceResult<ServiceDto>> CreateServiceAsync(CreateServiceDto serviceDto);
        Task<ServiceResult<ServiceDto>> UpdateServiceAsync(int id, UpdateServiceDto serviceDto);
    }
}