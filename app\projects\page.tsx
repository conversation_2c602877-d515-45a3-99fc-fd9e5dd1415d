"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Search, MapPin, Calendar, Play, ExternalLink } from "lucide-react"
import Image from "next/image"
import { AnimatedSection } from "@/components/animated-section"
import { StaggerContainer } from "@/components/stagger-container"
import { AnimatedCard } from "@/components/animated-card"
import { db } from "@/lib/database"
import type { Project } from "@/lib/database"
import Head from "next/head"
import { Metadata } from "next"


// Generate enhanced JSON-LD with dynamic project data
const generateProjectsJsonLd = (projects: Project[]) => {
  const itemList = projects.map((project) => ({
    "@type": "CaseStudy" as const,
    identifier: `project-${project.id}`,
    name: project.name,
    alternateName: project.nameAr,
    description: project.description,
    datePublished: project.date,
    category: project.type,
    location: {
      "@type": "Place" as const,
      name: project.location,
      address: {
        "@type": "PostalAddress" as const,
        addressLocality: project.location,
        addressCountry: "Iraq"
      }
    },
    offers: {
      "@type": "Offer" as const,
      itemOffered: {
        "@type": "Service" as const,
        name: project.services.join(", "),
        description: `Services provided: ${project.services.join(", ")}`
      }
    },
    associatedMedia: [
      {
        "@type": "ImageObject" as const,
        url: project.beforeImage || "/placeholder.svg",
        description: `${project.name} before construction`
      },
      {
        "@type": "ImageObject" as const,
        url: project.afterImage || "/placeholder.svg",
        description: `${project.name} after construction`
      },
      ...(project.videoUrl ? [{
        "@type": "VideoObject" as const,
        url: project.videoUrl,
        description: `${project.name} project video`
      }] : [])
    ],
    provider: {
      "@type": "Organization" as const,
      name: "DRYLEX Iraq",
      url: "https://drylexiraq.com"
    },
    author: {
      "@type": "Organization" as const,
      name: "DRYLEX Iraq",
      url: "https://drylexiraq.com"
    }
  }));

  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage" as const,
    name: "Construction Projects - DRYLEX Iraq",
    description:
      "Real-world applications of DRYLEX construction materials and services in Iraqi infrastructure projects. Technical details, challenges, and solutions for construction projects.",
    url: "https://drylexiraq.com/projects",
    mainEntity: {
      "@type": "ItemList" as const,
      name: "Construction Projects",
      numberOfItems: projects.length,
      itemListElement: itemList
    },
    hasPart: {
      "@type": "ItemList" as const,
      name: "Project Categories",
      itemListElement: ["Commercial", "Residential", "Industrial", "Infrastructure"].map((category) => ({
        "@type": "Thing" as const,
        name: category,
        description: `Construction projects in ${category} category`
      }))
    },
    breadcrumb: {
      "@type": "BreadcrumbList" as const,
      itemListElement: [
        {
          "@type": "ListItem" as const,
          position: 1,
          name: "Home",
          item: "https://drylexiraq.com"
        },
        {
          "@type": "ListItem" as const,
          position: 2,
          name: "Projects",
          item: "https://drylexiraq.com/projects"
        }
      ]
    }
  };
}

export default function ProjectsPage() {
  const settings = db.getSettings() || { address: '', phone: '', email: '' }
  const projects = db.getProjects()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('All')
  
  const projectTypes = ['All', 'Commercial', 'Residential', 'Industrial', 'Infrastructure']

  // Filter projects based on search term and selected type
  const filteredProjects = projects.filter((project) => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'All' || project.type === selectedType
    return matchesSearch && matchesType
  })

  // JSON-LD for CaseStudy
  const caseStudyJsonLd = {
    "@context": "https://schema.org",
    "@type": "CaseStudy",
    name: "DRYLEX Construction Projects",
    description: "Case studies of construction projects completed using DRYLEX materials and solutions",
    provider: {
      "@type": "Organization",
      name: "DRYLEX IRAQ",
      url: "https://drylexiraq.com",
      logo: "https://drylexiraq.com/logo.png",
    },
    category: "Construction Projects > Iraqi Construction",
    image: "https://drylexiraq.com/images/projects/construction-project.jpg",
    offers: {
      "@type": "OfferCatalog",
      name: "Construction Projects Catalog",
      url: "https://drylexiraq.com/projects",
      numberOfItems: "20",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      reviewCount: "45",
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": settings.address,
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": settings.phone,
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://facebook.com/drylexiraq",
      "https://instagram.com/drylexiraq",
      "https://linkedin.com/company/drylexiraq"
    ]
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(caseStudyJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <AnimatedSection className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Construction Projects</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Showcasing our successful construction projects across IRAQ. Over 1000 completed projects since 1995.
            </p>
          </AnimatedSection>

          {/* Search and Filter */}
          <AnimatedSection className="mb-8 flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search construction projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                aria-label="Search projects"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {projectTypes.map((type) => (
                <Button
                  key={type}
                  variant={selectedType === type ? "default" : "outline"}
                  onClick={() => setSelectedType(type)}
                  className="text-sm"
                  aria-label={`Filter by ${type} projects`}
                >
                  {type}
                </Button>
              ))}
            </div>
          </AnimatedSection>

          {/* Projects Grid */}
          <StaggerContainer className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {filteredProjects.map((project, index) => (
              <AnimatedCard key={project.id} delay={index * 0.1}>
                <Card className="group hover:shadow-lg transition-shadow">
                  <div className="grid grid-cols-2 gap-4 p-4">
                    <div className="space-y-2">
                      <p className="text-sm text-gray-500">Before</p>
                      <div className="relative overflow-hidden rounded-lg">
                        <Image
                          src={project.beforeImage || "/placeholder.svg"}
                          alt={`${project.name} before construction - DRYLEX  IRAQ`}
                          width={200}
                          height={150}
                          className="w-full h-32 object-cover"
                          loading={index < 4 ? "eager" : "lazy"}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm text-gray-500">After</p>
                      <div className="relative overflow-hidden rounded-lg">
                        <Image
                          src={project.afterImage || "/placeholder.svg"}
                          alt={`${project.name} after construction - DRYLEX  IRAQ`}
                          width={200}
                          height={150}
                          className="w-full h-32 object-cover"
                          loading={index < 4 ? "eager" : "lazy"}
                        />
                        {project.videoUrl && (
                          <div className="absolute inset-0 flex items-center justify-center bg-black/20">
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => window.open(project.videoUrl!, "_blank")}
                              aria-label={`Watch ${project.name} project video`}
                            >
                              <Play className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-xl">
                          {project.name}
                          <span className="block text-sm text-gray-500 font-normal mt-1">{project.nameAr}</span>
                        </CardTitle>
                        <CardDescription className="mt-2">{project.description}</CardDescription>
                      </div>
                      <Badge className="bg-blue-600">{project.type}</Badge>
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {project.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {project.date}
                        </div>
                      </div>

                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">Services Provided:</p>
                        <div className="flex flex-wrap gap-2">
                          {project.services.map((service, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {service}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="pt-2 border-t">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">Client:</span> {project.client}
                        </p>
                      </div>

                      {project.videoUrl && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full bg-transparent"
                          onClick={() => window.open(project.videoUrl!, "_blank")}
                          aria-label={`View ${project.name} project video`}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          View Project Video
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggerContainer>

          {filteredProjects.length === 0 && (
            <AnimatedSection className="text-center py-12">
              <p className="text-gray-500 text-lg">No construction projects found matching your criteria.</p>
            </AnimatedSection>
          )}
        </div>
      </div>
    </>
  )
}
