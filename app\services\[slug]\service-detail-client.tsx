"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Share2,
  MessageCircle,
  Phone,
  Star,
  Eye,
  Clock,
  Award,
  Shield,
  MapPin,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Users,
  CheckCircle,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import type { Service } from "@/lib/database-enhanced"

interface ServiceDetailClientProps {
  service: Service
}

export function ServiceDetailClient({ service }: ServiceDetailClientProps) {
  const { t, language } = useTranslation()
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const [activeTab, setActiveTab] = useState("overview")

  // Language-aware content
  const displayName = language === "ar" ? service.name_ar || service.name : service.name
  const displayDescription = language === "ar" ? service.description_ar || service.description : service.description
  const displayShortDescription =
    language === "ar" ? service.short_description_ar || service.short_description : service.short_description
  const displayFeatures = language === "ar" ? service.features_ar || service.features : service.features
  const displayDurationEstimate =
    language === "ar" ? service.duration_estimate_ar || service.duration_estimate : service.duration_estimate

  const handleInquiry = () => {
    const message = t(
      "whatsapp.service_inquiry",
      "Hello! I'm interested in your {service} service. Please provide more details including pricing and timeline.",
    ).replace("{service}", displayName)

    const whatsappUrl = `https://wa.me/+971501234567?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: displayName,
          text: displayShortDescription,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
    }
  }

  const nextImage = () => {
    if (service.images && service.images.length > 0) {
      setSelectedImageIndex((prev) => (prev + 1) % service.images.length)
    }
  }

  const prevImage = () => {
    if (service.images && service.images.length > 0) {
      setSelectedImageIndex((prev) => (prev - 1 + service.images.length) % service.images.length)
    }
  }

  const selectedImage = service.images?.[selectedImageIndex]

  return (
    <div className="max-w-7xl mx-auto">
      {/* Back Navigation */}
      <div className="mb-6">
        <Link
          href="/services"
          className="inline-flex items-center text-sm text-gray-600 hover:text-orange-600 transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          {t("nav.back_to_services", "Back to Services")}
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* Service Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden group">
            {selectedImage ? (
              <Image
                src={selectedImage.image_url || "/placeholder.svg"}
                alt={
                  language === "ar"
                    ? selectedImage.alt_text_ar || selectedImage.alt_text || displayName
                    : selectedImage.alt_text || displayName
                }
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <span className="text-gray-400">{t("no_image", "No Image Available")}</span>
              </div>
            )}

            {/* Image Navigation */}
            {service.images && service.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </>
            )}
          </div>

          {/* Thumbnail Images */}
          {service.images && service.images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto">
              {service.images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-colors ${
                    index === selectedImageIndex ? "border-orange-600" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image.image_url || "/placeholder.svg"}
                    alt={`${displayName} ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Service Information */}
        <div className="space-y-6">
          {/* Header */}
          <div>
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{displayName}</h1>
                {service.category && <p className="text-sm text-gray-500">{service.category.name}</p>}
              </div>
              <button
                onClick={handleShare}
                className="p-2 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors ml-4"
              >
                <Share2 className="h-5 w-5" />
              </button>
            </div>

            {/* Badges */}
            <div className="flex flex-wrap gap-2 mb-4">
              {service.is_featured && <Badge className="bg-blue-600">{t("badge.featured", "Featured")}</Badge>}
              {service.is_emergency && (
                <Badge className="bg-red-600">{t("badge.emergency", "Emergency Service")}</Badge>
              )}
              {service.certifications && service.certifications.length > 0 && (
                <Badge variant="outline" className="border-green-600 text-green-600">
                  {t("certified", "Certified")}
                </Badge>
              )}
            </div>

            {/* Rating and Stats */}
            <div className="flex items-center gap-4 text-sm text-gray-600 mb-4">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{service.rating || 4.8}</span>
                <span>(89 {t("reviews", "reviews")})</span>
              </div>
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>
                  {service.view_count || 0} {t("views", "views")}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>
                  {service.completion_count || 0} {t("completed", "completed")}
                </span>
              </div>
            </div>

            {/* Price */}
            {service.base_price && (
              <div className="mb-6">
                <div className="flex items-baseline gap-2">
                  <span className="text-sm text-gray-600">{t("starting_from", "Starting from")}</span>
                  <span className="text-3xl font-bold text-orange-600">
                    {service.currency} {service.base_price}
                  </span>
                  {service.unit && <span className="text-gray-500">/{service.unit}</span>}
                </div>
              </div>
            )}
          </div>

          {/* Short Description */}
          {displayShortDescription && (
            <div>
              <p className="text-gray-700 leading-relaxed">{displayShortDescription}</p>
            </div>
          )}

          {/* Service Details */}
          <div className="grid grid-cols-2 gap-4">
            {displayDurationEstimate && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-orange-600" />
                <div>
                  <div className="font-medium">{t("duration", "Duration")}</div>
                  <div className="text-gray-600">{displayDurationEstimate}</div>
                </div>
              </div>
            )}
            {service.service_area && (
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-blue-600" />
                <div>
                  <div className="font-medium">{t("service_area", "Service Area")}</div>
                  <div className="text-gray-600">{service.service_area}</div>
                </div>
              </div>
            )}
          </div>

          {/* Key Features */}
          {displayFeatures && displayFeatures.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">{t("service_features", "Service Features")}</h3>
              <ul className="space-y-2">
                {displayFeatures.slice(0, 5).map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button onClick={handleInquiry} className="flex-1 bg-orange-600 hover:bg-orange-700 text-white" size="lg">
                <MessageCircle className="h-5 w-5 mr-2" />
                {t("btn.request_quote", "Request Quote")}
              </Button>
              <Button
                variant="outline"
                className="flex-1 border-orange-600 text-orange-600 hover:bg-orange-50 bg-transparent"
                size="lg"
              >
                <Phone className="h-5 w-5 mr-2" />
                {t("btn.call_now", "Call Now")}
              </Button>
            </div>

            <Button variant="outline" size="sm" className="w-full bg-transparent">
              <Calendar className="h-4 w-4 mr-2" />
              {t("btn.schedule_consultation", "Schedule Consultation")}
            </Button>
          </div>

          {/* Service Guarantees */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-3 text-sm">
              <Shield className="h-4 w-4 text-green-600" />
              <span>{t("service.guarantee", "100% satisfaction guarantee")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Award className="h-4 w-4 text-blue-600" />
              <span>{t("service.licensed", "Licensed & insured professionals")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Users className="h-4 w-4 text-purple-600" />
              <span>{t("service.experienced", "10+ years of experience")}</span>
            </div>
            <div className="flex items-center gap-3 text-sm">
              <Clock className="h-4 w-4 text-orange-600" />
              <span>{t("service.support", "24/7 emergency support")}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Tabs */}
      <Card>
        <CardContent className="p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">{t("tab.overview", "Overview")}</TabsTrigger>
              <TabsTrigger value="process">{t("tab.process", "Process")}</TabsTrigger>
              <TabsTrigger value="portfolio">{t("tab.portfolio", "Portfolio")}</TabsTrigger>
              <TabsTrigger value="reviews">{t("tab.reviews", "Reviews")}</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-6">
              <div className="prose max-w-none">
                <h3 className="text-xl font-semibold mb-4">{t("service_overview", "Service Overview")}</h3>
                {displayDescription ? (
                  <div className="text-gray-700 leading-relaxed whitespace-pre-line">{displayDescription}</div>
                ) : (
                  <p className="text-gray-500">{t("no_description", "No detailed description available.")}</p>
                )}

                {/* Certifications */}
                {service.certifications && service.certifications.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold mb-3">{t("certifications", "Certifications")}</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.certifications.map((cert, index) => (
                        <Badge key={index} variant="outline" className="border-green-600 text-green-600">
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="process" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("service_process", "Service Process")}</h3>
              <div className="space-y-6">
                {[
                  {
                    step: 1,
                    title: t("process.consultation", "Initial Consultation"),
                    description: t(
                      "process.consultation_desc",
                      "We discuss your requirements and assess the project scope.",
                    ),
                  },
                  {
                    step: 2,
                    title: t("process.planning", "Planning & Design"),
                    description: t(
                      "process.planning_desc",
                      "Our team creates detailed plans and designs for your project.",
                    ),
                  },
                  {
                    step: 3,
                    title: t("process.execution", "Execution"),
                    description: t("process.execution_desc", "Professional execution with regular progress updates."),
                  },
                  {
                    step: 4,
                    title: t("process.completion", "Completion & Handover"),
                    description: t(
                      "process.completion_desc",
                      "Final inspection and project handover with documentation.",
                    ),
                  },
                ].map((item) => (
                  <div key={item.step} className="flex gap-4">
                    <div className="flex-shrink-0 w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center font-semibold">
                      {item.step}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-gray-600">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="portfolio" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("recent_projects", "Recent Projects")}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Sample portfolio items */}
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <div
                    key={item}
                    className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden group cursor-pointer"
                  >
                    <Image
                      src={`/placeholder.svg?height=200&width=300&text=Project ${item}`}
                      alt={`Portfolio Project ${item}`}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <span className="text-white font-medium">{t("view_project", "View Project")}</span>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <h3 className="text-xl font-semibold mb-4">{t("customer_reviews", "Customer Reviews")}</h3>
              <div className="space-y-4">
                {/* Review Summary */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">{service.rating || 4.8}</div>
                      <div className="flex items-center justify-center gap-1 mb-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <div className="text-sm text-gray-600">89 {t("reviews", "reviews")}</div>
                    </div>
                  </div>
                </div>

                {/* Sample Reviews */}
                <div className="space-y-4">
                  {[
                    {
                      name: "Mohammed Al-Zaabi",
                      rating: 5,
                      date: "2024-01-20",
                      comment:
                        "Excellent service! The team was professional, punctual, and delivered exactly what was promised. Highly recommend for any construction needs.",
                    },
                    {
                      name: "Lisa Chen",
                      rating: 5,
                      date: "2024-01-18",
                      comment:
                        "Outstanding work quality and attention to detail. The project was completed on time and within budget. Very satisfied with the results.",
                    },
                  ].map((review, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <div className="font-medium text-gray-900">{review.name}</div>
                          <div className="flex items-center gap-1 mt-1">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-4 w-4 ${star <= review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                      <p className="text-gray-700">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
