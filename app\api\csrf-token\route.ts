import { NextRequest, NextResponse } from "next/server"
import { generateCSRFToken } from "@/utils/security"

export async function GET(request: NextRequest) {
  try {
    const token = generateCSRFToken()
    
    // Set CSRF token in httpOnly cookie
    const response = NextResponse.json({ token })
    
    response.cookies.set('csrf-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 3600 // 1 hour
    })
    
    return response
  } catch (error) {
    console.error('CSRF token generation error:', error)
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    )
  }
}
