"use client"

import type React from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Star, MessageCircle, Clock, Award } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import { AnimatedCard } from "@/components/animated-card"
import type { Service } from "@/lib/database-enhanced"

interface ServiceCardProps {
  service: Service
  index?: number
}

export function ServiceCard({ service, index = 0 }: ServiceCardProps) {
  const { t, language } = useTranslation()

  const displayName = language === "ar" ? service.name_ar || service.name : service.name
  const displayDescription =
    language === "ar" ? service.short_description_ar || service.short_description : service.short_description

  const handleQuickInquiry = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const message = t(
      "whatsapp.service_inquiry",
      "Hello! I'm interested in your {service} service. Please provide more details.",
    ).replace("{service}", displayName)

    const whatsappUrl = `https://wa.me/+9647867100886?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  return (
    <AnimatedCard delay={index * 0.1} className="h-full">
      <Link href={`/services/${service.slug}`} className="block h-full" prefetch={true} scroll={true}>
        <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer h-full flex flex-col">
          {/* Service Image */}
          <div className="relative aspect-video overflow-hidden rounded-t-lg">
            <Image
              src={service.images?.[0]?.image_url || "/placeholder.svg?height=200&width=300"}
              alt={
                language === "ar"
                  ? service.images?.[0]?.alt_text_ar || service.images?.[0]?.alt_text || displayName
                  : service.images?.[0]?.alt_text || displayName
              }
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="lazy"
              priority={index < 4}
            />

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-1">
              {service.is_featured && (
                <Badge className="bg-blue-600 text-white text-xs">{t("badge.featured", "Featured")}</Badge>
              )}
              {service.is_emergency && (
                <Badge className="bg-red-600 text-white text-xs">{t("badge.emergency", "Emergency")}</Badge>
              )}
            </div>

            {/* Quick Actions */}
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="bg-white/90 hover:bg-white text-gray-800"
                onClick={handleQuickInquiry}
                aria-label={t("btn.quick_inquiry", "Quick Inquiry")}
              >
                <MessageCircle className="h-4 w-4" />
              </Button>
            </div>

            {/* Price Badge */}
            {service.base_price && (
              <div className="absolute bottom-3 left-3">
                <Badge variant="secondary" className="bg-white/90 text-gray-800 font-semibold">
                  {t("from", "From")} {service.currency} {service.base_price}
                  {service.unit && <span className="text-xs">/{service.unit}</span>}
                </Badge>
              </div>
            )}
          </div>

          {/* Service Info */}
          <CardContent className="p-4 flex-1 flex flex-col">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors">
                {displayName}
              </h3>

              <p className="text-sm text-gray-600 mb-3 line-clamp-3">{displayDescription}</p>

              {/* Key Features */}
              {service.features && service.features.length > 0 && (
                <div className="mb-3">
                  <ul className="text-xs text-gray-600 space-y-1">
                    {(language === "ar" ? service.features_ar || service.features : service.features)
                      .slice(0, 3)
                      .map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-1">
                          <span className="w-1 h-1 bg-orange-600 rounded-full flex-shrink-0"></span>
                          <span className="line-clamp-1">{feature}</span>
                        </li>
                      ))}
                  </ul>
                </div>
              )}

              {/* Service Details */}
              <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                {service.duration_estimate && (
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      {language === "ar"
                        ? service.duration_estimate_ar || service.duration_estimate
                        : service.duration_estimate}
                    </span>
                  </div>
                )}
                {service.certifications && service.certifications.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Award className="h-3 w-3" />
                    <span>{t("certified", "Certified")}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Footer */}
            <div className="pt-3 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{service.view_count || 0}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>{service.rating || 4.8}</span>
                  </div>
                </div>
                <span className="text-green-600 font-medium">
                  {service.completion_count || 0} {t("completed", "Completed")}
                </span>
              </div>

              <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white" size="sm">
                {t("btn.view_details", "View Details")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </Link>
    </AnimatedCard>
  )
}
