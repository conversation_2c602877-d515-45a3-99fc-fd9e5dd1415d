import { NextRequest, NextResponse } from "next/server"

// Simple test login endpoint without complex validation
export async function POST(request: NextRequest) {
  console.log('Simple login API called');
  
  try {
    const body = await request.json();
    console.log('Request body:', body);
    
    const { username, password } = body;
    
    // Simple validation - accept any non-empty credentials
    if (!username || !password) {
      console.log('Missing credentials');
      return NextResponse.json(
        { success: false, message: "Username and password required" },
        { status: 400 }
      )
    }
    
    // For testing, accept any credentials
    console.log('Login successful for:', username);
    
    return NextResponse.json({
      success: true,
      message: "Login successful",
      token: "test-jwt-token",
      refreshToken: "test-refresh-token",
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString() // 15 minutes
    })
    
  } catch (error) {
    console.error("Simple login error:", error)
    return NextResponse.json(
      { success: false, message: "Lo<PERSON> failed" },
      { status: 500 }
    )
  }
}
