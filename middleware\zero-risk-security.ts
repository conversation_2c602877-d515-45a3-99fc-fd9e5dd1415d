import { NextRequest, NextResponse } from 'next/server'
import { zeroRiskValidator } from '@/utils/zero-risk-validation'
import { zeroRiskAuth } from '@/utils/advanced-auth'

// Zero-risk security middleware
export function zeroRiskSecurityMiddleware(request: NextRequest) {
  const response = NextResponse.next()
  
  // Add maximum security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  response.headers.set('Permissions-Policy', 'geolocation=(), microphone=(), camera=(), interest-cohort=()')
  
  // Enhanced CSP
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'nonce-{NONCE}' https://*.googletagmanager.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "img-src 'self' data: https: blob:",
    "font-src 'self' https://fonts.gstatic.com data:",
    "connect-src 'self' https://api.drylexiraq.com",
    "frame-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "upgrade-insecure-requests",
    "block-all-mixed-content"
  ].join('; '))

  // Rate limiting headers
  response.headers.set('X-RateLimit-Limit', '100')
  response.headers.set('X-RateLimit-Remaining', '99')
  response.headers.set('X-RateLimit-Reset', String(Date.now() + 3600000))

  return response
}

// Zero-risk API validation
export function validateAPIRequest(request: NextRequest): boolean {
  const ipAddress = request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown'
  
  const userAgent = request.headers.get('user-agent') || 'unknown'
  
  // Check for suspicious activity
  if (zeroRiskAuth.checkSuspiciousActivity(ipAddress)) {
    return false
  }

  // Validate request headers
  const contentType = request.headers.get('content-type')
  if (contentType && !contentType.includes('application/json') && 
      !contentType.includes('multipart/form-data') &&
      !contentType.includes('application/x-www-form-urlencoded')) {
    return false
  }

  return true
}

// Zero-risk input sanitization
export async function sanitizeRequestBody(request: NextRequest): Promise<any> {
  try {
    const body = await request.json()
    const sanitizedBody: any = {}

    for (const [key, value] of Object.entries(body)) {
      const validation = zeroRiskValidator.validateInput(value, 'text', 1000)
      
      if (!validation.isValid) {
        throw new Error(`Invalid input for field ${key}: ${validation.errors.join(', ')}`)
      }
      
      sanitizedBody[key] = validation.sanitizedValue
    }

    return sanitizedBody
  } catch (error) {
    throw new Error('Invalid request body')
  }
}
