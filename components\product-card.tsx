"use client"

import type React from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Star, MessageCircle } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import { AnimatedCard } from "@/components/animated-card"
import type { Product } from "@/lib/database-enhanced"

interface ProductCardProps {
  product: Product
  index?: number
}

export function ProductCard({ product, index = 0 }: ProductCardProps) {
  const { t, language } = useTranslation()

  const displayName = language === "ar" ? product.name_ar || product.name : product.name
  const displayDescription =
    language === "ar" ? product.short_description_ar || product.short_description : product.short_description

  const handleQuickInquiry = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    const message = t(
      "whatsapp.quick_inquiry",
      "Hello! I'm interested in {product}. Please provide more information.",
    ).replace("{product}", displayName)

    const whatsappUrl = `https://wa.me/+9647867100886?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, "_blank")
  }

  return (
    <AnimatedCard delay={index * 0.1} className="h-full">
      <Link href={`/products/${product.slug}`} className="block h-full" prefetch={true} scroll={true}>
        <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer h-full flex flex-col">
          {/* Product Image */}
          <div className="relative aspect-square overflow-hidden rounded-t-lg">
            <Image
              src={product.images?.[0]?.image_url || "/placeholder.svg?height=300&width=300"}
              alt={
                language === "ar"
                  ? product.images?.[0]?.alt_text_ar || product.images?.[0]?.alt_text || displayName
                  : product.images?.[0]?.alt_text || displayName
              }
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="lazy"
              priority={index < 4}
            />

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-1">
              {product.is_new && <Badge className="bg-green-600 text-white text-xs">{t("badge.new", "New")}</Badge>}
              {product.is_bestseller && (
                <Badge className="bg-orange-600 text-white text-xs">{t("badge.bestseller", "Bestseller")}</Badge>
              )}
              {product.is_featured && (
                <Badge className="bg-blue-600 text-white text-xs">{t("badge.featured", "Featured")}</Badge>
              )}
            </div>

            {/* Quick Actions */}
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <Button
                size="sm"
                variant="secondary"
                className="bg-white/90 hover:bg-white text-gray-800"
                onClick={handleQuickInquiry}
                aria-label={t("btn.quick_inquiry", "Quick Inquiry")}
              >
                <MessageCircle className="h-4 w-4" />
              </Button>
            </div>

            {/* Price Badge */}
            {product.price && (
              <div className="absolute bottom-3 left-3">
                <Badge variant="secondary" className="bg-white/90 text-gray-800 font-semibold">
                  {product.currency} {product.price}
                  {product.unit && <span className="text-xs">/{product.unit}</span>}
                </Badge>
              </div>
            )}
          </div>

          {/* Product Info */}
          <CardContent className="p-4 flex-1 flex flex-col">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors">
                {displayName}
              </h3>

              {product.sku && <p className="text-xs text-gray-500 mb-2 font-mono">SKU: {product.sku}</p>}

              <p className="text-sm text-gray-600 mb-3 line-clamp-3">{displayDescription}</p>

              {/* Key Features */}
              {product.features && product.features.length > 0 && (
                <div className="mb-3">
                  <ul className="text-xs text-gray-600 space-y-1">
                    {(language === "ar" ? product.features_ar || product.features : product.features)
                      .slice(0, 2)
                      .map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-1">
                          <span className="w-1 h-1 bg-orange-600 rounded-full flex-shrink-0"></span>
                          <span className="line-clamp-1">{feature}</span>
                        </li>
                      ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="pt-3 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Eye className="h-3 w-3" />
                    <span>{product.view_count || 0}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>4.8</span>
                  </div>
                </div>
                {product.brand && <span className="text-orange-600 font-medium">{product.brand}</span>}
              </div>

              <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white" size="sm">
                {t("btn.view_details", "View Details")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </Link>
    </AnimatedCard>
  )
}
