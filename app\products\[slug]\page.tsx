import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getProductBySlug, getAllProductSlugs, incrementViewCount, Product } from "@/lib/database-enhanced"
import { ProductDetailClient } from "./product-detail-client"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import { SecureJSONLD } from "@/components/secure-json-ld"

interface ProductPageProps {
  params: {
    slug: string
  }
  searchParams: { [key: string]: string | string[] | undefined }
}

// Static metadata removed - using generateMetadata instead

export async function generateStaticParams() {
  try {
    const slugs = await getAllProductSlugs()
    return slugs.map((item) => ({
      slug: item.slug,
    }))
  } catch (error) {
    console.error("Error generating static params:", error)
    return []
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const product = await getProductBySlug(params.slug)

    if (!product) {
      return {
        title: "Product Not Found",
        description: "The requested product could not be found.",
      }
    }

    const title = product.seo_title || `${product.name} | DRYLEX IRAQ`
    const description = product.seo_description || product.short_description || `Learn more about ${product.name}`
    const keywords = product.seo_keywords || `${product.name}, construction materials, IRAQ`

    return {
      title,
      description,
      keywords: keywords.split(",").map(k => k.trim()),
      openGraph: {
        title,
        description,
        images: product.images && product.images.length > 0 ? [product.images[0].image_url] : ["/og-product.jpg"],
        type: "website",
        url: `https://drylexiraq.com/products/${product.slug}`,
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: product.images && product.images.length > 0 ? [product.images[0].image_url] : ["/og-product.jpg"],
      },
      alternates: {
        canonical: `https://drylexiraq.com/products/${product.slug}`,
      },
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "Product | DRYLEX IRAQ",
      description: "Premium construction materials and solutions",
    }
  }
}

export default async function ProductDetailPage({ params }: { params: { slug: string } }) {
  try {
    const product = await getProductBySlug(params.slug)

    if (!product) {
      notFound()
    }

    // Add defensive programming for product data
    const safeProduct = {
      ...product,
      images: product.images || [],
      features: product.features || [],
      applications: product.applications || [],
      technical_specs: product.technical_specs || {},
      category: product.category || { id: '', name: '', slug: '' }
    }

    // View count incremented on client side to avoid SSG build issues

    const productJsonLd = {
      "@context": "https://schema.org",
      "@type": "Product",
      name: product.name,
      description: product.description || product.short_description,
      brand: {
        "@type": "Brand",
        name: product.brand || "DRYLEX",
      },
      manufacturer: {
        "@type": "Organization",
        name: "DRYLEX IRAQ",
        url: "https://drylexiraq.com",
      },
      offers: {
        "@type": "Offer",
        price: product.price || 0,
        priceCurrency: product.currency,
        availability: "https://schema.org/InStock",
        seller: {
          "@type": "Organization",
          name: "DRYLEX IRAQ",
        },
      },
      image: product.images && product.images.length > 0 ? product.images[0].image_url : "/placeholder.svg",
      sku: product.sku,
      category: product.category?.name,
    }

    const breadcrumbItems = [
      { label: "Home", href: "/" },
      { label: "Products", href: "/products" },
      { label: product.name, href: `/products/${product.slug}` },
    ]

    return (
      <>
        <SecureJSONLD data={productJsonLd} />
        <SEOBreadcrumbs items={breadcrumbItems} />
        <ProductDetailClient product={safeProduct} />
      </>
    )
  } catch (error) {
    console.error("Error loading product:", error)
    notFound()
  }
}