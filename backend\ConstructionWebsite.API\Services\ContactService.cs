using ConstructionWebsite.API.Data;

using ConstructionWebsite.API.DTOs;
using ConstructionWebsite.API.Models;
using Microsoft.EntityFrameworkCore;

namespace ConstructionWebsite.API.Services
{
    public class ContactService : IContactService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ContactService> _logger;
        private readonly IEmailService _emailService;

        public ContactService(
            ApplicationDbContext context, 
            ILogger<ContactService> logger,
            IEmailService emailService)
        {
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        public async Task<IEnumerable<ContactMessage>> GetAllContactMessagesAsync()
        {
            try
            {
                return await _context.ContactMessages
                    .Where(c => !c.Is<PERSON>eleted)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all contact messages");
                throw;
            }
        }

        public async Task<ContactMessage?> GetContactMessageByIdAsync(int id)
        {
            try
            {
                return await _context.ContactMessages
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contact message with ID {ContactMessageId}", id);
                throw;
            }
        }

        public async Task<ContactMessage> CreateContactMessageAsync(ContactMessage contactMessage)
        {
            try
            {
                contactMessage.CreatedAt = DateTime.UtcNow;
                contactMessage.UpdatedAt = DateTime.UtcNow;
                contactMessage.IsRead = false;
                
                _context.ContactMessages.Add(contactMessage);
                await _context.SaveChangesAsync();
                
                // Send notification email to admin
                try
                {
                    await _emailService.SendContactNotificationAsync(contactMessage);
                }
                catch (Exception emailEx)
                {
                    _logger.LogWarning(emailEx, "Failed to send contact notification email for message ID {ContactMessageId}", contactMessage.Id);
                }
                
                _logger.LogInformation("Contact message created with ID {ContactMessageId}", contactMessage.Id);
                return contactMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contact message");
                throw;
            }
        }

        public async Task<ContactMessage?> UpdateContactMessageAsync(int id, ContactMessage contactMessage)
        {
            try
            {
                var existingMessage = await _context.ContactMessages
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
                
                if (existingMessage == null)
                    return null;

                existingMessage.Name = contactMessage.Name;
                existingMessage.Email = contactMessage.Email;
                existingMessage.Phone = contactMessage.Phone;
                existingMessage.Subject = contactMessage.Subject;
                existingMessage.Message = contactMessage.Message;
                existingMessage.IsRead = contactMessage.IsRead;
                existingMessage.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Contact message updated with ID {ContactMessageId}", id);
                return existingMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating contact message with ID {ContactMessageId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteContactMessageAsync(int id)
        {
            try
            {
                var contactMessage = await _context.ContactMessages
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
                
                if (contactMessage == null)
                    return false;

                contactMessage.IsDeleted = true;
                contactMessage.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Contact message deleted with ID {ContactMessageId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting contact message with ID {ContactMessageId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<ContactMessage>> GetUnreadContactMessagesAsync()
        {
            try
            {
                return await _context.ContactMessages
                    .Where(c => !c.IsDeleted && !c.IsRead)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving unread contact messages");
                throw;
            }
        }

        public async Task<bool> MarkAsReadAsync(int id)
        {
            try
            {
                var contactMessage = await _context.ContactMessages
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
                
                if (contactMessage == null)
                    return false;

                contactMessage.IsRead = true;
                contactMessage.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Contact message marked as read with ID {ContactMessageId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking contact message as read with ID {ContactMessageId}", id);
                throw;
            }
        }

        public async Task<bool> MarkAsUnreadAsync(int id)
        {
            try
            {
                var contactMessage = await _context.ContactMessages
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);
                
                if (contactMessage == null)
                    return false;

                contactMessage.IsRead = false;
                contactMessage.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Contact message marked as unread with ID {ContactMessageId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking contact message as unread with ID {ContactMessageId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<ContactMessage>> SearchContactMessagesAsync(string searchTerm)
        {
            try
            {
                return await _context.ContactMessages
                    .Where(c => !c.IsDeleted && 
                        (c.Name.Contains(searchTerm) || 
                         c.Email.Contains(searchTerm) ||
                         c.Subject.Contains(searchTerm) ||
                         c.Message.Contains(searchTerm)))
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching contact messages with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<bool> ContactMessageExistsAsync(int id)
        {
            try
            {
                return await _context.ContactMessages
                    .AnyAsync(c => c.Id == id && !c.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if contact message exists with ID {ContactMessageId}", id);
                throw;
            }
        }
    }
}