import { <PERSON> } from "@/components/hero"
import { FeaturedSection } from "@/components/featured-section"
import { WhatsAppFloat } from "@/components/whatsapp-float"
import { ContactSticky } from "@/components/contact-sticky"
import { AnimatedSection } from "@/components/animated-section"
import { db } from "@/lib/database"
import type { Metadata } from "next"

export const metadata: Metadata = {
  title: "DRYLEX Iraq | Premium Construction Materials & Services | Building Excellence in Iraq",
  description: "DRYLEX Iraq - Your trusted authorized distributor for high-performance construction materials in Iraq. Discover our premium Drylex waterproofing systems, concrete admixtures, repair materials, and construction chemicals for superior building solutions.",
  keywords: [
    "construction materials Iraq",
    "Drylex waterproofing Iraq",
    "concrete admixtures",
    "repair materials",
    "construction chemicals",
    "building materials supplier Iraq",
    "waterproofing solutions",
    "construction services Iraq",
    "Drylex distributor Iraq",
    "construction products Iraq",
    "building solutions Iraq",
    "industrial coatings Iraq",
    "construction waterproofing Iraq",
    "cement additives Iraq",
    "concrete repair Iraq",
    "construction chemicals supplier Iraq",
  ],
  openGraph: {
    title: "DRYLEX Iraq | Premium Construction Materials & Services",
    description: "DRYLEX Iraq - Your trusted authorized distributor for high-performance construction materials in Iraq. Discover our premium Drylex waterproofing systems, concrete admixtures, repair materials, and construction chemicals for superior building solutions.",
    images: ["/og-home.jpg"],
    url: "https://drylexiraq.com",
    type: "website",
    siteName: "DRYLEX Iraq",
    locale: "en_IQ",
  },
  twitter: {
    card: "summary_large_image",
    title: "DRYLEX Iraq | Premium Construction Materials & Services",
    description: "DRYLEX Iraq - Your trusted authorized distributor for high-performance construction materials in Iraq. Discover our premium Drylex waterproofing systems, concrete admixtures, repair materials, and construction chemicals for superior building solutions.",
    images: ["/og-home.jpg"],
    creator: "@drylexiraq",
  },
  alternates: {
    canonical: "https://drylexiraq.com",
  },
}

export default function HomePage() {
  const settings = db.getSettings()
  const allProducts = db.getProducts()
  const drylexProducts = allProducts.filter(p => p.category.includes('Drylex')).slice(0, 2)
  const otherProducts = allProducts.filter(p => !p.category.includes('Drylex')).slice(0, 1)
  const featuredProducts = [...drylexProducts, ...otherProducts]
  const featuredProjects = db.getProjects().slice(0, 2)

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex construction materials in Iraq, offering high-performance waterproofing systems, concrete admixtures, repair materials, and construction chemicals.",
    "foundingDate": "1995",
    "founder": {
      "@type": "Person",
      "name": "Founder Name"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": settings.phone,
      "contactType": "sales",
      "areaServed": "IQ",
      "availableLanguage": ["en", "ar"]
    },
    "address": {
      "@type": "PostalAddress",
      "streetAddress": settings.address,
      "addressLocality": "Nassiriya",
      "addressRegion": "Thi Qar",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "sameAs": [
      "https://facebook.com/drylexiraq",
      "https://instagram.com/drylexiraq",
      "https://linkedin.com/company/drylexiraq"
    ],
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "150"
    },
    "makesOffer": {
      "@type": "OfferCatalog",
      "name": "Construction Materials Catalog",
      "url": "https://drylexiraq.com/products",
      "itemListOrder": "https://schema.org/ItemListOrderAscending",
      "numberOfItems": "50"
    }
  }

  // JSON-LD for WebSite
  const websiteJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "url": "https://drylexiraq.com",
    "name": "DRYLEX Iraq",
    "description": "Leading supplier of high-performance construction materials in Iraq",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://drylexiraq.com/search?q={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(websiteJsonLd) }} />
      <div className="min-h-screen">
        <Hero slides={settings.heroSlides} />
        <AnimatedSection>
          <FeaturedSection products={featuredProducts} projects={featuredProjects} />
        </AnimatedSection>
        <WhatsAppFloat number={settings.whatsappNumber} />
        <ContactSticky phone={settings.phone} email={settings.email} />
      </div>
    </>
  )
}
