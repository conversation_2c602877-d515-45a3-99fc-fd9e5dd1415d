import { type NextRequest, NextResponse } from "next/server"

// Mock database connection (replace with actual database)
const mockImageSizes = [
  {
    id: 1,
    section_name: "products",
    image_type: "thumbnail",
    width: 400,
    height: 300,
    aspect_ratio: "4:3",
    description: "Product thumbnail for grid display",
    is_active: true,
    usage_count: 156,
    last_updated: "2024-01-15T10:30:00Z",
  },
  {
    id: 2,
    section_name: "products",
    image_type: "detail",
    width: 800,
    height: 600,
    aspect_ratio: "4:3",
    description: "Product detail page main image",
    is_active: true,
    usage_count: 89,
    last_updated: "2024-01-14T15:20:00Z",
  },
  {
    id: 3,
    section_name: "slider",
    image_type: "desktop",
    width: 1920,
    height: 1080,
    aspect_ratio: "16:9",
    description: "Desktop slider images",
    is_active: true,
    usage_count: 12,
    last_updated: "2024-01-16T09:15:00Z",
  },
  {
    id: 4,
    section_name: "slider",
    image_type: "mobile",
    width: 768,
    height: 1024,
    aspect_ratio: "3:4",
    description: "Mobile slider images",
    is_active: true,
    usage_count: 12,
    last_updated: "2024-01-16T09:15:00Z",
  },
  {
    id: 5,
    section_name: "services",
    image_type: "icon",
    width: 64,
    height: 64,
    aspect_ratio: "1:1",
    description: "Service icon images",
    is_active: true,
    usage_count: 24,
    last_updated: "2024-01-12T14:45:00Z",
  },
  {
    id: 6,
    section_name: "services",
    image_type: "banner",
    width: 1200,
    height: 400,
    aspect_ratio: "3:1",
    description: "Service banner images",
    is_active: true,
    usage_count: 8,
    last_updated: "2024-01-13T11:30:00Z",
  },
  {
    id: 7,
    section_name: "projects",
    image_type: "before",
    width: 800,
    height: 600,
    aspect_ratio: "4:3",
    description: "Project before images",
    is_active: true,
    usage_count: 45,
    last_updated: "2024-01-14T16:20:00Z",
  },
  {
    id: 8,
    section_name: "projects",
    image_type: "after",
    width: 800,
    height: 600,
    aspect_ratio: "4:3",
    description: "Project after images",
    is_active: true,
    usage_count: 45,
    last_updated: "2024-01-14T16:20:00Z",
  },
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const section = searchParams.get("section")
    const type = searchParams.get("type")

    let filteredSizes = mockImageSizes

    if (section) {
      filteredSizes = filteredSizes.filter((size) => size.section_name === section)
    }

    if (type) {
      filteredSizes = filteredSizes.filter((size) => size.image_type === type)
    }

    return NextResponse.json({
      success: true,
      data: filteredSizes,
      total: filteredSizes.length,
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to fetch image sizes" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { section_name, image_type, width, height, description } = body

    // Validate required fields
    if (!section_name || !image_type || !width || !height) {
      return NextResponse.json({ success: false, error: "Missing required fields" }, { status: 400 })
    }

    // Calculate aspect ratio
    const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b))
    const divisor = gcd(width, height)
    const aspect_ratio = `${width / divisor}:${height / divisor}`

    const newImageSize = {
      id: mockImageSizes.length + 1,
      section_name,
      image_type,
      width,
      height,
      aspect_ratio,
      description: description || "",
      is_active: true,
      usage_count: 0,
      last_updated: new Date().toISOString(),
    }

    mockImageSizes.push(newImageSize)

    return NextResponse.json({
      success: true,
      data: newImageSize,
      message: "Image size configuration created successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to create image size configuration" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, section_name, image_type, width, height, description, is_active } = body

    const index = mockImageSizes.findIndex((size) => size.id === id)
    if (index === -1) {
      return NextResponse.json({ success: false, error: "Image size configuration not found" }, { status: 404 })
    }

    // Calculate aspect ratio
    const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b))
    const divisor = gcd(width, height)
    const aspect_ratio = `${width / divisor}:${height / divisor}`

    mockImageSizes[index] = {
      ...mockImageSizes[index],
      section_name: section_name || mockImageSizes[index].section_name,
      image_type: image_type || mockImageSizes[index].image_type,
      width: width || mockImageSizes[index].width,
      height: height || mockImageSizes[index].height,
      aspect_ratio,
      description: description || mockImageSizes[index].description,
      is_active: is_active !== undefined ? is_active : mockImageSizes[index].is_active,
      last_updated: new Date().toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: mockImageSizes[index],
      message: "Image size configuration updated successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to update image size configuration" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = Number.parseInt(searchParams.get("id") || "0")

    const index = mockImageSizes.findIndex((size) => size.id === id)
    if (index === -1) {
      return NextResponse.json({ success: false, error: "Image size configuration not found" }, { status: 404 })
    }

    mockImageSizes.splice(index, 1)

    return NextResponse.json({
      success: true,
      message: "Image size configuration deleted successfully",
    })
  } catch (error) {
    return NextResponse.json({ success: false, error: "Failed to delete image size configuration" }, { status: 500 })
  }
}
