using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ConstructionWebsite.API.Models
{
    public class ApplicationUser : IdentityUser
    {
        [StringLength(100)]
        public string? FirstName { get; set; }

        [StringLength(100)]
        public string? LastName { get; set; }

        [StringLength(200)]
        public string? Company { get; set; }

        [StringLength(100)]
        public string? JobTitle { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        [StringLength(100)]
        public string? City { get; set; }

        [StringLength(100)]
        public string? Country { get; set; } = "IRAQ";

        [StringLength(2)]
        public string PreferredLanguage { get; set; } = "en";

        [StringLength(500)]
        public string? ProfileImage { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime? LastLoginAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(500)]
        public string? RefreshToken { get; set; }

        public DateTime? RefreshTokenExpiryTime { get; set; }

        // Navigation Properties
        public virtual ICollection<WishlistItem> WishlistItems { get; set; } = new List<WishlistItem>();
        public virtual ICollection<ContactMessage> ContactMessages { get; set; } = new List<ContactMessage>();
    }

    public class ContactMessage
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Company { get; set; }

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "ntext")]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string InquiryType { get; set; } = "general";

        public int? RelatedId { get; set; }

        [StringLength(20)]
        public string? RelatedType { get; set; }

        [StringLength(20)]
        public string PreferredContact { get; set; } = "email";

        [StringLength(2)]
        public string PreferredLanguage { get; set; } = "en";

        [StringLength(100)]
        public string? BudgetRange { get; set; }

        [StringLength(100)]
        public string? Timeline { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(50)]
        public string? Source { get; set; }

        [StringLength(100)]
        public string? UtmSource { get; set; }

        [StringLength(100)]
        public string? UtmMedium { get; set; }

        [StringLength(100)]
        public string? UtmCampaign { get; set; }

        [StringLength(45)]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "unread";

        [StringLength(20)]
        public string Priority { get; set; } = "medium";

        [StringLength(450)]
        public string? AssignedTo { get; set; }

        [Column(TypeName = "ntext")]
        public string? InternalNotes { get; set; }

        public DateTime? ResponseSentAt { get; set; }

        public bool IsRead { get; set; } = false;

        public bool IsDeleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ApplicationUser? AssignedUser { get; set; }
    }

    public class Translation
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string TranslationKey { get; set; } = string.Empty;

        [Required]
        [StringLength(5)]
        public string LanguageCode { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "ntext")]
        public string TranslationValue { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Context { get; set; }

        [StringLength(100)]
        public string? Page { get; set; }

        [StringLength(100)]
        public string? Section { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    public class SiteSettings
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string CompanyName { get; set; } = string.Empty;

        [StringLength(200)]
        public string? CompanyNameAr { get; set; }

        [StringLength(500)]
        public string? Logo { get; set; }

        [StringLength(500)]
        public string? Favicon { get; set; }

        [StringLength(20)]
        public string? WhatsappNumber { get; set; }

        [StringLength(200)]
        public string? Email { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(500)]
        public string? AddressAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? BusinessHours { get; set; }

        [Column(TypeName = "ntext")]
        public string? HeroSlides { get; set; }

        [Column(TypeName = "ntext")]
        public string? SocialMedia { get; set; }

        [Column(TypeName = "ntext")]
        public string? SeoSettings { get; set; }

        [Column(TypeName = "ntext")]
        public string? AnalyticsSettings { get; set; }

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
