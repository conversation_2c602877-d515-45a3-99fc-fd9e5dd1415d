import { NextRequest, NextResponse } from "next/server"

// Proxy to backend API to avoid CORS issues
export async function POST(request: NextRequest) {
  console.log('Proxy login API called');
  
  try {
    const body = await request.json();
    console.log('Proxying request to backend:', { email: body.email, password: '***' });
    
    // Forward the request to the actual backend
    const backendResponse = await fetch('https://localhost:56266/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Forward any relevant headers
        'User-Agent': request.headers.get('user-agent') || 'NextJS-Proxy',
      },
      body: JSON.stringify(body),
      // Disable SSL verification for development (NEVER do this in production)
      // @ts-ignore
      rejectUnauthorized: false
    });
    
    console.log('Backend response status:', backendResponse.status);
    
    if (backendResponse.ok) {
      const data = await backendResponse.json();
      console.log('Backend response success:', { success: data.success });
      
      return NextResponse.json(data);
    } else {
      const errorText = await backendResponse.text();
      console.log('Backend error response:', errorText);
      
      return NextResponse.json(
        { success: false, message: 'Backend authentication failed' },
        { status: backendResponse.status }
      );
    }
    
  } catch (error) {
    console.error("Proxy login error:", error);
    
    // Check if it's a network/SSL error
    if (error instanceof Error) {
      if (error.message.includes('ECONNREFUSED')) {
        return NextResponse.json(
          { success: false, message: 'Backend server is not running on https://localhost:56266' },
          { status: 503 }
        );
      }
      if (error.message.includes('certificate') || error.message.includes('SSL')) {
        return NextResponse.json(
          { success: false, message: 'SSL certificate error. Backend may be using self-signed certificate.' },
          { status: 503 }
        );
      }
    }
    
    return NextResponse.json(
      { success: false, message: 'Proxy request failed: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
