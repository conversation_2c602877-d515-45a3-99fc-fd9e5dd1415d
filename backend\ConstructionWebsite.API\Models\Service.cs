using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ConstructionWebsite.API.Models
{
    public class Service
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Category")]
        public int? CategoryId { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? ShortDescription { get; set; }

        [StringLength(500)]
        public string? ShortDescriptionAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Description { get; set; }

        [Column(TypeName = "ntext")]
        public string? DescriptionAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Features { get; set; }

        [Column(TypeName = "ntext")]
        public string? FeaturesAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? ProcessSteps { get; set; }

        [Column(TypeName = "ntext")]
        public string? ProcessStepsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Applications { get; set; }

        [Column(TypeName = "ntext")]
        public string? ApplicationsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Benefits { get; set; }

        [Column(TypeName = "ntext")]
        public string? BenefitsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? EquipmentUsed { get; set; }

        [Column(TypeName = "ntext")]
        public string? EquipmentUsedAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? MaterialsUsed { get; set; }

        [Column(TypeName = "ntext")]
        public string? MaterialsUsedAr { get; set; }

        [StringLength(200)]
        public string? DurationEstimate { get; set; }

        [StringLength(200)]
        public string? DurationEstimateAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? WarrantyInfo { get; set; }

        [Column(TypeName = "ntext")]
        public string? WarrantyInfoAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Certifications { get; set; }

        [Column(TypeName = "ntext")]
        public string? ServiceAreas { get; set; }

        [StringLength(50)]
        public string PricingModel { get; set; } = "quote";

        [Column(TypeName = "decimal(18,2)")]
        public decimal? BasePrice { get; set; }

        [StringLength(3)]
        public string Currency { get; set; } = "AED";

        [StringLength(50)]
        public string? Unit { get; set; }

        [StringLength(200)]
        public string? MinProjectSize { get; set; }

        [StringLength(100)]
        public string? LeadTime { get; set; }

        [StringLength(50)]
        public string Availability { get; set; } = "business_hours";

        [StringLength(200)]
        public string? SeoTitle { get; set; }

        [StringLength(200)]
        public string? SeoTitleAr { get; set; }

        [StringLength(500)]
        public string? SeoDescription { get; set; }

        [StringLength(500)]
        public string? SeoDescriptionAr { get; set; }

        [StringLength(500)]
        public string? SeoKeywords { get; set; }

        [StringLength(500)]
        public string? SeoKeywordsAr { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "active";

        public bool IsFeatured { get; set; } = false;

        public bool IsEmergency { get; set; } = false;

        public int ViewCount { get; set; } = 0;

        public int InquiryCount { get; set; } = 0;

        public int CompletionCount { get; set; } = 0;

        [Column(TypeName = "decimal(3,2)")]
        public decimal Rating { get; set; } = 0;

        public int SortOrder { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public bool IsDeleted { get; set; } = false;

        // Navigation Properties
        public virtual ServiceCategory? Category { get; set; }
        public virtual ICollection<ServiceImage> Images { get; set; } = new List<ServiceImage>();
        public virtual ICollection<ServiceInquiry> Inquiries { get; set; } = new List<ServiceInquiry>();
    }

    public class ServiceCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<Service> Services { get; set; } = new List<Service>();
    }

    public class ServiceImage
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Service")]
        public int ServiceId { get; set; }

        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ImageType { get; set; } = string.Empty;

        [StringLength(200)]
        public string? AltText { get; set; }

        [StringLength(200)]
        public string? AltTextAr { get; set; }

        [StringLength(500)]
        public string? Caption { get; set; }

        [StringLength(500)]
        public string? CaptionAr { get; set; }

        public int? Width { get; set; }

        public int? Height { get; set; }

        public long? FileSize { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsPrimary { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Service Service { get; set; } = null!;
    }

    public class ServiceInquiry
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Service")]
        public int ServiceId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Company { get; set; }

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "ntext")]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string InquiryType { get; set; } = "general";

        [StringLength(100)]
        public string? BudgetRange { get; set; }

        [StringLength(100)]
        public string? Timeline { get; set; }

        [StringLength(200)]
        public string? Location { get; set; }

        [StringLength(50)]
        public string Source { get; set; } = "website";

        [StringLength(45)]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "unread";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Service Service { get; set; } = null!;
    }
}
