"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Hammer, Shield, Droplets, Paintbrush, Wrench, CheckCircle, ArrowRight, Phone } from "lucide-react"
import Image from "next/image"
import { AnimatedSection } from "@/components/animated-section"
import { AnimatedCard } from "@/components/animated-card"
import { db } from "@/lib/database"
import type { Service } from "@/lib/database"
import Head from "next/head"
import { Metadata } from "next"

const iconMap = {
  Hammer,
  Shield,
  Droplets,
  Paintbrush,
  Wrench,
  CheckCircle,
}

// JSON-LD for Services Page
const servicesJsonLd = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  name: "Construction Services - DRYLEX Iraq",
  description:
    "Professional construction services from DRYLEX Iraq including waterproofing systems, concrete admixtures, repair materials and construction chemical solutions.",
  url: "https://drylexiraq.com/services",
  mainEntity: {
    "@type": "ItemList",
    name: "Construction Services",
    numberOfItems: 20,
    itemListElement: [
      {
        "@type": "Service",
        name: "Waterproofing Solutions",
        description: "Premium waterproofing membranes and liquid systems for Iraqi construction projects",
      },
      {
        "@type": "Service",
        name: "Concrete Repair Services",
        description: "Advanced concrete repair and injection solutions for infrastructure projects",
      },
      {
        "@type": "Service",
        name: "Construction Chemical Solutions",
        description: "Specialized chemical solutions for construction challenges in Iraq",
      },
    ],
  },
}

export default function ServicesPage() {
  const settings = db.getSettings() || { address: '', phone: '', email: '' }
  const services = db.getServices()

  // JSON-LD for Service
  const serviceJsonLd = {
    "@context": "https://schema.org",
    "@type": "Service",
    name: "Construction Material Solutions",
    description: "Comprehensive construction material solutions for Iraqi construction projects",
    provider: {
      "@type": "Organization",
      name: "DRYLEX IRAQ",
      url: "https://drylexiraq.com",
      logo: "https://drylexiraq.com/logo.png",
    },
    category: "Construction Materials > Construction Services",
    image: "https://drylexiraq.com/images/services/construction-service.jpg",
    offers: {
      "@type": "OfferCatalog",
      name: "Construction Services Catalog",
      url: "https://drylexiraq.com/services",
      numberOfItems: "10",
    },
    serviceType: "Construction Service",
    areaServed: "Iraq",
    availableChannel: {
      "@type": "ServiceChannel",
      name: "Contact Us",
      providesService: {
        "@type": "Service",
        name: "Free Consultation",
        description: "Get free consultation for our construction services",
        url: "https://drylexiraq.com/contact"
      }
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ali Al-Maliki",
        datePublished: "2024-08-15",
        name: "Excellent service",
        reviewBody: "DRYLEX provided excellent waterproofing service for our construction project in Baghdad. High-quality materials and professional application."
      },
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ahmed Al-Sudani",
        datePublished: "2024-07-20",
        name: "Professional approach",
        reviewBody: "The technical support team at DRYLEX is very professional and knowledgeable about construction materials."
      },
    ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      reviewCount: "65",
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": settings.address,
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": settings.phone,
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(serviceJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />

      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <AnimatedSection className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Construction Services</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Professional construction services delivered by experienced specialists across IRAQ since 1995.
            </p>
          </AnimatedSection>

          {/* Services Grid */}
          <div className="space-y-12">
            {services.map((service, index) => {
              const IconComponent = iconMap[service.icon as keyof typeof iconMap] || CheckCircle

              return (
                <AnimatedCard key={service.id} delay={index * 0.2}>
                  <Card className="overflow-hidden">
                    <div
                      className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${index % 2 === 1 ? "lg:grid-flow-col-dense" : ""}`}
                    >
                      {/* Image */}
                      <div className={`relative ${index % 2 === 1 ? "lg:col-start-2" : ""}`}>
                        <Image
                          src={service.image || "/placeholder.svg"}
                          alt={`${service.name} - Professional construction service by DRYLEX  IRAQ`}
                          width={600}
                          height={400}
                          className="w-full h-64 lg:h-full object-cover"
                          loading={index < 2 ? "eager" : "lazy"}
                        />
                        <div className="absolute top-4 left-4">
                          <div className="bg-orange-600 text-white p-3 rounded-full">
                            <IconComponent className="h-6 w-6" />
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className={`p-6 lg:p-8 ${index % 2 === 1 ? "lg:col-start-1" : ""}`}>
                        <div className="space-y-6">
                          <div>
                            <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">{service.name}</h2>
                            <p className="text-lg text-gray-500 mb-4">{service.nameAr}</p>
                            <p className="text-gray-600 text-lg leading-relaxed">{service.description}</p>
                          </div>

                          {/* Features */}
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-3">Key Features:</h3>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                              {service.features.map((feature, idx) => (
                                <div key={idx} className="flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                                  <span className="text-sm text-gray-600">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Applications */}
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-3">Applications:</h3>
                            <div className="flex flex-wrap gap-2">
                              {service.applications.map((app, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {app}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Process */}
                          <div>
                            <h3 className="font-semibold text-gray-900 mb-3">Our Process:</h3>
                            <div className="space-y-2">
                              {service.process.map((step, idx) => (
                                <div key={idx} className="flex items-center gap-3">
                                  <div className="w-6 h-6 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center text-xs font-bold">
                                    {idx + 1}
                                  </div>
                                  <span className="text-sm text-gray-600">{step}</span>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* CTA Buttons */}
                          <div className="flex flex-col sm:flex-row gap-3 pt-4">
                            <Button
                              className="bg-orange-600 hover:bg-orange-700"
                              onClick={() => handleContactService(service.name)}
                              aria-label={`Get consultation for ${service.name}`}
                            >
                              <Phone className="h-4 w-4 mr-2" />
                              Get Consultation
                            </Button>
                            <Button variant="outline" aria-label={`Learn more about ${service.name}`}>
                              Learn More
                              <ArrowRight className="h-4 w-4 ml-2" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                </AnimatedCard>
              )
            })}
          </div>

          {/* CTA Section */}
          <AnimatedSection className="mt-16 text-center bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Need a Custom Construction Solution?</h2>
            <p className="text-xl text-gray-600 mb-6 max-w-2xl mx-auto">
              Our team of experts can develop tailored solutions for your specific construction challenges across IRAQ
            </p>
            <Button size="lg" className="bg-orange-600 hover:bg-orange-700">
              <Phone className="h-5 w-5 mr-2" />
              Contact Our Experts
            </Button>
          </AnimatedSection>
        </div>
      </div>
    </>
  )
}
