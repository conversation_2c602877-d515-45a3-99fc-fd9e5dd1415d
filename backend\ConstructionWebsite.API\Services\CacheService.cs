using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace ConstructionWebsite.API.Services
{
    public class CacheService : ICacheService
    {
        private readonly IDistributedCache? _distributedCache;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<CacheService> _logger;
        private readonly bool _useDistributedCache;
        private readonly JsonSerializerOptions _jsonOptions;

        public CacheService(
            IMemoryCache memoryCache,
            ILogger<CacheService> logger,
            IDistributedCache? distributedCache = null)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
            _useDistributedCache = distributedCache != null;
            
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                if (_useDistributedCache)
                {
                    var cachedValue = await _distributedCache!.GetStringAsync(key);
                    if (!string.IsNullOrEmpty(cachedValue))
                    {
                        return JsonSerializer.Deserialize<T>(cachedValue, _jsonOptions);
                    }
                }
                else
                {
                    if (_memoryCache.TryGetValue(key, out T? cachedValue))
                    {
                        return cachedValue;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached value for key: {Key}", key);
                return null;
            }
        }

        public async Task<string?> GetStringAsync(string key)
        {
            try
            {
                if (_useDistributedCache)
                {
                    return await _distributedCache!.GetStringAsync(key);
                }
                else
                {
                    if (_memoryCache.TryGetValue(key, out string? cachedValue))
                    {
                        return cachedValue;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cached string for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var defaultExpiration = expiration ?? TimeSpan.FromMinutes(30);

                if (_useDistributedCache)
                {
                    var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
                    var options = new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = defaultExpiration
                    };
                    await _distributedCache!.SetStringAsync(key, serializedValue, options);
                }
                else
                {
                    var options = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = defaultExpiration,
                        SlidingExpiration = TimeSpan.FromMinutes(5)
                    };
                    _memoryCache.Set(key, value, options);
                }

                _logger.LogDebug("Cached value set for key: {Key} with expiration: {Expiration}", key, defaultExpiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached value for key: {Key}", key);
                throw;
            }
        }

        public async Task SetStringAsync(string key, string value, TimeSpan? expiration = null)
        {
            try
            {
                var defaultExpiration = expiration ?? TimeSpan.FromMinutes(30);

                if (_useDistributedCache)
                {
                    var options = new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = defaultExpiration
                    };
                    await _distributedCache!.SetStringAsync(key, value, options);
                }
                else
                {
                    var options = new MemoryCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = defaultExpiration,
                        SlidingExpiration = TimeSpan.FromMinutes(5)
                    };
                    _memoryCache.Set(key, value, options);
                }

                _logger.LogDebug("Cached string set for key: {Key} with expiration: {Expiration}", key, defaultExpiration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cached string for key: {Key}", key);
                throw;
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                if (_useDistributedCache)
                {
                    await _distributedCache!.RemoveAsync(key);
                }
                else
                {
                    _memoryCache.Remove(key);
                }

                _logger.LogDebug("Removed cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
                throw;
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                if (_useDistributedCache)
                {
                    // Note: Redis pattern removal would require additional Redis-specific implementation
                    _logger.LogWarning("Pattern-based removal not fully implemented for distributed cache");
                }
                else
                {
                    // For memory cache, we'd need to track keys separately
                    // This is a simplified implementation
                    _logger.LogWarning("Pattern-based removal not implemented for memory cache");
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (_useDistributedCache)
                {
                    var value = await _distributedCache!.GetStringAsync(key);
                    return !string.IsNullOrEmpty(value);
                }
                else
                {
                    return _memoryCache.TryGetValue(key, out _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if key exists: {Key}", key);
                return false;
            }
        }

        public async Task ClearAllAsync()
        {
            try
            {
                if (_useDistributedCache)
                {
                    // Note: This would require Redis-specific implementation to flush all keys
                    _logger.LogWarning("Clear all not implemented for distributed cache");
                }
                else
                {
                    // Memory cache doesn't have a clear all method, would need custom implementation
                    _logger.LogWarning("Clear all not implemented for memory cache");
                }

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing all cached values");
                throw;
            }
        }

        public async Task RefreshAsync(string key)
        {
            try
            {
                if (_useDistributedCache)
                {
                    await _distributedCache!.RefreshAsync(key);
                }
                // Memory cache doesn't need explicit refresh

                _logger.LogDebug("Refreshed cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing cached value for key: {Key}", key);
                throw;
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> getItem, TimeSpan? expiration = null) where T : class
        {
            try
            {
                var cachedValue = await GetAsync<T>(key);
                if (cachedValue != null)
                {
                    return cachedValue;
                }

                var item = await getItem();
                if (item != null)
                {
                    await SetAsync(key, item, expiration);
                }

                return item;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetOrSetAsync for key: {Key}", key);
                throw;
            }
        }
    }
}