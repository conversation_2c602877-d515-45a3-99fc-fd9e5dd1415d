import { NextRequest, NextResponse } from "next/server"
import { zeroRiskAuth } from "@/utils/advanced-auth"
import { zeroRiskValidator } from "@/utils/zero-risk-validation"
import { validateAPIRequest, sanitizeRequestBody } from "@/middleware/zero-risk-security"

// Zero-risk admin credentials
const ADMIN_CREDENTIALS = {
  username: process.env.ADMIN_USERNAME || "admin",
  passwordHash: process.env.ADMIN_PASSWORD_HASH || "$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj4J7Q8Q8Q8Q"
}

const JWT_SECRET = process.env.JWT_SECRET || "your-super-secret-jwt-key-change-this-in-production"
const JWT_EXPIRES_IN = "15m" // 15 minutes session for zero-risk

export async function POST(request: NextRequest) {
  console.log('Admin login API called');

  try {
    // Zero-risk API validation
    console.log('Validating API request...');
    if (!validateAPIRequest(request)) {
      console.log('API request validation failed');
      return NextResponse.json(
        { message: "Request blocked by security policy" },
        { status: 403 }
      )
    }

    // Sanitize request body
    console.log('Sanitizing request body...');
    const { username, password } = await sanitizeRequestBody(request)
    console.log('Sanitized credentials:', { username, password: password ? '***' : 'empty' });

    // Validate input with zero-risk validator
    const usernameValidation = zeroRiskValidator.validateInput(username, 'text', 50)
    const passwordValidation = zeroRiskValidator.validateInput(password, 'text', 100)

    if (!usernameValidation.isValid || !passwordValidation.isValid) {
      return NextResponse.json(
        { message: "Invalid input detected" },
        { status: 400 }
      )
    }

    const clientIP = request.headers.get("x-forwarded-for") || 
                    request.headers.get("x-real-ip") || 
                    "unknown"
    const userAgent = request.headers.get("user-agent") || "unknown"

    // Check for suspicious activity
    if (zeroRiskAuth.checkSuspiciousActivity(clientIP)) {
      return NextResponse.json(
        { message: "Account temporarily locked due to suspicious activity" },
        { status: 429 }
      )
    }
    
    // Check if username matches
    if (usernameValidation.sanitizedValue !== ADMIN_CREDENTIALS.username) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Verify password with zero-risk auth
    const isValidPassword = await zeroRiskAuth.verifyPassword(
      passwordValidation.sanitizedValue, 
      ADMIN_CREDENTIALS.passwordHash
    )
    
    if (!isValidPassword) {
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      )
    }

    // Create secure session with MFA requirement
    const session = zeroRiskAuth.createSession(
      "admin",
      ADMIN_CREDENTIALS.username,
      clientIP,
      userAgent
    )

    // Generate MFA code (in production, send via SMS/email)
    const mfaCode = zeroRiskAuth.generateMFACode()
    
    // Store MFA code temporarily (in production, use secure storage)
    // For demo purposes, we'll return it (NEVER do this in production)
    console.log(`MFA Code for ${ADMIN_CREDENTIALS.username}: ${mfaCode}`)

    return NextResponse.json({
      success: true,
      sessionId: session.sessionId,
      requiresMFA: true,
      message: "MFA code sent to your registered device"
    })

  } catch (error) {
    console.error("Zero-risk admin login error:", error)
    return NextResponse.json(
      { message: "Authentication failed" },
      { status: 500 }
    )
  }
}
