"use client"

import { useEffect, useState } from "react"
import { generateCSRFToken, validateCSRFToken, sanitizeInput, validateEmail } from "@/utils/security"

interface SecureFormProps {
  onSubmit: (data: FormData) => Promise<void>
  children: React.ReactNode
  className?: string
}

export function SecureForm({ onSubmit, children, className }: SecureFormProps) {
  const [csrfToken, setCsrfToken] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // Generate CSRF token on component mount
    setCsrfToken(generateCSRFToken())
  }, [])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const formData = new FormData(e.currentTarget)
      
      // Validate CSRF token
      const submittedToken = formData.get('_csrf') as string
      if (!validateCSRFToken(submittedToken, csrfToken)) {
        throw new Error('Invalid CSRF token')
      }

      // Sanitize all text inputs
      const sanitizedData = new FormData()
      for (const [key, value] of formData.entries()) {
        if (typeof value === 'string') {
          sanitizedData.append(key, sanitizeInput(value))
        } else {
          sanitizedData.append(key, value)
        }
      }

      await onSubmit(sanitizedData)
    } catch (error) {
      console.error('Form submission error:', error)
      alert('Form submission failed. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      <input type="hidden" name="_csrf" value={csrfToken} />
      {children}
      {isSubmitting && (
        <div className="text-center text-gray-600">
          Submitting...
        </div>
      )}
    </form>
  )
}

// Secure input component with validation
interface SecureInputProps {
  name: string
  type?: string
  placeholder?: string
  required?: boolean
  maxLength?: number
  validation?: (value: string) => string | null
  className?: string
}

export function SecureInput({ 
  name, 
  type = "text", 
  placeholder, 
  required = false, 
  maxLength = 1000,
  validation,
  className 
}: SecureInputProps) {
  const [value, setValue] = useState('')
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value
    setValue(inputValue)

    // Real-time validation
    if (validation) {
      const errorMessage = validation(inputValue)
      setError(errorMessage || '')
    }

    // Email validation
    if (type === 'email' && inputValue && !validateEmail(inputValue)) {
      setError('Invalid email format')
    }
  }

  return (
    <div>
      <input
        name={name}
        type={type}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        required={required}
        maxLength={maxLength}
        className={`${className} ${error ? 'border-red-500' : ''}`}
      />
      {error && <span className="text-red-500 text-sm">{error}</span>}
    </div>
  )
}
