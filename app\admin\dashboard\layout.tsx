"use client"

import type React from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Home, Package, FolderOpen, MessageSquare, Settings, LogOut } from "lucide-react"

export default function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const handleLogout = () => {
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("adminSession")
      window.location.href = "/admin"
    }
  }

  const navigation = [
    { name: "Dashboard", href: "/admin/dashboard", icon: Home },
    { name: "Products", href: "/admin/dashboard/products", icon: Package },
    { name: "Projects", href: "/admin/dashboard/projects", icon: FolderOpen },
    { name: "Messages", href: "/admin/dashboard/messages", icon: MessageSquare },
    { name: "Settings", href: "/admin/dashboard/settings", icon: Settings },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <Link href="/admin/dashboard" className="flex items-center space-x-2">
                <img src="/placeholder.svg?height=32&width=96" alt="DRYLEX  Admin" className="h-8 w-auto" />
                <span className="text-sm text-gray-500">Admin</span>
              </Link>

              <nav className="hidden md:flex items-center space-x-6">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="flex items-center space-x-2 text-sm font-medium text-gray-600 hover:text-orange-600 transition-colors"
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.name}</span>
                  </Link>
                ))}
              </nav>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="outline" asChild>
                <Link href="/">View Website</Link>
              </Button>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main>{children}</main>
    </div>
  )
}
