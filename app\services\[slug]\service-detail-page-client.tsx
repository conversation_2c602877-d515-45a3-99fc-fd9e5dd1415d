"use client"

import { useState, useEffect } from "react"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"
import { enhancedDb, type Service } from "@/lib/database-enhanced"
import { notFound } from "next/navigation"
import { ServiceDetailClient } from "./service-detail-client"

interface ServicePageProps {
  params: { slug: string }
  searchParams: { [key: string]: string | string[] | undefined }
}

function ServiceDetailLoading() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="animate-pulse space-y-8">
          <div className="h-6 bg-gray-300 rounded w-1/3"></div>
          <div className="h-64 bg-gray-300 rounded"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-300 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-300 rounded"></div>
        </div>
      </div>
    </div>
  )
}

export default function ServiceDetailPageClientComponent({ params }: ServicePageProps) {
  try {
    const { slug } = params
    const [service, setService] = useState<Service | null>(null)
    const [relatedServices, setRelatedServices] = useState<Service[]>([])
    const [loading, setLoading] = useState(true)

    useEffect(() => {
      const fetchData = async () => {
        try {
          const fetchedService = await enhancedDb.getServiceBySlug(slug)

          if (!fetchedService || fetchedService.status !== "active") {
            notFound()
            return
          }

          setService(fetchedService)

          const fetchedRelatedServices = await enhancedDb.getRelatedServices(
            fetchedService.id,
            fetchedService.category_id,
            3,
          )
          setRelatedServices(fetchedRelatedServices)
        } catch (error) {
          console.error("Error loading service:", error)
          notFound()
        } finally {
          setLoading(false)
        }
      }

      fetchData()
    }, [slug])

    if (loading) {
      return <ServiceDetailLoading />
    }

    if (!service) {
      return null
    }

    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "Service",
      "@id": `https://DRYLEX .ae/services/${service.slug}`,
      name: service.name,
      description: service.description,
      provider: {
        "@type": "Organization",
        name: "DRYLEX  Materials & Services",
        url: "https://DRYLEX .ae",
        telephone: "+971-50-123-4567",
      },
      offers: service.base_price
        ? {
            "@type": "Offer",
            price: service.base_price,
            priceCurrency: service.currency,
          }
        : undefined,
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: service.rating,
        reviewCount: service.completion_count,
      },
    }

    return (
      <>
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />

        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <SEOBreadcrumbs
              items={[
                { label: "Services", href: "/services" },
                { label: service.category?.name || "Category", href: `/services?category=${service.category?.slug}` },
                { label: service.name },
              ]}
            />

            <ServiceDetailClient service={service} relatedServices={relatedServices} />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error("Error loading service:", error)
    notFound()
    return null
  }
}
