using ConstructionWebsite.API.Models;

namespace ConstructionWebsite.API.Services
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string body, bool isHtml = true);
        Task SendEmailAsync(string to, string subject, string body, IEnumerable<string>? cc = null, IEnumerable<string>? bcc = null, bool isHtml = true);
        Task SendContactNotificationAsync(ContactMessage contactMessage);
        Task SendWelcomeEmailAsync(string email, string userName);
        Task SendPasswordResetEmailAsync(string email, string resetToken, string resetUrl);
        Task SendPasswordChangedNotificationAsync(string email, string userName);
        Task SendBulkEmailAsync(IEnumerable<string> recipients, string subject, string body, bool isHtml = true);
        Task<bool> ValidateEmailAddressAsync(string email);
        Task SendInquiryNotificationAsync(ProductInquiry inquiry, Product product);
    }
}