// Server-side translation utilities
import enTranslations from '../public/locales/en.json'
import arTranslations from '../public/locales/ar.json'

type Language = 'en' | 'ar'

const translations = {
  en: enTranslations,
  ar: arTranslations,
}

export function getServerTranslation(language: Language = 'en') {
  const t = (key: string, fallback?: string): string => {
    const keys = key.split('.')
    let value: any = translations[language]

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        // Fallback to English if not found
        if (language !== 'en') {
          let fallbackValue: any = translations.en
          for (const fallbackKey of keys) {
            if (fallbackValue && typeof fallbackValue === 'object' && fallbackKey in fallbackValue) {
              fallbackValue = fallbackValue[fallbackKey]
            } else {
              return fallback || key
            }
          }
          return typeof fallbackValue === 'string' ? fallbackValue : fallback || key
        }
        return fallback || key
      }
    }

    return typeof value === 'string' ? value : fallback || key
  }

  return { t, language }
}

export function getStaticTranslations() {
  return {
    en: translations.en,
    ar: translations.ar,
  }
}

