"use client"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin, Clock, ArrowRight } from "lucide-react"
import { useTranslation } from "./translation-provider"

const quickLinks = [
  { key: "footer.links.about", href: "/about", fallback: "About Us" },
  { key: "footer.links.products", href: "/products", fallback: "Products" },
  { key: "footer.links.services", href: "/services", fallback: "Services" },
  { key: "footer.links.projects", href: "/projects", fallback: "Projects" },
  { key: "footer.links.contact", href: "/contact", fallback: "Contact" },
]

const services = [
  { key: "footer.services.concrete", href: "/services/concrete-admixtures", fallback: "Concrete Admixtures" },
  { key: "footer.services.waterproofing", href: "/services/waterproofing", fallback: "Waterproofing Solutions" },
  { key: "footer.services.repair", href: "/services/repair-materials", fallback: "Repair Materials" },
  { key: "footer.services.adhesives", href: "/services/tile-adhesives", fallback: "Tile Adhesives" },
  { key: "footer.services.consulting", href: "/services/consulting", fallback: "Technical Consulting" },
]

const socialLinks = [
  { icon: Facebook, href: "https://facebook.com/drylex", label: "Facebook" },
    { icon: Twitter, href: "https://twitter.com/drylex", label: "Twitter" },
    { icon: Instagram, href: "https://instagram.com/drylex", label: "Instagram" },
    { icon: Linkedin, href: "https://linkedin.com/company/drylex", label: "LinkedIn" },
]

export function Footer() {
  const { t, isRTL } = useTranslation()

  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter Section */}
      <div className="border-b border-gray-800">
        <div className="container mx-auto px-4 py-12">
          <div className={`grid md:grid-cols-2 gap-8 items-center ${isRTL ? "text-right" : "text-left"}`}>
            <div>
              <h3 className="text-2xl font-bold mb-2">{t("footer.newsletter.title", "Stay Updated with DRYLEX")}</h3>
              <p className="text-gray-400">
                {t(
                  "footer.newsletter.description",
                  "Get the latest updates on new products, industry insights, and exclusive offers.",
                )}
              </p>
            </div>
            <div className={`flex gap-4 ${isRTL ? "flex-row-reverse" : ""}`}>
              <Input
                type="email"
                placeholder={t("footer.newsletter.placeholder", "Enter your email")}
                className="bg-gray-800 border-gray-700 text-white placeholder:text-gray-400"
              />
              <Button className="bg-primary hover:bg-primary/90 px-6">
                <ArrowRight className={`h-4 w-4 ${isRTL ? "rotate-180" : ""}`} />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className={`lg:col-span-1 ${isRTL ? "text-right" : "text-left"}`}>
            <div className={`flex items-center space-x-2 mb-6 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              <div className="h-10 w-10 rounded bg-primary flex items-center justify-center">
                <span className="text-white font-bold text-xl">D</span>
              </div>
              <span className="font-bold text-2xl">{t("footer.companyName", "DRYLEX")}</span>
            </div>
            <p className="text-gray-400 mb-6 leading-relaxed">
              {t(
                "footer.companyDescription",
                "Leading construction materials supplier in Iraq since 1995. We provide premium quality products and expert services to build your success.",
              )}
            </p>
            <div className={`flex space-x-4 ${isRTL ? "space-x-reverse" : ""}`}>
              {socialLinks.map((social, index) => (
                <Link
                  key={index}
                  href={social.href}
                  className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center hover:bg-primary transition-colors"
                  aria-label={social.label}
                >
                  <social.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div className={isRTL ? "text-right" : "text-left"}>
            <h4 className="font-semibold text-lg mb-6">{t("footer.quickLinks.title", "Quick Links")}</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-400 hover:text-primary/70 transition-colors">
                    {t(link.key, link.fallback)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className={isRTL ? "text-right" : "text-left"}>
            <h4 className="font-semibold text-lg mb-6">{t("footer.services.title", "Our Services")}</h4>
            <ul className="space-y-3">
              {services.map((service, index) => (
                <li key={index}>
                  <Link href={service.href} className="text-gray-400 hover:text-primary/70 transition-colors">
                    {t(service.key, service.fallback)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className={isRTL ? "text-right" : "text-left"}>
            <h4 className="font-semibold text-lg mb-6">{t("footer.contact.title", "Contact Info")}</h4>
            <div className="space-y-4">
              <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <MapPin className="h-5 w-5 text-primary/70 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-400">Alshimoukh district, district 205, zukak 68, building 40</p>
                  <p className="text-gray-400">Nassiriya, Thi Qar, Iraq</p>
                  <p className="text-gray-400 text-sm mt-1">حي الشموخ - مقابل مجمع الحضارات (تينا) - الناصرية- ذي قار - العراق</p>
                </div>
              </div>
              <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <Phone className="h-5 w-5 text-primary/70 flex-shrink-0" />
                <p className="text-gray-400">+9647867100886</p>
              </div>
              <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <Mail className="h-5 w-5 text-primary/70 flex-shrink-0" />
                <p className="text-gray-400"><EMAIL></p>
              </div>
              <div className={`flex items-start space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                <Clock className="h-5 w-5 text-primary/70 mt-1 flex-shrink-0" />
                <div>
                  <p className="text-gray-400">{t("footer.contact.hours.weekdays", "Sun-Thu: 8:00 AM - 6:00 PM")}</p>
                  <p className="text-gray-400">{t("footer.contact.hours.friday", "Fri: 8:00 AM - 12:00 PM")}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div
            className={`flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 ${isRTL ? "md:flex-row-reverse" : ""}`}
          >
            <p className="text-gray-400 text-sm">
              {t("footer.copyright", "© 2024 DRYLEX - New Generation Solutions. All rights reserved.")}
            </p>
            <div className={`flex space-x-6 text-sm ${isRTL ? "space-x-reverse" : ""}`}>
              <Link href="/privacy" className="text-gray-400 hover:text-primary/70 transition-colors">
                {t("footer.legal.privacy", "Privacy Policy")}
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-primary/70 transition-colors">
                {t("footer.legal.terms", "Terms of Service")}
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-primary/70 transition-colors">
                {t("footer.legal.sitemap", "Sitemap")}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
