using ConstructionWebsite.API.Data;

using ConstructionWebsite.API.DTOs;
using ConstructionWebsite.API.Models;
using Microsoft.EntityFrameworkCore;

namespace ConstructionWebsite.API.Services
{
    public class ServiceService : IServiceService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<ServiceService> _logger;

        public ServiceService(ApplicationDbContext context, ILogger<ServiceService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IEnumerable<Service>> GetAllServicesAsync()
        {
            try
            {
                return await _context.Services
                    .Where(s => !s.IsDeleted)
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all services");
                throw;
            }
        }

        public async Task<Service?> GetServiceByIdAsync(int id)
        {
            try
            {
                return await _context.Services
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving service with ID {ServiceId}", id);
                throw;
            }
        }

        public async Task<Service> CreateServiceAsync(Service service)
        {
            try
            {
                service.CreatedAt = DateTime.UtcNow;
                service.UpdatedAt = DateTime.UtcNow;
                
                _context.Services.Add(service);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Service created with ID {ServiceId}", service.Id);
                return service;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating service");
                throw;
            }
        }

        public async Task<Service?> UpdateServiceAsync(int id, Service service)
        {
            try
            {
                var existingService = await _context.Services
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
                
                if (existingService == null)
                    return null;

                existingService.Name = service.Name;
                existingService.Description = service.Description;
                existingService.CategoryId = service.CategoryId;
                existingService.BasePrice = service.BasePrice;
                existingService.Status = service.Status;
                existingService.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Service updated with ID {ServiceId}", id);
                return existingService;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating service with ID {ServiceId}", id);
                throw;
            }
        }

        public async Task<bool> DeleteServiceAsync(int id)
        {
            try
            {
                var service = await _context.Services
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);
                
                if (service == null)
                    return false;

                service.IsDeleted = true;
                service.UpdatedAt = DateTime.UtcNow;
                
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Service deleted with ID {ServiceId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting service with ID {ServiceId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<Service>> GetServicesByCategoryAsync(string category)
        {
            try
            {
                return await _context.Services
                    .Where(s => !s.IsDeleted && s.Category != null && s.Category.Name.ToLower() == category.ToLower())
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving services by category {Category}", category);
                throw;
            }
        }

        public async Task<IEnumerable<Service>> SearchServicesAsync(string searchTerm)
        {
            try
            {
                return await _context.Services
                    .Where(s => !s.IsDeleted && 
                        (s.Name.Contains(searchTerm) || 
                         (s.Description != null && s.Description.Contains(searchTerm)) ||
                         (s.Category != null && s.Category.Name.Contains(searchTerm))))
                    .OrderBy(s => s.Name)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching services with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        public async Task<bool> ServiceExistsAsync(int id)
        {
            try
            {
                return await _context.Services
                    .AnyAsync(s => s.Id == id && !s.IsDeleted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if service exists with ID {ServiceId}", id);
                throw;
            }
        }

        public Task<PagedResult<ServiceDto>> GetServicesAsync(string? search = null, int? categoryId = null, string? status = null, bool? isFeatured = null, string? language = "en", int page = 1, int pageSize = 12, string? sortBy = "createdAt", string? sortOrder = "desc")
        {
            throw new NotImplementedException();
        }

        public Task<ServiceDetailDto?> GetServiceBySlugAsync(string slug, string language = "en")
        {
            throw new NotImplementedException();
        }

        public Task<ServiceDetailDto?> GetServiceByIdAsync(int id, string language = "en")
        {
            throw new NotImplementedException();
        }

        public Task<List<ServiceDto>> GetRelatedServicesAsync(int id, string language = "en", int limit = 3)
        {
            throw new NotImplementedException();
        }

        public Task IncrementViewCountAsync(int id)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(int id, CreateServiceInquiryDto inquiryDto, string? clientIp, string? userAgent)
        {
            throw new NotImplementedException();
        }

        public Task<List<CategoryDto>> GetCategoriesAsync(string language = "en")
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<ServiceDto>> CreateServiceAsync(CreateServiceDto serviceDto)
        {
            throw new NotImplementedException();
        }

        public Task<ServiceResult<ServiceDto>> UpdateServiceAsync(int id, UpdateServiceDto serviceDto)
        {
            throw new NotImplementedException();
        }
    }
}