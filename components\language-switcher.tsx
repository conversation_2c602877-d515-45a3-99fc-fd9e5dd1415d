"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Globe, ChevronDown } from "lucide-react"
import { useTranslation } from "./translation-provider"

export function LanguageSwitcher() {
  const { language, setLanguage, isRTL, isLoading } = useTranslation()

  const languages = [
    { code: "en", name: "English", nativeName: "English", flag: "🇺🇸" },
    { code: "ar", name: "Arabic", nativeName: "العربية", flag: "🇦🇪" },
  ]

  const currentLanguage = languages.find((lang) => lang.code === language)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 px-3 text-gray-700 hover:bg-gray-100 transition-colors"
          disabled={isLoading}
        >
          <Globe className={`h-4 w-4 ${isRTL ? "ml-2" : "mr-2"}`} />
          <span className="hidden sm:inline">{currentLanguage?.nativeName}</span>
          <span className="sm:hidden">{currentLanguage?.flag}</span>
          <ChevronDown className={`h-3 w-3 ${isRTL ? "mr-1" : "ml-1"} opacity-50`} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isRTL ? "start" : "end"} className="w-40">
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => setLanguage(lang.code as "en" | "ar")}
            className={`flex items-center justify-between cursor-pointer ${
              language === lang.code ? "bg-orange-50 text-orange-600" : ""
            } ${isRTL ? "flex-row-reverse" : ""}`}
            disabled={isLoading}
          >
            <span className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
              <span className={`${isRTL ? "ml-2" : "mr-2"}`}>{lang.flag}</span>
              {lang.nativeName}
            </span>
            {language === lang.code && <span className="text-orange-600">✓</span>}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
