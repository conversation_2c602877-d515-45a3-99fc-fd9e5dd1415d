"use client"

import type React from "react"
import { Inter, Cairo } from "next/font/google"
import "./globals.css"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Toaster } from "@/components/ui/toaster"
import { TranslationProvider } from "@/components/translation-provider"
import { WhatsAppFloat } from "@/components/whatsapp-float"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  preload: true,
  variable: "--font-inter",
})

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  display: "swap",
  preload: true,
  variable: "--font-cairo",
})

// JSON-LD Structured Data
const jsonLd = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "DRYLEX - New Generation Solutions",
      alternateName: "DRYLEX",
      url: "https://drylexIraq.com",
      logo: "https://drylexIraq.com/logo.png",
  contactPoint: {
    "@type": "ContactPoint",
    telephone: "+971-50-123-4567",
    contactType: "customer service",
    areaServed: "AE",
    availableLanguage: ["en", "ar"],
  },
  address: {
    "@type": "PostalAddress",
    streetAddress: "Industrial Area 1",
    addressLocality: "Sharjah",
    addressCountry: "AE",
  },
  geo: {
    "@type": "GeoCoordinates",
    latitude: 25.3548,
    longitude: 55.4105,
  },
  openingHoursSpecification: [
    {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday"],
      opens: "08:00",
      closes: "18:00",
    },
    {
      "@type": "OpeningHoursSpecification",
      dayOfWeek: "Friday",
      opens: "08:00",
      closes: "12:00",
    },
  ],
  sameAs: [
    "https://www.facebook.com/drylex",
        "https://www.linkedin.com/company/drylex",
        "https://www.instagram.com/drylex",
  ],
}

export default function ClientRootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <meta name="theme-color" content="#ea580c" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="DRYLEX" />
      </head>
      <body className={`${inter.variable} ${cairo.variable} font-sans`}>
        <style jsx global>{`
          :root {
            --font-inter: ${inter.style.fontFamily};
            --font-cairo: ${cairo.style.fontFamily};
          }
          
          body.ltr {
            font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          }
          
          body.rtl {
            font-family: var(--font-cairo), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          }
          
          body.rtl h1,
          body.rtl h2,
          body.rtl h3,
          body.rtl h4,
          body.rtl h5,
          body.rtl h6 {
            font-family: var(--font-cairo), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
          }
        `}</style>
        <TranslationProvider>
          <Header />
          <main id="main-content">{children}</main>
          <Footer />
          <WhatsAppFloat />
          <Toaster />
        </TranslationProvider>
      </body>
    </html>
  )
}
