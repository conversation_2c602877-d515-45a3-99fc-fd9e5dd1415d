using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ConstructionWebsite.API.Services;

using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ProductsController : ControllerBase
    {
        private readonly IProductService _productService;
        private readonly ILogger<ProductsController> _logger;

        public ProductsController(IProductService productService, ILogger<ProductsController> logger)
        {
            _productService = productService;
            _logger = logger;
        }

        /// <summary>
        /// Get all products with optional filtering and pagination
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<PagedResult<ProductDto>>> GetProducts(
            [FromQuery] string? search = null,
            [FromQuery] int? categoryId = null,
            [FromQuery] string? status = null,
            [FromQuery] bool? isFeatured = null,
            [FromQuery] string? language = "en",
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 12,
            [FromQuery] string? sortBy = "createdAt",
            [FromQuery] string? sortOrder = "desc")
        {
            try
            {
                var result = await _productService.GetProductsAsync(
                    search, categoryId, status, isFeatured, language, 
                    page, pageSize, sortBy, sortOrder);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving products");
                return StatusCode(500, new { message = "An error occurred while retrieving products" });
            }
        }

        /// <summary>
        /// Get product by slug
        /// </summary>
        [HttpGet("slug/{slug}")]
        public async Task<ActionResult<ProductDetailDto>> GetProductBySlug(
            string slug, 
            [FromQuery] string language = "en")
        {
            try
            {
                var product = await _productService.GetProductBySlugAsync(slug, language);
                if (product == null)
                {
                    return NotFound(new { message = "Product not found" });
                }

                // Increment view count
                await _productService.IncrementViewCountAsync(product.Id);

                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product with slug: {Slug}", slug);
                return StatusCode(500, new { message = "An error occurred while retrieving the product" });
            }
        }

        /// <summary>
        /// Get product by ID
        /// </summary>
        [HttpGet("{id:int}")]
        public async Task<ActionResult<ProductDetailDto>> GetProduct(
            int id, 
            [FromQuery] string language = "en")
        {
            try
            {
                var product = await _productService.GetProductByIdAsync(id, language);
                if (product == null)
                {
                    return NotFound(new { message = "Product not found" });
                }

                return Ok(product);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the product" });
            }
        }

        /// <summary>
        /// Get related products
        /// </summary>
        [HttpGet("{id:int}/related")]
        public async Task<ActionResult<List<ProductDto>>> GetRelatedProducts(
            int id,
            [FromQuery] string language = "en",
            [FromQuery] int limit = 4)
        {
            try
            {
                var relatedProducts = await _productService.GetRelatedProductsAsync(id, language, limit);
                return Ok(relatedProducts);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving related products for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving related products" });
            }
        }

        /// <summary>
        /// Create product inquiry
        /// </summary>
        [HttpPost("{id:int}/inquiries")]
        public async Task<ActionResult<InquiryResponseDto>> CreateProductInquiry(
            int id, 
            [FromBody] CreateProductInquiryDto inquiryDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var clientIp = HttpContext.Connection.RemoteIpAddress?.ToString();
                var userAgent = Request.Headers["User-Agent"].ToString();

                var result = await _productService.CreateInquiryAsync(
                    id, inquiryDto, clientIp, userAgent);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product inquiry for ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while creating the inquiry" });
            }
        }

        /// <summary>
        /// Add product to wishlist
        /// </summary>
        [HttpPost("{id:int}/wishlist")]
        public async Task<ActionResult> AddToWishlist(int id)
        {
            try
            {
                var sessionId = HttpContext.Session.Id;
                var userId = User.Identity?.IsAuthenticated == true ? User.Identity.Name : null;

                var result = await _productService.AddToWishlistAsync(id, sessionId, userId);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(new { message = "Product added to wishlist" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding product to wishlist: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while adding to wishlist" });
            }
        }

        /// <summary>
        /// Remove product from wishlist
        /// </summary>
        [HttpDelete("{id:int}/wishlist")]
        public async Task<ActionResult> RemoveFromWishlist(int id)
        {
            try
            {
                var sessionId = HttpContext.Session.Id;
                var userId = User.Identity?.IsAuthenticated == true ? User.Identity.Name : null;

                var result = await _productService.RemoveFromWishlistAsync(id, sessionId, userId);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(new { message = "Product removed from wishlist" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing product from wishlist: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while removing from wishlist" });
            }
        }

        /// <summary>
        /// Check if product is in wishlist
        /// </summary>
        [HttpGet("{id:int}/wishlist/status")]
        public async Task<ActionResult<WishlistStatusDto>> GetWishlistStatus(int id)
        {
            try
            {
                var sessionId = HttpContext.Session.Id;
                var userId = User.Identity?.IsAuthenticated == true ? User.Identity.Name : null;

                var isInWishlist = await _productService.IsInWishlistAsync(id, sessionId, userId);
                return Ok(new WishlistStatusDto { IsInWishlist = isInWishlist });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking wishlist status for product: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while checking wishlist status" });
            }
        }

        /// <summary>
        /// Get product categories
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<List<CategoryDto>>> GetCategories(
            [FromQuery] string language = "en")
        {
            try
            {
                var categories = await _productService.GetCategoriesAsync(language);
                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving product categories");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        /// <summary>
        /// Create a new product (Admin only)
        /// </summary>
        [HttpPost]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ProductDto>> CreateProduct([FromBody] CreateProductDto productDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _productService.CreateProductAsync(productDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return CreatedAtAction(nameof(GetProduct), new { id = result.Data.Id }, result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating product");
                return StatusCode(500, new { message = "An error occurred while creating the product" });
            }
        }

        /// <summary>
        /// Update a product (Admin only)
        /// </summary>
        [HttpPut("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ProductDto>> UpdateProduct(int id, [FromBody] UpdateProductDto productDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _productService.UpdateProductAsync(id, productDto);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating product with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while updating the product" });
            }
        }

        /// <summary>
        /// Delete a product (Admin only)
        /// </summary>
        [HttpDelete("{id:int}")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult> DeleteProduct(int id)
        {
            try
            {
                var result = await _productService.DeleteProductAsync(id);
                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting product with ID: {Id}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the product" });
            }
        }
    }
}
