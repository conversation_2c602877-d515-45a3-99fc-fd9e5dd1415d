"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, X, Phone, Mail, MapPin } from "lucide-react"
import { LanguageSwitcher } from "./language-switcher"
import { useTranslation } from "./translation-provider"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { t, isRTL } = useTranslation()

  const navigation = [
    { name: t("nav.home"), href: "/" },
    { name: t("nav.about"), href: "/about" },
    { name: t("nav.products"), href: "/products" },
    { name: t("nav.projects"), href: "/projects" },
    { name: t("nav.services"), href: "/services" },
    { name: t("nav.contact"), href: "/contact" },
  ]

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-primary text-white py-2">
        <div className="container mx-auto px-4">
          <div className={`flex items-center justify-between text-sm ${isRTL ? "flex-row-reverse" : ""}`}>
            <div className={`flex items-center space-x-4 ${isRTL ? "space-x-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "space-x-reverse" : "space-x-2"}`}>
                <Phone className="w-4 h-4" />
                <span>+9647867100886</span>
              </div>
              <div className={`flex items-center ${isRTL ? "space-x-reverse" : "space-x-2"}`}>
                <Mail className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
            </div>
            <div className={`flex items-center ${isRTL ? "space-x-reverse" : "space-x-2"}`}>
              <MapPin className="w-4 h-4" />
              <span>{t("header.location")}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="container mx-auto px-4">
        <div className={`flex items-center justify-between h-16 ${isRTL ? "flex-row-reverse" : ""}`}>
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/images/drylex-logo.svg"
              alt="DRYLEX"
              width={120}
              height={40}
              className="h-10 w-auto"
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <div className={`flex items-center ${isRTL ? "space-x-reverse" : "space-x-8"}`}>
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-700 hover:text-primary font-medium transition-colors duration-200"
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </nav>

          {/* Language Switcher & Mobile Menu Button */}
          <div className={`flex items-center ${isRTL ? "space-x-reverse" : "space-x-4"}`}>
            <LanguageSwitcher />

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-100 transition-colors"
              aria-label={t("nav.toggleMenu")}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200">
            <nav className="py-4 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-4 py-2 text-gray-700 hover:text-primary hover:bg-gray-50 transition-colors ${
                    isRTL ? "text-right" : "text-left"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
