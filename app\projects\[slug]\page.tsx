import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getProjectBySlug, getAllProjectSlugs } from "@/lib/database-enhanced"
import { ProjectDetailClient } from "./project-detail-client"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"

interface ProjectPageProps {
  params: {
    slug: string
  }
  searchParams: { [key: string]: string | string[] | undefined }
}

// Static metadata removed - using generateMetadata instead

export async function generateStaticParams() {
  try {
    const slugs = await getAllProjectSlugs()
    return slugs.map((item) => ({
      slug: item.slug,
    }))
  } catch (error) {
    console.error("Error generating static params:", error)
    return []
  }
}

export async function generateMetadata({ params }: ProjectPageProps): Promise<Metadata> {
  try {
    const project = await getProjectBySlug(params.slug)

    if (!project) {
      return {
        title: "Project Not Found",
        description: "The requested project could not be found.",
      }
    }

    const title = project.seo_title || `${project.name} | DRYLEX  IRAQ`
    const description =
      project.seo_description || project.short_description || `Learn more about our ${project.name} project`
    const keywords = project.seo_keywords || `${project.name}, construction project, IRAQ`

    const images = project.images?.filter((img) => img.is_primary) || []
    const primaryImage = images[0]?.image_url

    return {
      title,
      description,
      keywords,
      authors: [{ name: "DRYLEX  IRAQ" }],
      creator: "DRYLEX  IRAQ",
      publisher: "DRYLEX  IRAQ",
      formatDetection: {
        email: false,
        address: false,
        telephone: false,
      },
      metadataBase: new URL("https://drylexiraq.com"),
      alternates: {
        canonical: `/projects/${params.slug}`,
        languages: {
          "en-US": `/projects/${params.slug}`,
          "ar-AE": `/ar/projects/${params.slug}`,
        },
      },
      openGraph: {
        title,
        description,
        url: `/projects/${params.slug}`,
        siteName: "DRYLEX  IRAQ",
        images: primaryImage
          ? [
              {
                url: primaryImage,
                width: 1200,
                height: 630,
                alt: project.name,
              },
            ]
          : [],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: primaryImage ? [primaryImage] : [],
        creator: "@DRYLEX IRAQ",
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "Project | DRYLEX  IRAQ",
      description: "Discover our construction projects and case studies.",
    }
  }
}


export default async function ProjectDetailPage({ params }: { params: { slug: string } }) {
  try {
    const project = await getProjectBySlug(params.slug)

    if (!project) {
      notFound()
    }

    // Generate enhanced JSON-LD structured data
    const caseStudyJsonLd = {
      "@context": "https://schema.org",
      "@type": "CaseStudy",
      name: project.name,
      description: project.seo_description || project.short_description,
      provider: {
        "@type": "Organization",
        name: "DRYLEX IRAQ",
        url: "https://drylexiraq.com",
        logo: "https://drylexiraq.com/logo.png",
      },
      category: `Construction Projects > ${project.category?.name || 'Uncategorized'}`,
      image: project.featured_image || "https://drylexiraq.com/images/projects/construction-project.jpg",
      datePublished: project.date,
      author: {
        "@type": "Organization",
        name: "DRYLEX IRAQ",
      },
      about: project.description,
      industry: "Construction",
      workPerformed: project.services_used?.map((service: any) => service.name) || [],
      result: project.results_summary,
      review: project.reviews?.map((review: any) => ({
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: review.rating,
          bestRating: "5",
        },
        author: review.author,
        datePublished: review.date,
        name: review.title,
        reviewBody: review.text,
      })) || [],
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: project.rating?.average || "4.9",
        reviewCount: project.rating?.review_count || "45",
      },
      offers: {
        "@type": "OfferCatalog",
        name: "Construction Projects Catalog",
        url: "https://drylexiraq.com/projects",
        numberOfItems: "20"
      }
    }

    const breadcrumbItems = [
      { name: "Home", href: "/" },
      { name: "Projects", href: "/projects" },
      ...(project.category
        ? [{ name: project.category.name, href: `/projects?category=${project.category.slug}` }]
        : []),
      { name: project.name, href: `/projects/${project.slug}` },
    ]

    return (
      <>
        <script 
          type="application/ld+json" 
          dangerouslySetInnerHTML={{ 
            __html: JSON.stringify(caseStudyJsonLd, null, 2) 
          }} 
        />
        
        <div className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-6">
            <SEOBreadcrumbs items={breadcrumbItems} />
            <ProjectDetailClient project={project} />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error("Error loading project page:", error)
    notFound()
  }
}
