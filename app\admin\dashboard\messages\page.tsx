"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Eye, Trash2, Download, Reply, Mail, Search, Filter } from "lucide-react"
import { AuthGuard } from "@/components/auth-guard"
import { contactApi, ContactDto } from "@/lib/api"
import { db } from "@/lib/database"
import type { ContactMessage } from "@/lib/database"

// JSON-LD for Admin Messages Page
const adminMessagesJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Admin Messages - DRYLEX Iraq",
  description: "Admin dashboard for viewing and managing messages from DRYLEX Iraq website. View customer inquiries and contact form submissions.",
  url: "https://drylexiraq.com/admin/dashboard/messages",
  mainContentOfPage: {
    "@type": "WebApplication",
    name: "DRYLEX Iraq Admin Messages",
    description: "Web application for managing messages and customer inquiries for DRYLEX Iraq website",
    operatingSystem: "Web",
    applicationCategory: "BusinessApplication",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      eligibleRegion: "IQ",
    },
  },
}

export default function MessagesPage() {
  // JSON-LD for WebPage
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Admin Messages - DRYLEX Iraq",
    "description": "Admin dashboard messages management for DRYLEX Iraq construction materials",
    "url": "https://drylexiraq.com/admin/dashboard/messages",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Admin",
          "item": "https://drylexiraq.com/admin"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Messages",
          "item": "https://drylexiraq.com/admin/dashboard/messages"
        }
      ]
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  const [messages, setMessages] = useState<ContactDto[]>([])
  const [filteredMessages, setFilteredMessages] = useState<ContactDto[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [useApi, setUseApi] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    loadMessages()
  }, [])

  useEffect(() => {
    filterMessages()
  }, [messages, searchTerm, statusFilter])

  const loadMessages = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      if (useApi) {
        // Try to load from API first
        const apiMessages = await contactApi.getContactMessages()
        setMessages(apiMessages)
      } else {
        // Fallback to local database
        loadFromLocalDatabase()
      }
    } catch (error) {
      console.error('Failed to load from API, falling back to local database:', error)
      setError('API connection failed, using local data')
      setUseApi(false)
      loadFromLocalDatabase()
    } finally {
      setIsLoading(false)
    }
  }

  const loadFromLocalDatabase = () => {
    try {
      const allMessages = db.getMessages()
      // Convert local data to API format
      const convertedMessages = allMessages.map(msg => ({
        ...msg,
        isRead: msg.status !== 'pending',
        isReplied: msg.status === 'replied',
        reply: undefined,
        repliedAt: undefined,
        repliedBy: undefined,
        inquiryType: undefined,
        preferredContactMethod: undefined,
        preferredContactTime: undefined
      }))
      setMessages(convertedMessages)
    } catch (error) {
      console.error('Error loading messages from local database:', error)
      toast({
        title: "Error",
        description: "Failed to load messages",
        variant: "destructive",
      })
    }
  }

  const filterMessages = () => {
    let filtered = messages

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(message =>
        message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
        message.message.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      if (statusFilter === 'unread') {
        filtered = filtered.filter(message => !message.isRead)
      } else if (statusFilter === 'read') {
        filtered = filtered.filter(message => message.isRead && !message.isReplied)
      } else if (statusFilter === 'replied') {
        filtered = filtered.filter(message => message.isReplied)
      }
    }

    setFilteredMessages(filtered)
  }

  const handleMarkAsRead = async (id: number) => {
    try {
      if (useApi) {
        await contactApi.markAsRead(id)
      } else {
        db.updateMessage(id, { status: "read" })
      }
      loadMessages()
      toast({
        title: "Success",
        description: "Message marked as read",
      })
    } catch (error) {
      console.error('Failed to mark message as read:', error)
      // Fallback to local database
      db.updateMessage(id, { status: "read" })
      loadMessages()
      toast({
        title: "Warning",
        description: "Marked as read locally (API unavailable)",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: number) => {
    if (confirm("Are you sure you want to delete this message?")) {
      try {
        if (useApi) {
          // This would need a delete endpoint in the API
          console.log('Delete functionality would need API endpoint')
          toast({
            title: "Info",
            description: "Delete via API not implemented yet",
          })
        } else {
          db.deleteMessage(id)
          loadMessages()
          toast({
            title: "Success",
            description: "Message deleted successfully",
          })
        }
      } catch (error) {
        console.error('Failed to delete message:', error)
        toast({
          title: "Error",
          description: "Failed to delete message",
          variant: "destructive",
        })
      }
    }
  }

  const handleReply = (message: ContactDto) => {
    const subject = `Re: ${message.subject}`
    const body = `Dear ${message.name},\n\nThank you for contacting DRYLEX  Materials & Services.\n\n---\nOriginal Message:\n${message.message}`
    const mailtoUrl = `mailto:${message.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoUrl)

    // Mark as replied (API endpoint would be needed for this)
    if (!useApi) {
      db.updateMessage(message.id, { status: "replied" })
      loadMessages()
    }
  }

  const exportToCSV = () => {
    const csvContent = [
      ["Name", "Email", "Phone", "Subject", "Message", "Status", "Date"],
      ...messages.map((m) => [
        m.name,
        m.email,
        m.phone || "",
        m.subject,
        m.message.replace(/\n/g, " "),
        m.status,
        new Date(m.createdAt).toLocaleDateString(),
      ]),
    ]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv" })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `messages-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  if (isLoading) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
              <div className="h-4 bg-gray-300 rounded w-1/2 mb-8"></div>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="bg-white rounded-lg p-6">
                    <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
                    <div className="h-3 bg-gray-300 rounded w-1/3 mb-4"></div>
                    <div className="h-3 bg-gray-300 rounded w-full"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </AuthGuard>
    )
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(adminMessagesJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Error Banner */}
          {error && (
            <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800 text-sm">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                className="mt-2"
                onClick={() => {
                  setUseApi(true)
                  loadMessages()
                }}
              >
                Retry API Connection
              </Button>
            </div>
          )}
          
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Contact Messages</h1>
              <p className="text-gray-600">View and manage contact form submissions {useApi ? '(API)' : '(Local)'}</p>
            </div>
            <Button onClick={exportToCSV} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>

          {/* Search and Filter */}
          <div className="mb-6 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search messages..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Messages</SelectItem>
                <SelectItem value="unread">Unread</SelectItem>
                <SelectItem value="read">Read</SelectItem>
                <SelectItem value="replied">Replied</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Messages</p>
                    <p className="text-2xl font-bold">{messages.length}</p>
                  </div>
                  <Mail className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Unread</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {messages.filter((m) => !m.isRead).length}
                    </p>
                  </div>
                  <Eye className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Replied</p>
                    <p className="text-2xl font-bold text-green-600">
                      {messages.filter((m) => m.isReplied).length}
                    </p>
                  </div>
                  <Reply className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Messages List */}
          <div className="space-y-4">
            {isLoading ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading messages...</p>
                </CardContent>
              </Card>
            ) : filteredMessages.map((message) => (
              <Card key={message.id} className={!message.isRead ? "border-orange-200 bg-orange-50/30" : ""}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div>
                        <h3 className="font-semibold text-lg">{message.name}</h3>
                        <p className="text-sm text-gray-600">{message.email}</p>
                        {message.phone && <p className="text-sm text-gray-600">{message.phone}</p>}
                      </div>
                      <Badge
                        variant={
                          !message.isRead
                            ? "default"
                            : message.isReplied
                              ? "secondary"
                              : "outline"
                        }
                        className={
                          !message.isRead
                            ? "bg-orange-600"
                            : message.isReplied
                              ? "bg-green-600"
                              : ""
                        }
                      >
                        {!message.isRead ? "unread" : message.isReplied ? "replied" : "read"}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(message.createdAt).toLocaleDateString()} at{" "}
                      {new Date(message.createdAt).toLocaleTimeString()}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Subject: {message.subject}</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-700 whitespace-pre-wrap">{message.message}</p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {!message.isRead && (
                      <Button size="sm" variant="outline" onClick={() => handleMarkAsRead(message.id)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Mark as Read
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleReply(message)}
                      className="bg-blue-50 hover:bg-blue-100"
                    >
                      <Reply className="h-4 w-4 mr-2" />
                      Reply
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(message.id)}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}

            {!isLoading && filteredMessages.length === 0 && messages.length > 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No messages found</h3>
                  <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
                </CardContent>
              </Card>
            )}

            {!isLoading && messages.length === 0 && (
              <Card>
                <CardContent className="p-12 text-center">
                  <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                  <p className="text-gray-600">When customers contact you, their messages will appear here.</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </AuthGuard>
    </>
  )
}
