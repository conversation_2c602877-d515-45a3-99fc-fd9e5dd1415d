namespace ConstructionWebsite.API.DTOs
{
    public class ImageDto
    {
        public int Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileUrl { get; set; } = string.Empty;
        public string? AltText { get; set; }
        public string? AltTextAr { get; set; }
        public string? Caption { get; set; }
        public string? CaptionAr { get; set; }
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public int? Width { get; set; }
        public int? Height { get; set; }
        public bool IsPrimary { get; set; }
        public int SortOrder { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public int EntityId { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateImageDto
    {
        public string FileName { get; set; } = string.Empty;
        public string OriginalFileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string? AltText { get; set; }
        public string? AltTextAr { get; set; }
        public string? Caption { get; set; }
        public string? CaptionAr { get; set; }
        public long FileSize { get; set; }
        public string MimeType { get; set; } = string.Empty;
        public int? Width { get; set; }
        public int? Height { get; set; }
        public bool IsPrimary { get; set; }
        public int SortOrder { get; set; }
        public string EntityType { get; set; } = string.Empty;
        public int EntityId { get; set; }
    }

    public class UpdateImageDto
    {
        public string? AltText { get; set; }
        public string? AltTextAr { get; set; }
        public string? Caption { get; set; }
        public string? CaptionAr { get; set; }
        public bool? IsPrimary { get; set; }
        public int? SortOrder { get; set; }
    }
}