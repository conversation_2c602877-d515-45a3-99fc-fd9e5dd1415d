namespace ConstructionWebsite.API.Services
{
    public interface IFileUploadService
    {
        Task<string> UploadFileAsync(IFormFile file, string folder = "uploads");
        Task<IEnumerable<string>> UploadMultipleFilesAsync(IEnumerable<IFormFile> files, string folder = "uploads");
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> FileExistsAsync(string filePath);
        string GetFileUrl(string filePath);
        bool IsValidFileType(IFormFile file, string[] allowedExtensions);
        bool IsValidFileSize(IFormFile file, long maxSizeInBytes);
        Task<byte[]> GetFileContentAsync(string filePath);
    }
}