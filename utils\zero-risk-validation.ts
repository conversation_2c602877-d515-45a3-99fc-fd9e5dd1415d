// Zero-risk input validation and sanitization

import crypto from 'crypto'

export interface ValidationResult {
  isValid: boolean
  sanitizedValue: string
  errors: string[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

export interface SecurityThreat {
  type: 'xss' | 'sql_injection' | 'path_traversal' | 'command_injection' | 'ldap_injection'
  pattern: RegExp
  severity: 'low' | 'medium' | 'high' | 'critical'
}

class ZeroRiskValidator {
  private threats: SecurityThreat[] = [
    // XSS patterns
    { type: 'xss', pattern: /<script[^>]*>.*?<\/script>/gi, severity: 'critical' },
    { type: 'xss', pattern: /javascript:/gi, severity: 'critical' },
    { type: 'xss', pattern: /on\w+\s*=/gi, severity: 'high' },
    { type: 'xss', pattern: /<iframe[^>]*>/gi, severity: 'high' },
    { type: 'xss', pattern: /<object[^>]*>/gi, severity: 'high' },
    { type: 'xss', pattern: /<embed[^>]*>/gi, severity: 'high' },
    
    // SQL injection patterns
    { type: 'sql_injection', pattern: /('|(\\')|(;)|(--)|(\|)|(\*)|(%)|(_))/gi, severity: 'critical' },
    { type: 'sql_injection', pattern: /(union|select|insert|update|delete|drop|create|alter|exec|execute)/gi, severity: 'critical' },
    
    // Path traversal patterns
    { type: 'path_traversal', pattern: /\.\.\//g, severity: 'high' },
    { type: 'path_traversal', pattern: /\.\.\\/g, severity: 'high' },
    
    // Command injection patterns
    { type: 'command_injection', pattern: /[;&|`$()]/g, severity: 'high' },
    { type: 'command_injection', pattern: /(rm|del|format|shutdown|reboot)/gi, severity: 'critical' }
  ]

  // Validate and sanitize any input
  validateInput(value: any, type: string, maxLength: number = 1000): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      sanitizedValue: '',
      errors: [],
      riskLevel: 'low'
    }

    if (value === null || value === undefined) {
      result.sanitizedValue = ''
      return result
    }

    let stringValue = String(value).trim()
    
    // Check length
    if (stringValue.length > maxLength) {
      result.errors.push(`Input exceeds maximum length of ${maxLength} characters`)
      result.riskLevel = 'medium'
    }

    // Check for threats
    for (const threat of this.threats) {
      if (threat.pattern.test(stringValue)) {
        result.errors.push(`Potential ${threat.type} attack detected`)
        result.riskLevel = threat.severity
        result.isValid = false
      }
    }

    // Sanitize based on type
    switch (type) {
      case 'email':
        result.sanitizedValue = this.sanitizeEmail(stringValue)
        break
      case 'phone':
        result.sanitizedValue = this.sanitizePhone(stringValue)
        break
      case 'url':
        result.sanitizedValue = this.sanitizeURL(stringValue)
        break
      case 'filename':
        result.sanitizedValue = this.sanitizeFileName(stringValue)
        break
      default:
        result.sanitizedValue = this.sanitizeText(stringValue)
    }

    return result
  }

  // Sanitize email
  private sanitizeEmail(email: string): string {
    return email
      .toLowerCase()
      .replace(/[^a-z0-9@._-]/g, '')
      .slice(0, 254)
  }

  // Sanitize phone
  private sanitizePhone(phone: string): string {
    return phone.replace(/[^\d+\-\(\)\s]/g, '').slice(0, 20)
  }

  // Sanitize URL
  private sanitizeURL(url: string): string {
    const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'onload:', 'onerror:']
    
    for (const protocol of dangerousProtocols) {
      if (url.toLowerCase().startsWith(protocol)) {
        return ''
      }
    }
    
    return url.slice(0, 2048)
  }

  // Sanitize filename
  private sanitizeFileName(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/\.\./g, '')
      .replace(/^\.+/, '')
      .slice(0, 255)
  }

  // Sanitize text
  private sanitizeText(text: string): string {
    return text
      .replace(/[<>]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/script/gi, '')
      .replace(/iframe/gi, '')
      .replace(/object/gi, '')
      .replace(/embed/gi, '')
      .replace(/form/gi, '')
      .trim()
  }

  // Generate secure hash for sensitive data
  generateSecureHash(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex')
  }

  // Validate file upload
  validateFileUpload(file: File): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      sanitizedValue: '',
      errors: [],
      riskLevel: 'low'
    }

    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      result.errors.push('File size exceeds 10MB limit')
      result.riskLevel = 'high'
      result.isValid = false
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      result.errors.push('File type not allowed')
      result.riskLevel = 'high'
      result.isValid = false
    }

    // Sanitize filename
    const sanitizedFilename = this.sanitizeFileName(file.name)
    result.sanitizedValue = sanitizedFilename

    return result
  }
}

export const zeroRiskValidator = new ZeroRiskValidator()
