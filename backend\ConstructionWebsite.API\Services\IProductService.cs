using ConstructionWebsite.API.DTOs;
using ConstructionWebsite.API.Models;

namespace ConstructionWebsite.API.Services
{
    public interface IProductService
    {
        Task<PagedResult<ProductDto>> GetProductsAsync(
            string? search, int? categoryId, string? status, bool? isFeatured, 
            string language, int page, int pageSize, string? sortBy, string? sortOrder);
        
        Task<ProductDetailDto?> GetProductByIdAsync(int id, string language);
        Task<ProductDetailDto?> GetProductBySlugAsync(string slug, string language);
        Task<List<ProductDto>> GetRelatedProductsAsync(int productId, string language, int limit);
        Task<List<CategoryDto>> GetCategoriesAsync(string language);
        
        Task<ServiceResult<ProductDto>> CreateProductAsync(CreateProductDto productDto);
        Task<ServiceResult<ProductDto>> UpdateProductAsync(int id, UpdateProductDto productDto);
        Task<ServiceResult<bool>> DeleteProductAsync(int id);
        
        Task<ServiceResult<InquiryResponseDto>> CreateInquiryAsync(
            int productId, CreateProductInquiryDto inquiryDto, string? clientIp, string? userAgent);
        
        Task<ServiceResult<bool>> AddToWishlistAsync(int productId, string sessionId, string? userId);
        Task<ServiceResult<bool>> RemoveFromWishlistAsync(int productId, string sessionId, string? userId);
        Task<bool> IsInWishlistAsync(int productId, string sessionId, string? userId);
        
        Task IncrementViewCountAsync(int productId);
    }
}
