import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { getServiceBySlug, getAllServiceSlugs } from "@/lib/database-enhanced"
import { ServiceDetailClient } from "./service-detail-client"
import { SEOBreadcrumbs } from "@/components/seo-breadcrumbs"

interface ServicePageProps {
  params: {
    slug: string
  }
  searchParams: { [key: string]: string | string[] | undefined }
}

// Static metadata removed - using generateMetadata instead

export async function generateStaticParams() {
  try {
    const slugs = await getAllServiceSlugs()
    return slugs.map((item) => ({
      slug: item.slug,
    }))
  } catch (error) {
    console.error("Error generating static params:", error)
    return []
  }
}

export async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {
  try {
    const service = await getServiceBySlug(params.slug)

    if (!service) {
      return {
        title: "Service Not Found",
        description: "The requested service could not be found.",
      }
    }

    const title = service.seo_title || `${service.name} | DRYLEX  IRAQ`
    const description =
      service.seo_description || service.short_description || `Learn more about our ${service.name} service`
    const keywords = service.seo_keywords || `${service.name}, construction services, IRAQ`

    const images = service.images?.filter((img) => img.is_primary) || []
    const primaryImage = images[0]?.image_url

    return {
      title,
      description,
      keywords,
      authors: [{ name: "DRYLEX  IRAQ" }],
      creator: "DRYLEX  IRAQ",
      publisher: "DRYLEX  IRAQ",
      formatDetection: {
        email: false,
        address: false,
        telephone: false,
      },
      metadataBase: new URL("https://drylexiraq.com"),
      alternates: {
        canonical: `/services/${params.slug}`,
        languages: {
          "en-US": `/services/${params.slug}`,
          "ar-AE": `/ar/services/${params.slug}`,
        },
      },
      openGraph: {
        title,
        description,
        url: `/services/${params.slug}`,
        siteName: "DRYLEX  IRAQ",
        images: primaryImage
          ? [
              {
                url: primaryImage,
                width: 1200,
                height: 630,
                alt: service.name,
              },
            ]
          : [],
        locale: "en_US",
        type: "website",
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: primaryImage ? [primaryImage] : [],
        creator: "@DRYLEX IRAQ",
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          "max-video-preview": -1,
          "max-image-preview": "large",
          "max-snippet": -1,
        },
      },
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "Service | DRYLEX  IRAQ",
      description: "Discover our construction services and solutions.",
    }
  }
}

// JSON-LD for Service
const serviceJsonLd = {
  "@context": "https://schema.org",
  "@type": "Service",
  name: "Waterproofing Solutions",
  description: "Premium waterproofing membranes and liquid systems for Iraqi construction projects",
  provider: {
    "@type": "Organization",
    name: "DRYLEX IRAQ",
    url: "https://drylexiraq.com",
    logo: "https://drylexiraq.com/logo.png",
  },
  category: "Construction Services > Waterproofing",
  serviceArea: {
    "@type": "Country",
    name: "Iraq",
  },
  offers: {
    "@type": "OfferCatalog",
    name: "Waterproofing Services Catalog",
    url: "https://drylexiraq.com/services/waterproofing",
    numberOfItems: "5",
  },
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: "4.8",
    reviewCount: "65",
  },
  review: [
    {
      "@type": "Review",
      reviewRating: {
        "@type": "Rating",
        ratingValue: "5",
        bestRating: "5",
      },
      author: "Ali Al-Maliki",
      datePublished: "2024-08-15",
      name: "Excellent waterproofing solution",
      reviewBody: "Used Drylex waterproofing system for a basement project in Baghdad. No leaks after 6 months, even during heavy rains. Highly recommended!",
    },
    {
      "@type": "Review",
      reviewRating: {
        "@type": "Rating",
        ratingValue: "5",
        bestRating: "5",
      },
      author: "Ahmed Al-Sudani",
      datePublished: "2024-07-20",
      name: "Superior quality materials",
      reviewBody: "Drylex construction chemicals have exceeded our expectations in several infrastructure projects. Durable and reliable solutions.",
    },
  ],
}

export default async function ServiceDetailPage({ params }: { params: { slug: string } }) {
  try {
    const service = await getServiceBySlug(params.slug)

    if (!service) {
      notFound()
    }

    // JSON-LD for Service
    const serviceJsonLd = {
      "@context": "https://schema.org",
      "@type": "Service",
      name: service.name,
      description: service.description || service.short_description || `Learn more about our ${service.name} service`,
      provider: {
        "@type": "Organization",
        name: "DRYLEX IRAQ",
        url: "https://drylexiraq.com",
        logo: "https://drylexiraq.com/logo.png",
      },
      category: `Construction Services > ${service.category?.name || 'General'}`,
      image: service.images?.find((img) => img.is_primary)?.image_url || "https://drylexiraq.com/images/services/construction-service.jpg",
      offers: {
        "@type": "OfferCatalog",
        name: `${service.name} Catalog`,
        url: `https://drylexiraq.com/services/${service.slug}`,
        numberOfItems: "1",
      },
      serviceType: service.type || "Construction Service",
      areaServed: {
        "@type": "Country",
        name: "Iraq",
      },
      availableChannel: {
        "@type": "ServiceChannel",
        name: "Contact Us",
        providesService: {
          "@type": "Service",
          name: "Free Consultation",
          description: "Get free consultation for our construction services",
          url: "https://drylexiraq.com/contact"
        }
      },
      review: service.reviews?.map((review) => ({
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: review.rating,
          bestRating: "5",
        },
        author: review.author,
        datePublished: review.date,
        name: review.title,
        reviewBody: review.text,
      })) || [],
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: service.rating?.average || "4.8",
        reviewCount: service.rating?.reviews?.length || "65",
      },
      hasOfferCatalog: {
        "@type": "OfferCatalog",
        name: "Construction Services Catalog",
        url: "https://drylexiraq.com/services",
        numberOfItems: "10"
      }
    }

    const breadcrumbItems = [
      { name: "Home", href: "/" },
      { name: "Services", href: "/services" },
      ...(service.category
        ? [{ name: service.category.name, href: `/services?category=${service.category.slug}` }]
        : []),
      { name: service.name, href: `/services/${service.slug}` },
    ]

    return (
      <>
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(serviceJsonLd) }} />

        <div className="min-h-screen bg-gray-50">
          <div className="container mx-auto px-4 py-6">
            <SEOBreadcrumbs items={breadcrumbItems} />
            <ServiceDetailClient service={service} />
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error("Error loading service page:", error)
    notFound()
  }
}
