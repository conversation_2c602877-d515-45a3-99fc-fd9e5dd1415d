// Enhanced database functions with view count tracking and language support

export interface Product {
  id: string
  name: string
  name_ar?: string
  slug: string
  short_description: string
  short_description_ar?: string
  description?: string
  description_ar?: string
  price?: number
  original_price?: number
  currency: string
  unit?: string
  sku?: string
  brand?: string
  is_new: boolean
  is_bestseller: boolean
  is_featured: boolean
  view_count: number
  features?: string[]
  features_ar?: string[]
  applications?: string[]
  applications_ar?: string[]
  technical_specs?: Record<string, any>
  seo_title?: string
  seo_description?: string
  seo_keywords?: string
  category?: {
    id: string
    name: string
    slug: string
  }
  images?: {
    id: string
    image_url: string
    alt_text?: string
    alt_text_ar?: string
    is_primary: boolean
  }[]
  created_at: string
  updated_at: string
}

export interface Service {
  id: string
  name: string
  name_ar?: string
  slug: string
  short_description: string
  short_description_ar?: string
  description?: string
  description_ar?: string
  base_price?: number
  currency: string
  unit?: string
  is_featured: boolean
  is_emergency: boolean
  view_count: number
  rating?: number
  completion_count?: number
  duration_estimate?: string
  duration_estimate_ar?: string
  service_area?: string
  features?: string[]
  features_ar?: string[]
  certifications?: string[]
  seo_title?: string
  seo_description?: string
  seo_keywords?: string
  category?: {
    id: string
    name: string
    slug: string
  }
  images?: {
    id: string
    image_url: string
    alt_text?: string
    alt_text_ar?: string
    is_primary: boolean
  }[]
  created_at: string
  updated_at: string
}

export interface Project {
  id: string
  name: string
  name_ar?: string
  slug: string
  short_description: string
  short_description_ar?: string
  description?: string
  description_ar?: string
  project_status: string
  is_featured: boolean
  view_count: number
  client_name?: string
  client_name_ar?: string
  location?: string
  location_ar?: string
  project_value?: number
  currency: string
  project_size?: string
  duration_months?: number
  team_size?: number
  start_date?: string
  completion_date?: string
  key_features?: string[]
  key_features_ar?: string[]
  awards_received?: string[]
  technical_specs?: Record<string, any>
  seo_title?: string
  seo_description?: string
  seo_keywords?: string
  category?: {
    id: string
    name: string
    slug: string
  }
  images?: {
    id: string
    image_url: string
    alt_text?: string
    alt_text_ar?: string
    is_primary: boolean
  }[]
  created_at: string
  updated_at: string
}

// Mock database functions - replace with actual database calls
const mockProducts: Product[] = [
  {
    id: "1",
    name: "Premium Steel Rebar",
    name_ar: "حديد التسليح الممتاز",
    slug: "premium-steel-rebar",
    short_description: "High-grade steel reinforcement bars for construction projects",
    short_description_ar: "قضبان التسليح الفولاذية عالية الجودة لمشاريع البناء",
    description:
      "Our premium steel rebar is manufactured to the highest standards, providing exceptional strength and durability for all types of construction projects. Made from high-quality steel with superior corrosion resistance.",
    description_ar:
      "يتم تصنيع حديد التسليح الممتاز وفقاً لأعلى المعايير، مما يوفر قوة ومتانة استثنائية لجميع أنواع مشاريع البناء.",
    price: 2500,
    currency: "AED",
    unit: "ton",
    sku: "RB-001",
    brand: "DRYLEX",
    is_new: true,
    is_bestseller: false,
    is_featured: true,
    view_count: 1250,
    features: ["High tensile strength", "Corrosion resistant", "Grade 60 steel", "Ribbed surface"],
    features_ar: ["قوة شد عالية", "مقاوم للتآكل", "فولاذ درجة 60", "سطح مضلع"],
    applications: ["Residential construction", "Commercial buildings", "Infrastructure projects"],
    applications_ar: ["البناء السكني", "المباني التجارية", "مشاريع البنية التحتية"],
    technical_specs: {
      Diameter: "8mm - 32mm",
      Length: "12m standard",
      Grade: "Grade 60",
      "Yield Strength": "420 MPa",
    },
    seo_title: "Premium Steel Rebar - High Quality Construction Materials | DRYLEX  IRAQ",
    seo_description:
      "Buy premium steel rebar for construction projects in IRAQ. High-grade reinforcement bars with superior strength and corrosion resistance.",
    seo_keywords: "steel rebar, construction materials, reinforcement bars, IRAQ, DRYLEX ",
    category: {
      id: "cat-1",
      name: "Steel & Metal",
      slug: "steel-metal",
    },
    images: [
      {
        id: "img-1",
        image_url: "/placeholder.svg?height=400&width=400&text=Steel+Rebar",
        alt_text: "Premium Steel Rebar",
        alt_text_ar: "حديد التسليح الممتاز",
        is_primary: true,
      },
    ],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
  },
]

const mockServices: Service[] = [
  {
    id: "1",
    name: "Concrete Pouring Service",
    name_ar: "خدمة صب الخرسانة",
    slug: "concrete-pouring-service",
    short_description: "Professional concrete pouring services for all construction needs",
    short_description_ar: "خدمات صب الخرسانة المهنية لجميع احتياجات البناء",
    description:
      "Our expert team provides comprehensive concrete pouring services using state-of-the-art equipment and high-quality materials. We ensure precise mixing, timely delivery, and professional installation for all types of construction projects.",
    description_ar: "يقدم فريقنا الخبير خدمات صب الخرسانة الشاملة باستخدام أحدث المعدات والمواد عالية الجودة.",
    base_price: 150,
    currency: "AED",
    unit: "m³",
    is_featured: true,
    is_emergency: false,
    view_count: 890,
    rating: 4.9,
    completion_count: 245,
    duration_estimate: "1-3 days",
    duration_estimate_ar: "1-3 أيام",
    service_area: "IRAQ",
    features: ["Professional team", "Quality materials", "Timely delivery", "Equipment included"],
    features_ar: ["فريق محترف", "مواد عالية الجودة", "تسليم في الوقت المحدد", "معدات مشمولة"],
    certifications: ["ISO 9001", "IRAQ Municipality Approved"],
    seo_title: "Professional Concrete Pouring Service | DRYLEX  IRAQ",
    seo_description:
      "Expert concrete pouring services in IRAQ. Professional team, quality materials, and timely delivery for all construction projects.",
    seo_keywords: "concrete pouring, construction services, IRAQ, professional contractors",
    category: {
      id: "cat-1",
      name: "Construction Services",
      slug: "construction-services",
    },
    images: [
      {
        id: "img-1",
        image_url: "/placeholder.svg?height=300&width=400&text=Concrete+Pouring",
        alt_text: "Concrete Pouring Service",
        alt_text_ar: "خدمة صب الخرسانة",
        is_primary: true,
      },
    ],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
  },
]

const mockProjects: Project[] = [
  {
    id: "1",
    name: "Dubai Marina Residential Tower",
    name_ar: "برج ذي قار مارينا السكني",
    slug: "dubai-marina-residential-tower",
    short_description: "Luxury 40-story residential tower in Dubai Marina",
    short_description_ar: "برج سكني فاخر من 40 طابقاً في ذي قار مارينا",
    description:
      "A prestigious 40-story residential tower featuring luxury apartments with stunning marina views. The project incorporates cutting-edge construction techniques and premium materials to deliver exceptional quality and durability.",
    description_ar: "برج سكني مرموق من 40 طابقاً يضم شققاً فاخرة مع إطلالات خلابة على المارينا.",
    project_status: "completed",
    is_featured: true,
    view_count: 2150,
    client_name: "Marina Development LLC",
    client_name_ar: "شركة تطوير المارينا المحدودة",
    location: "Dubai Marina, IRAQ",
    location_ar: "ذي قار مارينا، العراق",
    project_value: 85000000,
    currency: "AED",
    project_size: "45,000 m²",
    duration_months: 36,
    team_size: 150,
    start_date: "2021-01-15",
    completion_date: "2024-01-15",
    key_features: ["40 floors", "200 luxury units", "Marina views", "Premium finishes", "Smart home technology"],
    key_features_ar: ["40 طابقاً", "200 وحدة فاخرة", "إطلالات على المارينا", "تشطيبات ممتازة", "تقنية المنزل الذكي"],
    awards_received: ["Best Residential Project 2024", "Excellence in Construction"],
    technical_specs: {
      Height: "150 meters",
      Units: "200 apartments",
      Parking: "300 spaces",
      Amenities: "Pool, Gym, Spa",
    },
    seo_title: "Dubai Marina Residential Tower - Luxury Construction Project | DRYLEX  IRAQ",
    seo_description:
      "Completed luxury residential tower project in Dubai Marina. 40 stories of premium apartments with marina views.",
    seo_keywords: "Dubai Marina, residential tower, luxury apartments, construction project, IRAQ",
    category: {
      id: "cat-1",
      name: "Residential",
      slug: "residential",
    },
    images: [
      {
        id: "img-1",
        image_url: "/placeholder.svg?height=300&width=400&text=Dubai+Marina+Tower",
        alt_text: "Dubai Marina Residential Tower",
        alt_text_ar: "برج ذي قار مارينا السكني",
        is_primary: true,
      },
    ],
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
  },
]

// Database functions
export async function getProductBySlug(slug: string): Promise<Product | null> {
  // Simulate database delay
  await new Promise((resolve) => setTimeout(resolve, 100))

  const product = mockProducts.find((p) => p.slug === slug)
  return product || null
}

export async function getServiceBySlug(slug: string): Promise<Service | null> {
  await new Promise((resolve) => setTimeout(resolve, 100))

  const service = mockServices.find((s) => s.slug === slug)
  return service || null
}

export async function getProjectBySlug(slug: string): Promise<Project | null> {
  await new Promise((resolve) => setTimeout(resolve, 100))

  const project = mockProjects.find((p) => p.slug === slug)
  return project || null
}

export async function getAllProductSlugs(): Promise<{ slug: string }[]> {
  await new Promise((resolve) => setTimeout(resolve, 50))

  return mockProducts.map((p) => ({ slug: p.slug }))
}

export async function getAllServiceSlugs(): Promise<{ slug: string }[]> {
  await new Promise((resolve) => setTimeout(resolve, 50))

  return mockServices.map((s) => ({ slug: s.slug }))
}

export async function getAllProjectSlugs(): Promise<{ slug: string }[]> {
  await new Promise((resolve) => setTimeout(resolve, 50))

  return mockProjects.map((p) => ({ slug: p.slug }))
}

export async function incrementViewCount(type: "products" | "services" | "projects", id: string): Promise<void> {
  // Simulate database update
  await new Promise((resolve) => setTimeout(resolve, 50))

  // In a real implementation, this would update the database
  console.log(`Incrementing view count for ${type} with id: ${id}`)
}

export async function getProducts(params?: {
  category?: string
  featured?: boolean
  limit?: number
  offset?: number
}): Promise<Product[]> {
  await new Promise((resolve) => setTimeout(resolve, 100))

  let filtered = [...mockProducts]

  if (params?.category) {
    filtered = filtered.filter((p) => p.category?.slug === params.category)
  }

  if (params?.featured) {
    filtered = filtered.filter((p) => p.is_featured)
  }

  if (params?.offset) {
    filtered = filtered.slice(params.offset)
  }

  if (params?.limit) {
    filtered = filtered.slice(0, params.limit)
  }

  return filtered
}

export async function getServices(params?: {
  category?: string
  featured?: boolean
  limit?: number
  offset?: number
}): Promise<Service[]> {
  await new Promise((resolve) => setTimeout(resolve, 100))

  let filtered = [...mockServices]

  if (params?.category) {
    filtered = filtered.filter((s) => s.category?.slug === params.category)
  }

  if (params?.featured) {
    filtered = filtered.filter((s) => s.is_featured)
  }

  if (params?.offset) {
    filtered = filtered.slice(params.offset)
  }

  if (params?.limit) {
    filtered = filtered.slice(0, params.limit)
  }

  return filtered
}

export async function getProjects(params?: {
  category?: string
  featured?: boolean
  status?: string
  limit?: number
  offset?: number
}): Promise<Project[]> {
  await new Promise((resolve) => setTimeout(resolve, 100))

  let filtered = [...mockProjects]

  if (params?.category) {
    filtered = filtered.filter((p) => p.category?.slug === params.category)
  }

  if (params?.featured) {
    filtered = filtered.filter((p) => p.is_featured)
  }

  if (params?.status) {
    filtered = filtered.filter((p) => p.project_status === params.status)
  }

  if (params?.offset) {
    filtered = filtered.slice(params.offset)
  }

  if (params?.limit) {
    filtered = filtered.slice(0, params.limit)
  }

  return filtered
}

// Remove this entire block (lines 436-502 approximately):
// export const enhancedDb = {
//   // Product functions
//   getProductById: async (id: number): Promise<Product | null> => {
//     await new Promise((resolve) => setTimeout(resolve, 100))
//     const product = mockProducts.find((p) => p.id === id.toString())
//     return product || null
//   },
//   
//   getProductBySlug,
//   getAllProductSlugs,
//   getProducts,
//   
//   // Service functions
//   getServiceBySlug,
//   getAllServiceSlugs,
//   getServices,
//   incrementServiceViews: async (id: string): Promise<void> => {
//     await incrementViewCount('services', id)
//   },
//   getRelatedServices: async (serviceId: string, categoryId?: string, limit: number = 3): Promise<Service[]> => {
//     await new Promise((resolve) => setTimeout(resolve, 100))
//     return mockServices.filter(s => s.id !== serviceId && (!categoryId || s.category?.id === categoryId)).slice(0, limit)
//   },
//   
//   // Project functions
//   getProjectBySlug,
//   getAllProjectSlugs,
//   getProjects,
//   incrementProjectViews: async (id: string): Promise<void> => {
//     await incrementViewCount('projects', id)
//   },
//   getRelatedProjects: async (projectId: string, categoryId?: string, limit: number = 3): Promise<Project[]> => {
//     await new Promise((resolve) => setTimeout(resolve, 100))
//     return mockProjects.filter(p => p.id !== projectId && (!categoryId || p.category?.id === categoryId)).slice(0, limit)
//   },
//   
//   // Inquiry functions
//   createProductInquiry: async (inquiry: any): Promise<{ id: string }> => {
//     await new Promise((resolve) => setTimeout(resolve, 100))
//     return { id: `inquiry_${Date.now()}` }
//   },
//   incrementProductInquiries: async (productId: number): Promise<void> => {
//     await new Promise((resolve) => setTimeout(resolve, 50))
//     console.log(`Incrementing inquiries for product ${productId}`)
//   },
//   getProductInquiries: async (productId: number): Promise<any[]> => {
//     await new Promise((resolve) => setTimeout(resolve, 100))
//     return []
//   },
//   
//   // Wishlist functions
//   addToWishlist: async (sessionId: string, productId: number): Promise<void> => {
//     await new Promise((resolve) => setTimeout(resolve, 50))
//     console.log(`Adding product ${productId} to wishlist for session ${sessionId}`)
//   },
//   removeFromWishlist: async (sessionId: string, productId: number): Promise<void> => {
//     await new Promise((resolve) => setTimeout(resolve, 50))
//     console.log(`Removing product ${productId} from wishlist for session ${sessionId}`)
//   },
//   isInWishlist: async (sessionId: string, productId: number): Promise<boolean> => {
//     await new Promise((resolve) => setTimeout(resolve, 50))
//     return false
//   },
// }

// Keep only the second enhancedDb export at the end of the file
export const enhancedDb = {
  // Product functions
  getProductById: async (id: number) => {
    const product = mockProducts.find(p => p.id === id)
    return product || null
  },
  getProductBySlug,
  getAllProductSlugs,
  getProducts,
  
  // Service functions
  getServiceBySlug,
  getAllServiceSlugs,
  getServices,
  incrementServiceViews: async (slug: string) => {
    await incrementViewCount('services', slug)  // Fix: use 'slug' instead of 'id'
  },
  getRelatedServices: async (serviceId: string, categoryId?: string, limit: number = 3): Promise<Service[]> => {
    await new Promise((resolve) => setTimeout(resolve, 100))
    return mockServices.filter(s => s.id !== serviceId && (!categoryId || s.category?.id === categoryId)).slice(0, limit)
  },
  
  // Project functions
  getProjectBySlug,
  getAllProjectSlugs,
  getProjects,
  incrementProjectViews: async (slug: string) => {
    await incrementViewCount('projects', slug)  // Fix: use 'slug' instead of 'id'
  },
  getRelatedProjects: async (projectId: string, categoryId?: string, limit: number = 3): Promise<Project[]> => {
    await new Promise((resolve) => setTimeout(resolve, 100))
    return mockProjects.filter(p => p.id !== projectId && (!categoryId || p.category?.id === categoryId)).slice(0, limit)
  },
  
  // Inquiry functions
  createProductInquiry: async (inquiry: any) => {
    console.log('Creating product inquiry:', inquiry)
    return { success: true, id: Date.now() }
  },
  incrementProductInquiries: async (productId: number) => {
    console.log(`Incrementing inquiries for product: ${productId}`)
  },
  
  // Wishlist functions
  addToWishlist: async (sessionId: string, productId: number) => {
    console.log(`Adding product ${productId} to wishlist for session ${sessionId}`)
  },
  removeFromWishlist: async (sessionId: string, productId: number) => {
    console.log(`Removing product ${productId} from wishlist for session ${sessionId}`)
  },
  isInWishlist: async (sessionId: string, productId: number) => {
    return false
  }
}
