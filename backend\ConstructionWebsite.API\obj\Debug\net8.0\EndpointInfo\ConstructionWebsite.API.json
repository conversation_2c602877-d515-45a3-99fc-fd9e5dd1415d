{"openapi": "3.0.1", "info": {"title": "Construction Website API", "description": "API for Construction Materials & Services Website", "contact": {"name": "DRYLEX  Support", "email": "support@DRYLEX .ae"}, "version": "v1"}, "paths": {"/api/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordModel"}}}}, "responses": {"200": {"description": "Success"}}}}, "/api/Auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Success"}}}}, "/api/Products": {"get": {"tags": ["Products"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "isFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "schema": {"type": "string", "default": "desc"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDtoPagedResult"}}}}}}, "post": {"tags": ["Products"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}}, "/api/Products/slug/{slug}": {"get": {"tags": ["Products"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}}}}}}, "/api/Products/{id}": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDetailDto"}}}}}}, "put": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProductDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProductDto"}}}}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Products/{id}/related": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 4}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}}}}}}}}, "/api/Products/{id}/inquiries": {"post": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProductInquiryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProductInquiryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProductInquiryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}}}}}}, "/api/Products/{id}/wishlist": {"post": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}, "delete": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Products/{id}/wishlist/status": {"get": {"tags": ["Products"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WishlistStatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WishlistStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WishlistStatusDto"}}}}}}}, "/api/Products/categories": {"get": {"tags": ["Products"], "parameters": [{"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}}}}}, "/api/Projects": {"get": {"tags": ["Projects"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "isFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "schema": {"type": "string", "default": "desc"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDtoPagedResult"}}}}}}, "post": {"tags": ["Projects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProjectDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}}}}, "/api/Projects/slug/{slug}": {"get": {"tags": ["Projects"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}}}}}}, "/api/Projects/{id}": {"get": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDetailDto"}}}}}}, "put": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateProjectDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProjectDto"}}}}}}, "delete": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Projects/{id}/related": {"get": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 3}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDto"}}}}}}}}, "/api/Projects/{id}/inquiries": {"post": {"tags": ["Projects"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProjectInquiryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateProjectInquiryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateProjectInquiryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}}}}}}, "/api/Projects/categories": {"get": {"tags": ["Projects"], "parameters": [{"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}}}}}, "/api/Services": {"get": {"tags": ["Services"], "parameters": [{"name": "search", "in": "query", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "status", "in": "query", "schema": {"type": "string"}}, {"name": "isFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}}, {"name": "sortBy", "in": "query", "schema": {"type": "string", "default": "createdAt"}}, {"name": "sortOrder", "in": "query", "schema": {"type": "string", "default": "desc"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceDtoPagedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDtoPagedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDtoPagedResult"}}}}}}, "post": {"tags": ["Services"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateServiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateServiceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateServiceDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}}}}}}, "/api/Services/slug/{slug}": {"get": {"tags": ["Services"], "parameters": [{"name": "slug", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}}}}}}, "/api/Services/{id}": {"get": {"tags": ["Services"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDetailDto"}}}}}}, "put": {"tags": ["Services"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateServiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateServiceDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateServiceDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ServiceDto"}}}}}}, "delete": {"tags": ["Services"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}}}}, "/api/Services/{id}/related": {"get": {"tags": ["Services"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}, {"name": "limit", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 3}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceDto"}}}}}}}}, "/api/Services/{id}/inquiries": {"post": {"tags": ["Services"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateServiceInquiryDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateServiceInquiryDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateServiceInquiryDto"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InquiryResponseDto"}}}}}}}, "/api/Services/categories": {"get": {"tags": ["Services"], "parameters": [{"name": "language", "in": "query", "schema": {"type": "string", "default": "en"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}}}}}}}}}, "components": {"schemas": {"CategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "parentId": {"type": "integer", "format": "int32", "nullable": true}, "parent": {"$ref": "#/components/schemas/CategoryDto"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryDto"}, "nullable": true}, "productCount": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ChangePasswordModel": {"type": "object", "properties": {"currentPassword": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProductDto": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32", "nullable": true}, "sku": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "minOrderQty": {"type": "integer", "format": "int32"}, "unit": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "brand": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isBestseller": {"type": "boolean"}, "isNew": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateProductInquiryDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "inquiryType": {"type": "string", "nullable": true}, "preferredContactMethod": {"type": "string", "nullable": true}, "preferredContactTime": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32", "nullable": true}, "projectDetails": {"type": "string", "nullable": true}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "budget": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProjectDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "titleAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "client": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "locationAr": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "budget": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionDate": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "areaUnit": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "challenges": {"type": "array", "items": {"type": "string"}, "nullable": true}, "solutions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "technologies": {"type": "array", "items": {"type": "string"}, "nullable": true}, "materials": {"type": "array", "items": {"type": "string"}, "nullable": true}, "teamMembers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "clientTestimonial": {"type": "string", "nullable": true}, "clientTestimonialAr": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "virtualTourUrl": {"type": "string", "nullable": true}, "projectStats": {"type": "object", "additionalProperties": {}, "nullable": true}, "awards": {"type": "array", "items": {"type": "string"}, "nullable": true}, "caseStudyUrl": {"type": "string", "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateProjectInquiryDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "inquiryType": {"type": "string", "nullable": true}, "preferredContactMethod": {"type": "string", "nullable": true}, "preferredContactTime": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "projectDetails": {"type": "string", "nullable": true}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "budget": {"type": "string", "nullable": true}, "timeline": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "projectArea": {"type": "number", "format": "double", "nullable": true}, "areaUnit": {"type": "string", "nullable": true}, "constructionType": {"type": "string", "nullable": true}, "requiredServices": {"type": "array", "items": {"type": "string"}, "nullable": true}, "source": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateServiceDto": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "priceType": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "benefits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "process": {"type": "array", "items": {"type": "string"}, "nullable": true}, "requirements": {"type": "array", "items": {"type": "string"}, "nullable": true}, "deliverables": {"type": "array", "items": {"type": "string"}, "nullable": true}, "portfolio": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "brochureUrl": {"type": "string", "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateServiceInquiryDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "inquiryType": {"type": "string", "nullable": true}, "preferredContactMethod": {"type": "string", "nullable": true}, "preferredContactTime": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "projectDetails": {"type": "string", "nullable": true}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "budget": {"type": "string", "nullable": true}, "timeline": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ImageDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "fileName": {"type": "string", "nullable": true}, "originalFileName": {"type": "string", "nullable": true}, "filePath": {"type": "string", "nullable": true}, "fileUrl": {"type": "string", "nullable": true}, "altText": {"type": "string", "nullable": true}, "altTextAr": {"type": "string", "nullable": true}, "caption": {"type": "string", "nullable": true}, "captionAr": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int64"}, "mimeType": {"type": "string", "nullable": true}, "width": {"type": "integer", "format": "int32", "nullable": true}, "height": {"type": "integer", "format": "int32", "nullable": true}, "isPrimary": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "entityType": {"type": "string", "nullable": true}, "entityId": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "InquiryResponseDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "company": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "inquiryType": {"type": "string", "nullable": true}, "preferredContactMethod": {"type": "string", "nullable": true}, "preferredContactTime": {"type": "string", "nullable": true}, "quantity": {"type": "integer", "format": "int32", "nullable": true}, "projectDetails": {"type": "string", "nullable": true}, "requiredDate": {"type": "string", "format": "date-time", "nullable": true}, "budget": {"type": "string", "nullable": true}, "source": {"type": "string", "nullable": true}, "productId": {"type": "integer", "format": "int32"}, "productName": {"type": "string", "nullable": true}, "isRead": {"type": "boolean"}, "isReplied": {"type": "boolean"}, "reply": {"type": "string", "nullable": true}, "repliedAt": {"type": "string", "format": "date-time", "nullable": true}, "repliedBy": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "ipAddress": {"type": "string", "nullable": true}, "userAgent": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "LoginModel": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "rememberMe": {"type": "boolean"}}, "additionalProperties": false}, "ProductDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isBestseller": {"type": "boolean"}, "isNew": {"type": "boolean"}, "viewCount": {"type": "integer", "format": "int32"}, "inquiryCount": {"type": "integer", "format": "int32"}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "primaryImage": {"$ref": "#/components/schemas/ImageDto"}, "createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "technicalSpecs": {"type": "object", "additionalProperties": {}, "nullable": true}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "applications": {"type": "array", "items": {"type": "string"}, "nullable": true}, "benefits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "usageInstructions": {"type": "string", "nullable": true}, "safetyInfo": {"type": "string", "nullable": true}, "storageConditions": {"type": "string", "nullable": true}, "packagingInfo": {"type": "object", "additionalProperties": {}, "nullable": true}, "coverageArea": {"type": "string", "nullable": true}, "shelfLife": {"type": "string", "nullable": true}, "certifications": {"type": "array", "items": {"type": "string"}, "nullable": true}, "minOrderQty": {"type": "integer", "format": "int32"}, "weight": {"type": "number", "format": "double", "nullable": true}, "dimensions": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "originCountry": {"type": "string", "nullable": true}, "barcode": {"type": "string", "nullable": true}, "qrCode": {"type": "string", "nullable": true}, "pdfDatasheet": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}, "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ProductDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "unit": {"type": "string", "nullable": true}, "brand": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isBestseller": {"type": "boolean"}, "isNew": {"type": "boolean"}, "viewCount": {"type": "integer", "format": "int32"}, "inquiryCount": {"type": "integer", "format": "int32"}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "primaryImage": {"$ref": "#/components/schemas/ImageDto"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProductDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProductDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}, "additionalProperties": false}, "ProjectDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "titleAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "client": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "locationAr": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "budget": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionDate": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "areaUnit": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "primaryImage": {"$ref": "#/components/schemas/ImageDto"}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "challenges": {"type": "array", "items": {"type": "string"}, "nullable": true}, "solutions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "technologies": {"type": "array", "items": {"type": "string"}, "nullable": true}, "materials": {"type": "array", "items": {"type": "string"}, "nullable": true}, "teamMembers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "clientTestimonial": {"type": "string", "nullable": true}, "clientTestimonialAr": {"type": "string", "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}, "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "virtualTourUrl": {"type": "string", "nullable": true}, "projectStats": {"type": "object", "additionalProperties": {}, "nullable": true}, "awards": {"type": "array", "items": {"type": "string"}, "nullable": true}, "caseStudyUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "titleAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "client": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "locationAr": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "budget": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionDate": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "areaUnit": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "primaryImage": {"$ref": "#/components/schemas/ImageDto"}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ProjectDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}, "additionalProperties": false}, "RegisterModel": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordModel": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}, "token": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "priceType": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "inquiryCount": {"type": "integer", "format": "int32"}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "benefits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "process": {"type": "array", "items": {"type": "string"}, "nullable": true}, "requirements": {"type": "array", "items": {"type": "string"}, "nullable": true}, "deliverables": {"type": "array", "items": {"type": "string"}, "nullable": true}, "portfolio": {"type": "string", "nullable": true}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/ImageDto"}, "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "brochureUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ServiceDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "priceType": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "isActive": {"type": "boolean"}, "sortOrder": {"type": "integer", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}, "inquiryCount": {"type": "integer", "format": "int32"}, "category": {"$ref": "#/components/schemas/CategoryDto"}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "benefits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ServiceDtoPagedResult": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/ServiceDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}, "additionalProperties": false}, "UpdateProductDto": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32", "nullable": true}, "sku": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "minOrderQty": {"type": "integer", "format": "int32", "nullable": true}, "unit": {"type": "string", "nullable": true}, "weight": {"type": "number", "format": "double", "nullable": true}, "brand": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean", "nullable": true}, "isBestseller": {"type": "boolean", "nullable": true}, "isNew": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "UpdateProjectDto": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "titleAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "client": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "locationAr": {"type": "string", "nullable": true}, "projectType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "budget": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "completionDate": {"type": "string", "format": "date-time", "nullable": true}, "duration": {"type": "string", "nullable": true}, "area": {"type": "number", "format": "double", "nullable": true}, "areaUnit": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "challenges": {"type": "array", "items": {"type": "string"}, "nullable": true}, "solutions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "technologies": {"type": "array", "items": {"type": "string"}, "nullable": true}, "materials": {"type": "array", "items": {"type": "string"}, "nullable": true}, "teamMembers": {"type": "array", "items": {"type": "string"}, "nullable": true}, "clientTestimonial": {"type": "string", "nullable": true}, "clientTestimonialAr": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "virtualTourUrl": {"type": "string", "nullable": true}, "projectStats": {"type": "object", "additionalProperties": {}, "nullable": true}, "awards": {"type": "array", "items": {"type": "string"}, "nullable": true}, "caseStudyUrl": {"type": "string", "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateServiceDto": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32", "nullable": true}, "name": {"type": "string", "nullable": true}, "nameAr": {"type": "string", "nullable": true}, "slug": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "shortDescriptionAr": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "descriptionAr": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "image": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double", "nullable": true}, "currency": {"type": "string", "nullable": true}, "priceType": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "sortOrder": {"type": "integer", "format": "int32", "nullable": true}, "features": {"type": "array", "items": {"type": "string"}, "nullable": true}, "benefits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "process": {"type": "array", "items": {"type": "string"}, "nullable": true}, "requirements": {"type": "array", "items": {"type": "string"}, "nullable": true}, "deliverables": {"type": "array", "items": {"type": "string"}, "nullable": true}, "portfolio": {"type": "string", "nullable": true}, "videoUrl": {"type": "string", "nullable": true}, "brochureUrl": {"type": "string", "nullable": true}, "seoTitle": {"type": "string", "nullable": true}, "seoDescription": {"type": "string", "nullable": true}, "seoKeywords": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WishlistStatusDto": {"type": "object", "properties": {"isInWishlist": {"type": "boolean"}, "wishlistCount": {"type": "integer", "format": "int32"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bear<PERSON> scheme", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}