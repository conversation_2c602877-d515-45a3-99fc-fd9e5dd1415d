# Construction Website Backend API

This is the backend API for the Construction Materials & Services website built with ASP.NET Core 8.0.

## Prerequisites

- **Visual Studio 2022** (Community, Professional, or Enterprise)
- **.NET 8.0 SDK**
- **SQL Server** (LocalDB, Express, or Full version)
- **Redis** (optional, for caching)

## Getting Started

### 1. Open the Solution

1. Open Visual Studio 2022
2. Click **"Open a project or solution"**
3. Navigate to `backend/ConstructionWebsite.sln`
4. Click **Open**

### 2. Database Setup

#### Option A: Using Entity Framework Migrations (Recommended)

1. Open **Package Manager Console** in Visual Studio:
   - Tools → NuGet Package Manager → Package Manager Console

2. Run the following commands:
   ```powershell
   # Create initial migration (if not exists)
   Add-Migration InitialCreate
   
   # Update database
   Update-Database
   ```

#### Option B: Using SQL Script

1. Open **SQL Server Management Studio** or **Visual Studio SQL Server Object Explorer**
2. Connect to your SQL Server instance
3. Open and execute `Database/CreateDatabase.sql`

### 3. Configuration

The application uses the following configuration files:

- `appsettings.json` - Production settings
- `appsettings.Development.json` - Development settings

#### Connection Strings

**Default (LocalDB):**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=ConstructionWebsiteDB;Trusted_Connection=true;MultipleActiveResultSets=true",
    "Redis": "localhost:6379"
  }
}
```

**SQL Server Express:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=.\\SQLEXPRESS;Database=ConstructionWebsiteDB;Trusted_Connection=true;MultipleActiveResultSets=true"
  }
}
```

**SQL Server with Authentication:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-server;Database=ConstructionWebsiteDB;User Id=your-username;Password=your-password;MultipleActiveResultSets=true"
  }
}
```

### 4. Running the Application

1. Set `ConstructionWebsite.API` as the startup project
2. Press **F5** or click the **Start** button
3. The API will launch with Swagger UI at `https://localhost:7xxx/`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/refresh` - Refresh JWT token

### Products
- `GET /api/products` - Get all products
- `GET /api/products/{id}` - Get product by ID
- `GET /api/products/slug/{slug}` - Get product by slug
- `POST /api/products` - Create product (Admin)
- `PUT /api/products/{id}` - Update product (Admin)
- `DELETE /api/products/{id}` - Delete product (Admin)

### Categories
- `GET /api/categories` - Get all categories
- `GET /api/categories/{id}` - Get category by ID
- `POST /api/categories` - Create category (Admin)
- `PUT /api/categories/{id}` - Update category (Admin)

### Services
- `GET /api/services` - Get all services
- `GET /api/services/{id}` - Get service by ID
- `GET /api/services/slug/{slug}` - Get service by slug

### Projects
- `GET /api/projects` - Get all projects
- `GET /api/projects/{id}` - Get project by ID
- `GET /api/projects/slug/{slug}` - Get project by slug

### Contact
- `POST /api/contact` - Submit contact form
- `GET /api/contact/messages` - Get contact messages (Admin)

### Health Check
- `GET /health` - Application health status

## Features

- **JWT Authentication** - Secure API access
- **Role-based Authorization** - Admin, Manager roles
- **Entity Framework Core** - Database ORM
- **Swagger Documentation** - Interactive API docs
- **CORS Support** - Frontend integration
- **Caching** - Redis integration
- **File Upload** - Image and document handling
- **Email Service** - Contact form notifications
- **Health Checks** - Application monitoring
- **Localization Support** - English/Arabic content

## Database Schema

### Main Tables
- **AspNetUsers** - User accounts (Identity)
- **AspNetRoles** - User roles
- **Categories** - Product categories
- **Products** - Product catalog
- **Services** - Service offerings
- **Projects** - Portfolio projects
- **ContactMessages** - Contact form submissions
- **NewsletterSubscriptions** - Email subscriptions

## Development

### Adding New Migrations

```powershell
# Add new migration
Add-Migration MigrationName

# Update database
Update-Database
```

### Seeding Data

The application automatically seeds initial data on startup:
- Default admin user
- Sample categories
- Sample services

### Environment Variables

For production deployment, set these environment variables:
- `ConnectionStrings__DefaultConnection`
- `JwtSettings__SecretKey`
- `EmailSettings__SenderEmail`
- `EmailSettings__SenderPassword`

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify SQL Server is running
   - Check connection string
   - Ensure database exists

2. **Migration Errors**
   - Delete `Migrations` folder
   - Run `Add-Migration InitialCreate`
   - Run `Update-Database`

3. **JWT Token Issues**
   - Verify `JwtSettings__SecretKey` is at least 32 characters
   - Check token expiration settings

4. **CORS Errors**
   - Update allowed origins in `Program.cs`
   - Verify frontend URL is included

### Logs

Check the console output or configure file logging for detailed error information.

## Production Deployment

1. Update connection strings
2. Set environment to "Production"
3. Configure HTTPS certificates
4. Set up Redis for caching
5. Configure email settings
6. Deploy to IIS or Azure App Service

## Support

For technical support or questions:
- Email: support@DRYLEX .ae
- Documentation: Check Swagger UI when running