import { type NextRequest, NextResponse } from "next/server"
import { enhancedDb } from "@/lib/database-enhanced"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const productId = Number.parseInt(params.id)
    const body = await request.json()

    // Validate required fields
    const { name, email, phone, message, company } = body
    if (!name || !email || !message) {
      return NextResponse.json({ error: "Name, email, and message are required" }, { status: 400 })
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: "Invalid email format" }, { status: 400 })
    }

    // Get product details
    const product = await enhancedDb.getProductById(productId)
    if (!product) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    // Create inquiry record
    const inquiry = await enhancedDb.createProductInquiry({
      product_id: productId,
      name,
      email,
      phone,
      company,
      message,
      source: "product_detail_page",
      ip_address: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
      user_agent: request.headers.get("user-agent") || "unknown",
    })

    // Increment inquiry count
    await enhancedDb.incrementProductInquiries(productId)

    // Send notification email (implement based on your email service)
    // await sendInquiryNotification(inquiry, product)

    return NextResponse.json({
      success: true,
      message: "Inquiry submitted successfully",
      inquiry_id: inquiry.id,
    })
  } catch (error) {
    console.error("Error creating product inquiry:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const productId = Number.parseInt(params.id)
    const inquiries = await enhancedDb.getProductInquiries(productId)

    return NextResponse.json({
      success: true,
      inquiries,
    })
  } catch (error) {
    console.error("Error fetching product inquiries:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
