"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, MapPin, Calendar, DollarSign, Building } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useTranslation } from "@/components/translation-provider"
import { AnimatedCard } from "@/components/animated-card"
import type { Project } from "@/lib/database-enhanced"

interface ProjectCardProps {
  project: Project
  index?: number
}

export function ProjectCard({ project, index = 0 }: ProjectCardProps) {
  const { t, language } = useTranslation()

  const displayName = language === "ar" ? project.name_ar || project.name : project.name
  const displayDescription =
    language === "ar" ? project.short_description_ar || project.short_description : project.short_description
  const displayLocation = language === "ar" ? project.location_ar || project.location : project.location
  const displayClientName = language === "ar" ? project.client_name_ar || project.client_name : project.client_name

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-600"
      case "ongoing":
        return "bg-blue-600"
      case "planning":
        return "bg-yellow-600"
      default:
        return "bg-gray-600"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return t("status.completed", "Completed")
      case "ongoing":
        return t("status.ongoing", "Ongoing")
      case "planning":
        return t("status.planning", "Planning")
      default:
        return status
    }
  }

  return (
    <AnimatedCard delay={index * 0.1} className="h-full">
      <Link href={`/projects/${project.slug}`} className="block h-full" prefetch={true} scroll={true}>
        <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer h-full flex flex-col">
          {/* Project Image */}
          <div className="relative aspect-video overflow-hidden rounded-t-lg">
            <Image
              src={project.images?.[0]?.image_url || "/placeholder.svg?height=200&width=300"}
              alt={
                language === "ar"
                  ? project.images?.[0]?.alt_text_ar || project.images?.[0]?.alt_text || displayName
                  : project.images?.[0]?.alt_text || displayName
              }
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              loading="lazy"
              priority={index < 4}
            />

            {/* Status Badge */}
            <div className="absolute top-3 left-3">
              <Badge className={`${getStatusColor(project.project_status || "")} text-white text-xs`}>
                {getStatusText(project.project_status || "")}
              </Badge>
            </div>

            {/* Featured Badge */}
            {project.is_featured && (
              <div className="absolute top-3 right-3">
                <Badge className="bg-orange-600 text-white text-xs">{t("badge.featured", "Featured")}</Badge>
              </div>
            )}

            {/* Project Value */}
            {project.project_value && (
              <div className="absolute bottom-3 left-3">
                <Badge variant="secondary" className="bg-white/90 text-gray-800 font-semibold">
                  {project.currency} {(project.project_value / 1000000).toFixed(1)}M
                </Badge>
              </div>
            )}
          </div>

          {/* Project Info */}
          <CardContent className="p-4 flex-1 flex flex-col">
            <div className="flex-1">
              <h3 className="font-semibold text-lg mb-2 line-clamp-2 group-hover:text-orange-600 transition-colors">
                {displayName}
              </h3>

              <p className="text-sm text-gray-600 mb-3 line-clamp-2">{displayDescription}</p>

              {/* Project Details */}
              <div className="space-y-2 mb-3">
                {displayClientName && (
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <Building className="h-3 w-3 flex-shrink-0" />
                    <span className="line-clamp-1">{displayClientName}</span>
                  </div>
                )}

                {displayLocation && (
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <MapPin className="h-3 w-3 flex-shrink-0" />
                    <span className="line-clamp-1">{displayLocation}</span>
                  </div>
                )}

                {project.completion_date && (
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <Calendar className="h-3 w-3 flex-shrink-0" />
                    <span>{new Date(project.completion_date).getFullYear()}</span>
                  </div>
                )}

                {project.project_size && (
                  <div className="flex items-center gap-2 text-xs text-gray-600">
                    <DollarSign className="h-3 w-3 flex-shrink-0" />
                    <span>{project.project_size}</span>
                  </div>
                )}
              </div>

              {/* Key Features */}
              {project.key_features && project.key_features.length > 0 && (
                <div className="mb-3">
                  <ul className="text-xs text-gray-600 space-y-1">
                    {(language === "ar" ? project.key_features_ar || project.key_features : project.key_features)
                      .slice(0, 2)
                      .map((feature, idx) => (
                        <li key={idx} className="flex items-center gap-1">
                          <span className="w-1 h-1 bg-orange-600 rounded-full flex-shrink-0"></span>
                          <span className="line-clamp-1">{feature}</span>
                        </li>
                      ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="pt-3 border-t border-gray-100">
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <div className="flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  <span>
                    {project.view_count || 0} {t("views", "views")}
                  </span>
                </div>
                {project.duration_months && (
                  <span className="text-orange-600 font-medium">
                    {project.duration_months} {t("months", "months")}
                  </span>
                )}
              </div>

              <Button className="w-full bg-orange-600 hover:bg-orange-700 text-white" size="sm">
                {t("btn.view_details", "View Details")}
              </Button>
            </div>
          </CardContent>
        </Card>
      </Link>
    </AnimatedCard>
  )
}
